<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>order-push-service</artifactId>
        <groupId>com.wanshifu</groupId>
        <version>1.0.48-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>order-push-service-api</artifactId>
    <version>1.0.48-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-core</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>