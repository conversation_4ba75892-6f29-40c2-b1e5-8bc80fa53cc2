package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
public class InfoOrderBaseDTO implements Serializable {
    /**
     * PK
     */
    private Long id;

    /**
     * 订单id(guid生成)
     */
    private Long orderId;

    /**
     * 订单全局ID
     */
    private Long globalOrderTraceId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 下单人账号ID
     */
    private Long accountId;

    /**
     * 下单子账号ID
     */
    private Long subAccountId;

    /**
     * 下单地区ID（三级地址）
     */
    private Long thirdDivisionId;

    /**
     * 下单地区ID（四级地址）
     */
    private Long fourthDivisionId;

    /**
     * 下单人账号类型
     */
    private String accountType;

    /**
     * 订单类目id
     */
    private Integer categoryId;

    /**
     * 下单服务类型id(config_order_server_type.id)
     */
    private String serveTypeIds;

    /**
     * 下单商品与下单服务类型关联表id(goods_server_type_rela.id)
     */
    private String goodsServerTypeRelaIds;

    /**
     * 订单状态(trading-交易进行中,close-订单关闭,finish-交易完成)
     */
    private String orderStatus;

    /**
     * 服务集合(多个逗号隔开)
     */
    private String serveIds;

    /**
     * 服务一级服务id集合
     */
    private String serveLevel1Ids;


    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 下单服务绑定的技能快照ID集合(1,3|1,4|2,3|2,4)
     */
    private String bindingTechnologyIds;


    /**
     * 订单下单时间
     */
    private Date orderCreateTime;


    /**
     * 风控检测状态(wait_check:待检测,check_normal:检测正常,check_abnormal:检测异常)
     */
    private String riskControlStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}