package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 22:48
 */
@Data
public class OrderGrabDTO {

    /**
     * 订单指派记录ID
     */
    private Long grabId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 城市ID
     */
    private Long secondDivisionId;

    /**
     * 所在区id
     */
    private Integer areaId;

    /**
     * 所在区id
     */
    private Long thirdDivisionId;


    /**
     * 街道id
     */
    private Long fourthDivisionId;


    /**
     * 报价截止时间
     */
    private Date endDate;

    /**
     * 报价数量
     */
    private Integer offerNumber;

    /**
     * 雇佣师傅ID
     */
    private Long hireMasterId;

    /**
     * 雇佣时间
     */
    private Date hireTime;

    /**
     * 确认雇佣状态(1:已确认;0:未确认)
     */
    private Integer confirmServeStatus;

    /**
     * 指派类型(2-发布任务，3-直接雇佣,4-一口价,5-预付款)
     */
    private Integer appointType;

    /**
     * 流程版本 0 默认版本  1：版本2
     */
    private Integer processVersion;

    /**
     * 要求(0-无 1-实名认证 2-签约)
     */
    private Integer requireCondition;

    /**
     * 下单方账号类型(user-用户,enterprise-总包)
     */
    private String accountType;

    /**
     * 下单方账号ID
     */
    private Long accountId;

    /**
     * 删除状态(1:删除0:未删除)
     */
    private Integer isDelete;

    /**
     * 订单支付状态,0:默认,1:一口价前置支付
     */
    private Integer orderPayStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
