package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/5/21 11:41
 */
@Data
public class OrderAddItemServiceInfoDTO {

    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单全局id
     */
    private Long globalOrderTraceId;

    /**
     * 增项服务ID
     */
    private Long itemId;

    /**
     * 增项服务类型，urgent_fee：加急费，night_fee：夜间费
     */
    private String itemType;

    /**
     * 增项服务状态  apply:申请,success:成功,fail:失败,cancel:取消
     */
    private String status;


    /**
     * 失败类型  refund_all：全额退款  sign_abnormal 签到异常 userCancel 用户取消
     */
    private String failType;

    /**
     * 条件执行参数json
     */
    private String conditionJsonParam;

    /**
     * 初始增项服务费用
     */
    private BigDecimal initItemFee;

    /**
     * 追加增项服务费用
     */
    private BigDecimal extraItemFee;

    /**
     * 总增项服务费用
     */
    private BigDecimal totalItemFee;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否删除（0否，1是）
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
