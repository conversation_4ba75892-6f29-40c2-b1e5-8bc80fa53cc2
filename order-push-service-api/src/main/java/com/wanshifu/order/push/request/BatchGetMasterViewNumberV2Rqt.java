package com.wanshifu.order.push.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @description 批量获取订单已查看师傅数量
 * @date 2024/7/1 18:00
 */
@Data
public class BatchGetMasterViewNumberV2Rqt {


    /**
     * 下单人账号ID
     */
    @NotNull
    private Long accountId;

    /**
     * 下单人账号类型
     */
    @NotEmpty
    private String accountType;

    /**
     * 订单信息
     */
    @NotEmpty
    @Size(min = 1, max = 20)
    private List<OrderInfo> orderInfos;


    @Data
    @Validated
    public static class OrderInfo {

        /**
         * 全局订单id
         */
        @NotNull
        @Min(value = 1L)
        private Long globalOrderTraceId;

        /**
         * 推单数据省下级地址id
         * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
         *
         */
        @Min(value = 1L)
        @NotNull
        private Long provinceNextId;
    }
}
