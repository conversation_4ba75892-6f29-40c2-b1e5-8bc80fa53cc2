package com.wanshifu.order.push.enums;

/**
 * <AUTHOR>
 * @date 2025/7/8 15:46
 */
public enum SiteOrderDetailMasterListMasterSubLabelEnum {


    /**
     * 常报最低价
     */
    USUAL_OFFER_LOW_PRICE("usual_offer_low_price", "常报最低价"),

    /**
     * 常抢一口价
     */
    USUAL_GRAB_ORDER("usual_grab_order", "常抢一口价"),

    /**
     * 距离近
     */
    NEARBY_MASTER("nearby_master","距离近");



    private final String labelName;

    private final String desc;

    SiteOrderDetailMasterListMasterSubLabelEnum(String labelName, String desc) {
        this.labelName = labelName;
        this.desc = desc;
    }

    public String getLabelName() {
        return labelName;
    }

    public String getDesc() {
        return desc;
    }
}
