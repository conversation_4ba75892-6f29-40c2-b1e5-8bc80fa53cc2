package com.wanshifu.order.push.request.tmplcity;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/3 14:26
 */
@Data
public class ListTmplCityOrderRecommendMasterRqtV2 {

    /**
     * 全局订单id
     */
    @NotNull
    @Min(1L)
    private Long globalOrderTraceId;

    /**
     * 返回数量限制，1~1000
     */
    @NotNull
    @Min(1)
    @Max(1000)
    private Integer returnLimit;

    /**
     * 排序类型，1: 降序，2: 升序, 默认按评分降序
     */
    private Integer sortType = 1;

    /**
     * 是否已推單，1：已推單，0：未推單
     */
    private Integer isPush;


    /**
     * 师傅来源类型，tob: B端师傅，toc: C端师傅
     */
    private String masterSourceType;
}
