package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/12/16
 */

@Data
public class GetTodayUserHireOrderRqt {

    /**
     * 师傅id
     */
    @NotNull
    private Long masterId;

    /**
     * 当前时间
     */
    @NotNull
    private Date nowTime;

    /**
     * 推单数据省下级地址id
     * (当前业务传订单区域id或者师傅区域id就行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}
