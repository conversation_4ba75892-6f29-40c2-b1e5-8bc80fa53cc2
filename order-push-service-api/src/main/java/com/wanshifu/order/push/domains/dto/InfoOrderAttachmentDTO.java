package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
public class InfoOrderAttachmentDTO {
    /**
     * 商品图片ID，主键自增
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 附件类型（image:图片，video：视频）
     */
    private String attachmentType;

    /**
     * 附件值
     */
    private String attachmentValue;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}