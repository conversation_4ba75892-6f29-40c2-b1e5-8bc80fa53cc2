package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class InfoOrderGoodsDTO implements Cloneable {
    /**
     * 订单商品ID，主键自增
     */
    private Long orderGoodsId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型
     */
    private Integer goodsCategory;

    /**
     * 商品数量
     */
    private Integer number;

    /**
     * 数量单位
     */
    private String numberUnit;

    /**
     * 商品数量
     */
    private BigDecimal area;

    /**
     * 面积单位
     */
    private String areaUnit;

    /**
     * 安装项（json格式）
     */
    private String installItem;

    /**
     * 备注
     */
    private String note;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}