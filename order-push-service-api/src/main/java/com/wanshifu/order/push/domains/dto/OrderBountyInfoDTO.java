package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/1 17:36
 */
@Data
public class OrderBountyInfoDTO {

    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 悬赏金额
     */
    private BigDecimal bountyAmount;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否删除（0否，1是）
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
