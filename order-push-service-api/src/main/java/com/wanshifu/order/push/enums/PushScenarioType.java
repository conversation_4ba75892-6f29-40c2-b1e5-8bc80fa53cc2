package com.wanshifu.order.push.enums;

import com.wanshifu.framework.core.BusException;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023-09-21 17:43
 * @Description 推单场景类型
 * @Version v1
 **/
public enum PushScenarioType {

    /**
     * 直接指派推单
     */
    DIRECT_ASSIGNMENT_PUSH("direct_assignment_push"),

    /**
     * 直接推单
     */
    DIRECT_PUSH("direct_push"),

    /**
     * 智能推单
     */
    SMART_PUSH("smart_push"),

    /**
     *  手动推单
     */
    MANUAL_PUSH("manual_push"),

    /**
     * 再次推单
     * (目前只有品牌师傅-非系统指派时会推单完成后，重新再推单)
     */
    AGAIN_PUSH("again_push")
    ;


    PushScenarioType(String scenarioVal) {
        this.scenarioVal = scenarioVal;
    }

    public final String scenarioVal;


    public static PushScenarioType tf (String code){
        return Stream.of(PushScenarioType.values()).filter(it -> it.scenarioVal.equals(code)).findFirst().orElseThrow(()-> new BusException(PushBusinessCode.REQ_VALIDATION_ERROR.code,"暂不支持的推单场景"));
    }
    /**
     * 是否手动推单
     * @return
     */
    public  boolean isManualPush(){

        if (this == MANUAL_PUSH) {
            return true;
        }
        return false;
    }

    /**
     * 业务工作场景 （包含手动推单、品牌订单再次推单）
     * @param pushScenarioType
     * @return
     */
    public static boolean bussWorkScene (PushScenarioType pushScenarioType){
        return Objects.nonNull(pushScenarioType) && (pushScenarioType == MANUAL_PUSH || pushScenarioType == AGAIN_PUSH);
    }

    /**
     * 推单工作场景（包含直接指派推单、直接推单、智能推单）
     * @param pushScenarioType
     * @return
     */
    public static  boolean pushWorkScene(PushScenarioType pushScenarioType){
        return Objects.nonNull(pushScenarioType) && (pushScenarioType == DIRECT_ASSIGNMENT_PUSH || pushScenarioType == DIRECT_PUSH ||  pushScenarioType == SMART_PUSH);

    }


}
