package com.wanshifu.order.push.response.special;

import lombok.Data;

import java.util.List;

/**
 * 获取报价订单附近单数量
 *
 * <AUTHOR>
 */
@Data
public class GetOrderNearbyNumberResp {

    /**
     * 报价-附近单数量   ---------------7.12--425迭代
     */
    private List<NearbyOrderVo> nearbyOrderList;

    @Data
    public static class NearbyOrderVo {
        /**
         * 订单id
         */
        private Long orderId;

        /**
         * 附近单数量
         */
        private Integer nearbyNumber;
    }
}
