package com.wanshifu.order.push.request.push;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021-03-13 16:48
 */
@Data
public class ClearExpiredPushRqt {

    /**
     * 查询过期时间小于 fromDayNumber 的数据
     */
    @NotNull
    private int fromDayNumber = 2;

    /**
     * 每次查询数量
     */
    @Max(4000)
    @NotNull
    private int queryNumber = 200;

    /**
     * 分页删除数量
     */
    @NotNull
    private int perDeleteNumber = 200;

}