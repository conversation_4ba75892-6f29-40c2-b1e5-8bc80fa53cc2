package com.wanshifu.order.push.response;

import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderExtraDataDTO;
import com.wanshifu.order.push.domains.dto.OrderLogisticsInfoDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/6/24 10:28
 */
@Data
public class WaitOfferNoPageResp {
    /**
     * 订单物流信息
     */
    @Deprecated
    private OrderLogisticsInfoDTO orderLogisticsInfoDTO;

    /**
     * 订单额外信息
     */
    private OrderExtraDataDTO orderExtraDataDTO;

    /**
     * 订单基本信息
     */
    private OrderBaseDTO orderBaseDTO;

    /**
     * 订单首个图片aid
     */
    private Long orderFirstImageAid;

}
