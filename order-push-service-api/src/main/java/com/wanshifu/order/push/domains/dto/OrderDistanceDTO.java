package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


@Data
public class OrderDistanceDTO {

    /**
     * PK,订单距离记录ID
     */
    private Long distanceId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 距离的订单ID
     */
    private Long distanceOrderId;

    /**
     * 订单类目id
     */
    private Integer categoryId;

    /**
     * 距离订单类目id
     */
    private Integer distanceCategoryId;

    /**
     * 城市ID（二级地址，对应address表division_id字段）
     */
    private Long secondDivisionId;

    /**
     * 地区ID（三级地址，对应address表division_id字段）
     */
    private Long thirdDivisionId;

    /**
     * 订单地址距离(订单之间的距离),单位米
     */
    private Long orderAddressDistance;

    /**
     * 删除状态,1:删除,0:未删除
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String note;

    /**
     * 距离记录创建时间
     */
    private Date distanceCreateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}