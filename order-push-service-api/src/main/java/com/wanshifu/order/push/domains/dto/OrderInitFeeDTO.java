package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderInitFeeDTO {
    /**
     * 订单初始费用ID，主键自增
     */
    private Long initFeeId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 初始代付运费
     */
    private BigDecimal initLogisticsFee;

    /**
     * 一口价服务费用
     */
    private BigDecimal definiteServeFee;

    /**
     * 初始预付款费用
     */
    private BigDecimal initAdvanceFee;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}