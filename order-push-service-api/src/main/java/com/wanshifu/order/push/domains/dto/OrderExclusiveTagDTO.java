package com.wanshifu.order.push.domains.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023-11-22 17:12
 * @Description
 * @Version v1
 **/
@Data
public class OrderExclusiveTagDTO {
    /**
     *  默认0 专属订单标记：1专属订单，2转换后的普通订单,100:专属二期转换后的普通订单,101:系统报价专属单,102:抢单报价单专属单,103成品报价专属单,104家庭专属单,
     * 105:总包专属单,106:系统报价专属单转抢单报价单专属单,107:满足兜底自动抢单专属单(专属转普通订单后),108:满足兜底自动报价专属单,109:师傅店铺订单
     */
    private Integer tagValue;

    /**
     * 标签名,exclusive:专属,direct_appointment:直约,preferred:优选,contract:合约
     * agent:代理商
     */
    private String tagName;

    /**
     * 标签类型 exclusive-专属  agent-代理 merchant_invite-商家邀请
     */
    private String tagType;

    /**
     * 标签展示类型 0-全局  1-待报价列表
     */
    private Integer displayType;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 是否动态标签
     * 固定格式 tagType:一级标签    tagName:一级描述
     */
    private Integer isDynamicTag = 0;


    /**
     * 一级标签描述
     */
    private String lv1TagDesc;
}
