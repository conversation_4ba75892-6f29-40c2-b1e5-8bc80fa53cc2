package com.wanshifu.order.push.request.special;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SideOrderCombinationDTO {

    /**
     * 师傅ID
     */
    @NotNull
    @Min(value = 1L)
    private Long masterId;


    /**
     * 订单ids集合（最大5个）
     */
    @NotNull
    @Size(min = 1,max = 6)
    private List<Long> orderIds;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    @Min(value = 1L)
    @NotNull
    private Long provinceNextId;

}
