package com.wanshifu.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.order.push.domains.dto.OrderPushDTO;
import com.wanshifu.order.push.request.*;
import com.wanshifu.order.push.request.push.NoOfferByOrderIdRqt;
import com.wanshifu.order.push.response.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 14:01
 */
@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/orderPush/resource")
public interface OrderPushResourceApi {

    /**
     * 批量获取推单时间
     * @param getPushTimeRqt
     * @return
     */
    @PostMapping("batchPushTime")
    List<BatchGetPushTimeResp> batchPushTime(@RequestBody @Validated BatchGetPushTimeRqt getPushTimeRqt);


    /**
     * 查询意向订单推单师傅集合(全局id)
     * 查询10条
     * 网站邀请师傅报价场景
     */
    @PostMapping("getInvitePushMasterIds")
    List<Long> getInvitePushMasterIds(@RequestBody @Valid GetInvitePushMasterIdsRqt rqt);


    /**
     * 查询该师傅的在途的推单订单
     *
     * @param rqt
     * @return
     */
    @PostMapping("selectPushOrderByMasterId")
    public List<OrderPushDTO> selectPushOrderByMasterId(@RequestBody @Valid SelectPushOrderByMasterIdRqt rqt);

    /**
     * 订单ID和师傅ID获取订单推送信息
     */
    @PostMapping("batchOrderPush")
    List<BatchOrderPushResp> batchOrderPush(@RequestBody @Valid BatchOrderPushRqt batchOrderPushRqt);


    /**
     * 订单ID和师傅ID批量获取订单推送信息
     * @param batchOrderPushRqt
     * @return
     */
    @PostMapping("batchGetOrderPush")
    List<OrderPushDTO> batchGetOrderPush(@RequestBody @Valid BatchOrderPushRqt batchOrderPushRqt);

    /**
     * 订单ID和师傅ID取订单推送信息
     * @param getOrderPushRqt
     * @return
     */
    @PostMapping("getOrderPush")
    OrderPushDTO getOrderPush(@RequestBody @Valid GetOrderPushRqt getOrderPushRqt);

    /**
     * 根据orderIds + masterId 批量查询订单推送信息
     * @param batchGetOrderPushRqt
     * @return
     */
    @PostMapping("batchGetOrderPushByOrderIdsAndMasterId")
    List<OrderPushDTO> batchGetOrderPushByOrderIdsAndMasterId(@RequestBody @Valid BatchGetOrderPushRqt batchGetOrderPushRqt);

    /**
     * 根据orderId查询订单推送信息
     *
     * @param orderId 订单id
     * @param provinceNextId 省下级地址id (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     * @return
     */
    @PostMapping("getOrderPushByOrderId")
    List<OrderPushDTO> getOrderPushByOrderId(@RequestParam(value = "orderId") Long orderId, @RequestParam(value = "provinceNextId", required = false) Long provinceNextId);

    /**
     * 根据orderId查询订单推送未报价信息
     *
     * @param orderId 订单id
     * @param provinceNextId 省下级地址id (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     * @return
     */
    @PostMapping("getOrderPushNoOfferByOrderId")
    List<OrderPushDTO> getOrderPushNoOfferByOrderId(@RequestParam(value = "orderId") Long orderId, @RequestParam(value = "provinceNextId", required = false) Long provinceNextId);

    /**
     * 根据orderId查询订单推送未报价信息  分页接口
     * @param rqt
     * @return
     */
    @PostMapping("getOrderPushNoOfferByOrderIdV2")
    SimplePageInfo<OrderPushDTO> getOrderPushNoOfferByOrderIdV2(@Valid @RequestBody NoOfferByOrderIdRqt rqt);

    /**
     * 获取推单新旧系统切换开关
     * @return
     */
    @PostMapping("getHandoffTag")
    String getHandoffTag(@RequestParam(value = "orderId") Long orderId);

    /**
     * 查询师傅待报价列表订单类目占比
     * @interface
     * <AUTHOR>
     **/
    @PostMapping("getMasterOrderCategoryRate")
    List<GetMasterOrderCategoryRateResp> getMasterOrderCategoryRate(@RequestBody @Validated GetMasterOrderCategoryRateRqt rqt);


    /**
     * 查询订单待报价师傅id
     * <AUTHOR>
     **/
    @PostMapping("getWaitOfferMasterIdsByOrderId")
    List<Long> getWaitOfferMasterIdsByOrderId(@RequestBody @Validated GetWaitOfferMasterIdsByOrderIdRqt getWaitOfferMasterIdsByOrderIdRqt);



    /**
     * 查询师傅待报价列表可筛选订单类目选择器
     * @param rqt
     * @interface
     * <AUTHOR>
     **/
    @PostMapping("getMasterCategorySelector")
    List<Long> getMasterCategorySelector(@RequestBody @Validated GetMasterCategorySelectorRqt rqt);

    /**
     * 查询师傅推送后是否已读详情
     *
     * @param getUnreadRqt
     * @return
     */
    @PostMapping("getUnread")
    GetUnreadResp getUnread(@RequestBody @Validated GetUnreadRqt getUnreadRqt);

    /**
     * 批量获取订单已查看师傅数量 (最多20条)
     * @param rqt
     * @interface
     * <AUTHOR>
     **/
    @PostMapping("batchGetMasterViewNumber")
    List<GetMasterViewNumberResp> batchGetMasterViewNumber(@RequestBody @Validated BatchGetMasterViewNumberRqt rqt);

    /**
     * 批量获取订单已查看师傅数量V2 (最多20条)
     * @param rqt BatchGetMasterViewNumberV2Rqt
     * @return List<GetMasterViewNumberResp>
     */
    @PostMapping("batchGetMasterViewNumberV2")
    List<GetMasterViewNumberResp> batchGetMasterViewNumberV2(@RequestBody @Validated BatchGetMasterViewNumberV2Rqt rqt);

    /**
     * 统计分析待报价订单指标
     * @param analyticWaitOfferCountReq
     * @return
     */
    @PostMapping("analyticWaitOfferCount")
    WaitOfferCountResp analyticWaitOfferCount(@RequestBody @Validated WaitOfferCountReq analyticWaitOfferCountReq);

    /**
     * 获取当天是否有用户直接雇佣的订单
     * arms统计到师傅app调用，但师傅侧反馈已无调用
     * @param getTodayUserHireOrderRqt
     * @return
     */
    @PostMapping("getTodayUserHireOrder")
    GetTodayUserHireOrderResp getTodayUserHireOrder(@RequestBody @Validated GetTodayUserHireOrderRqt getTodayUserHireOrderRqt);


    /**
     * 查询订单被师傅查看人数
     * @param getOrderShowRqt
     * @return
     */
    @PostMapping("getOrderShowNumOfPeople")
    Integer getOrderShowNumOfPeople(@RequestBody @Validated GetOrderShowRqt getOrderShowRqt);

    /**
     * 平台干预列表
     */
    @PostMapping("getInterfereOrderPushList")
    List<GetInterfereOrderPushListResp> getInterfereOrderPushList(@RequestBody @Valid GetInterfereOrderPushListRqt rqt);


    @PostMapping("orderPushCount")
    OrderPushCountResp orderPushCount(@RequestBody @Validated OrderPushCountRqt orderPushCountRqt);
}
