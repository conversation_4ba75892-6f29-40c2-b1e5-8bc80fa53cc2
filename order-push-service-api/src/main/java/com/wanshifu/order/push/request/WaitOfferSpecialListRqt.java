package com.wanshifu.order.push.request;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Title：师傅待报价订单列表--特殊列表
 */
@Data
@AllArgsConstructor
public class WaitOfferSpecialListRqt {

    /**
     * 师傅id
     */
    @NotNull
    private Long masterId;

    /**
     * 报价订单id
     */
    @NotNull
    private Long officeOrderId;

    /**
     * 特殊订单类型(1:附近单,2:附近相似单)
     */
    @NotNull
    @ValueIn("1,2")
    private Integer specialSideType;

    /**
     * 是否跳过order_push的推单距离校验(关闭订单、改派、修改订单地址这几个缺省页场景需要跳过，传true)
     */
    private boolean skipCheckOrderPushDistance;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    /**
     * 样板城市订单师傅身份
     * 1：主力师傅
     * 2：储备师傅
     * 3.普通师傅
     */
    private Integer tmplCityMasterRole;
}
