package com.wanshifu.order.push.api;

import com.wanshifu.order.push.request.InviteMasterOfferMarkRqt;
import com.wanshifu.order.push.request.push.*;
import com.wanshifu.order.push.response.push.BatchUpdatePushDistanceResp;
import com.wanshifu.order.push.response.push.PushedToMasterResp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;


/**
 * <AUTHOR>
 * @Date 2023-08-22 13:48
 * @Description 报价单推单模块
 * @Version v1
 **/

@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/orderPush/operate")
public interface OrderPushOperateApi {

    /**
     * 订单推送给师傅
     * @param pushOrderRqt
     * <AUTHOR>
     **/
    @PostMapping("pushedToMaster")
    PushedToMasterResp pushedToMaster(@RequestBody @Valid PushedToMasterRqt pushOrderRqt);

    /**
     * 师傅更新服务地区服务类型重新计算推单
     * @param resendOrderPushRqt
     * @return
     */
    @PostMapping("resendOrderPushByMaster")
    int resendOrderPushByMaster(ResendOrderPushRqt resendOrderPushRqt);

    /**
     * 清理过期的推送记录
     *
     * @param clearExpiredPushRqt
     * @return
     */
    @PostMapping("clearExpiredOrderPush")
    Integer clearExpiredOrderPush(ClearExpiredPushRqt clearExpiredPushRqt);

    /**
     * 清理分表过期的推送记录
     * 分表清理
     *
     * @param clearExpiredPushRqt
     * @return
     */
    @PostMapping("clearExpiredOrderPushV2")
    Integer clearExpiredOrderPushV2(ClearExpiredPushRqt clearExpiredPushRqt);

    /**
     * 批量更新师傅推单距离和经纬度
     * @param batchUpdatePushDistanceRqt
     * @return
     */
    @PostMapping("batchUpdatePushDistance")
    @Deprecated
    BatchUpdatePushDistanceResp batchUpdatePushDistance(@RequestBody @Valid BatchUpdatePushDistanceRqt batchUpdatePushDistanceRqt);

    /**
     * 批量更新师傅推单距离和经纬度
     * 待报价列表数据架构升级项目V2版本，去除根据主键pushId操作逻辑，增加省下级地址id参数传递
     * @param batchUpdatePushDistanceV2Rqt
     * @return
     */
    @PostMapping("batchUpdatePushDistanceV2")
    BatchUpdatePushDistanceResp batchUpdatePushDistanceV2(@RequestBody @Valid BatchUpdatePushDistanceV2Rqt batchUpdatePushDistanceV2Rqt);


    /**
     * @param orderId  订单id
     * @param masterId 师傅id
     * @interface 手动删除订单推送记录---慎用【备用接口】
     * <AUTHOR>
     **/
    @PostMapping("manualClearPush")
    @Deprecated
    int manualClearPush(@org.springframework.web.bind.annotation.RequestParam("orderId") Long orderId, @org.springframework.web.bind.annotation.RequestParam("masterId") Long masterId);


    /**
     * 修改推单记录菜单类别
     * @param updateOrderPushMenuCategoryRqt
     * @return
     */
    @PostMapping("updateOrderPushMenuCategory")
    Integer updateOrderPushMenuCategory(@RequestBody @Valid UpdateOrderPushMenuCategoryRqt updateOrderPushMenuCategoryRqt);


    /**
     *  清除orderPush记录
     * @param clearOrderPushRqt
     * @return
     */
    @PostMapping("clearOrderPush")
    Integer clearOrderPush(@RequestBody @Valid ClearOrderPushRqt clearOrderPushRqt);

    /**
     * 更新orderPush的offerTime
     * @param updateOrderPushOfferTimeRqt
     * @return
     */
    @PostMapping("updateOrderPushOfferTime")
    Integer updateOrderPushOfferTime(@RequestBody @Valid UpdateOrderPushOfferTimeRqt updateOrderPushOfferTimeRqt);

    /**
     * 更新orderPush的firstViewTime
     * @param updateFirstViewTimeRqt
     * @return
     */
    @PostMapping("updateOrderPushFirstViewTime")
    Integer updateOrderPushFirstViewTime(@RequestBody @Valid UpdateFirstViewTimeRqt updateFirstViewTimeRqt);


    /**
     * 更新推单记录的 订单距离拉取状态
     * @param updatePullOrderDistanceRqt
     * @return
     */
    @PostMapping("updatePullOrderDistanceByPushId")
    @Deprecated
    Integer updatePullOrderDistanceByPushId(@RequestBody @Valid UpdatePullOrderDistanceRqt updatePullOrderDistanceRqt);

    /**
     * 更新推单记录的 订单距离拉取状态
     * @param updatePullOrderDistanceRqt
     * @return
     */
    @PostMapping("updatePullOrderDistanceByPushIdV2")
    Integer updatePullOrderDistanceByPushIdV2(@RequestBody @Valid UpdatePullOrderDistanceRqtV2 updatePullOrderDistanceRqt);

    /**
     * 更新物流到货状态--用于排序
     * @param updateIsArrivedRqt
     * @return
     */
    @PostMapping("updateIsArrivedByOrderId")
    Integer updateIsArrivedByOrderId(@RequestBody @Valid UpdateIsArrivedRqt updateIsArrivedRqt);


    /**
     * 更新orderPush的IsPullView
     * @param updatePullViewTimeRqt
     * @return
     */
    @PostMapping("updateOrderPushViewTime")
    Integer updateOrderPushViewTime(@RequestBody @Valid UpdatePullViewTimeRqt updatePullViewTimeRqt);

    /**
     * 查看订单详情后续逻辑
     * @param afterOrderDetailRqt
     */
    @PostMapping("afterOrderDetail")
    void afterOrderDetail(@RequestBody @Valid AfterOrderDetailRqt afterOrderDetailRqt);

}
