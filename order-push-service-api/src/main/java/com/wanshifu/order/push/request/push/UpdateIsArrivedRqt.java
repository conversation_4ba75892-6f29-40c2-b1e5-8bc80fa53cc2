package com.wanshifu.order.push.request.push;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/11 17:43
 */
@Data
public class UpdateIsArrivedRqt {

    @NotNull
    private Long orderId;

    /**
     * 到货标识 2 未到货 1已到货
     */
    @NotNull
    private Integer isArrived;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     *
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}
