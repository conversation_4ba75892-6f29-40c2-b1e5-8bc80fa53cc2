package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
public class OrderLogisticsInfoDTO {
    /**
     * 订单物流信息ID，主键自增
     */
    private Long logisticsId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 货物是否到达物流签收点
1：已达到； 2：未到达； 0：默认  3:部分到货
     */
    private Integer isArrived;

    /**
     * 物流公司
     */
    private String logisticsCompanyName;

    /**
     * 物流运单号
     */
    private String logisticsNo;

    /**
     * 物流提货地址
     */
    private String pickupAddress;

    /**
     * 物流提货电话
     */
    private String pickupPhone;

    /**
     * 包装件数
     */
    private Integer packNumber;

    /**
     * 预计到货时间
     */
    private Date expectArriveTime;

    /**
     * 到货时间
     */
    private Date arriveTime;

    /**
     * 物流补充时间(雇佣后物流补充的时间)
     */
    private Date logisticsSupplementTime;

    /**
     * 货物是否到达客户家
1：已到货； 2：未到货； 0：默认  3:部分到货
     */
    private Integer arriveCustomerHomeStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作到货人类型（user:用户，enterprise:总包，master:师傅）,默认用户
     */
    private String operateArrivedAccountType;

    /**
     * 取货地点与客户家之间的距离(单位:米)
     */
    private Integer logisticsToCustomerDistance;


    /**
     * 操作提货地址人类型（user:用户，enterprise:总包，master:师傅）
     */
    private String operatePickupAddressAccountType;
}