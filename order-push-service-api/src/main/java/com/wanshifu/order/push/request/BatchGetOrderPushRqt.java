package com.wanshifu.order.push.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/11 11:04
 */
@Data
public class BatchGetOrderPushRqt {

    /**
     * 师傅id
     */
    @NotNull
    @Min(1L)
    private Long masterId;

    /**
     * 订单ids
     */
    @Size(min = 1, max = 10)
    @NotEmpty
    private List<Long> orderIds;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     * 以师傅维度传参，传师傅所在区域地址
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}
