package com.wanshifu.order.push.enums;

/**
 * <AUTHOR>
 * @date 2025/7/8 15:33
 */
public enum SiteOrderDetailMasterListMasterMainLabelEnum {


    /**
     * 收藏师傅
     */
    COLLECT_MASTER("collect_master", "收藏师傅"),

    /**
     * 合作过
     */
    COOPERATION_MASTER("cooperation_master", "合作过"),

    /**
     * 服务好
     */
    GOOD_SERVICE("good_service","服务好"),

    /**
     * 省钱师傅
     */
    SAVE_MONEY("save_money","省钱师傅"),

    /**
     * 普通师傅
     */
    NORMAL("normal","普通师傅");




    private final String labelName;

    private final String desc;

    SiteOrderDetailMasterListMasterMainLabelEnum(String labelName, String desc) {
        this.labelName = labelName;
        this.desc = desc;
    }

    public String getLabelName() {
        return labelName;
    }

    public String getDesc() {
        return desc;
    }
}
