package com.wanshifu.order.push.request.push;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/11 11:47
 */
@Data
public class AfterOrderDetailRqt {


    /**
     * 师傅id
     */
    @NotNull
    @Min(value = 1L)
    private Long masterId;

    /**
     * 订单id
     */
    @NotNull
    @Min(value = 1L)
    private Long orderId;

    /**
     * 省下级地址id（可传thirdDivisionId）
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    /**
     * 师傅查看详情时间
     * 默认new Date()
     */
    @NotNull
    private Date firstViewTime = new Date();

    /**
     * orderGrab的secondDivisionId
     */
    private Long secondDivisionId;

    /**
     * 订单类目id
     */
    private Integer categoryId;

    /**
     * 1： 代理商查看订单
     * 0: 无任何操作
     * 默认0
     */
    @ValueIn("1,0")
    private Integer agentViewOrderFlag = 0;
}
