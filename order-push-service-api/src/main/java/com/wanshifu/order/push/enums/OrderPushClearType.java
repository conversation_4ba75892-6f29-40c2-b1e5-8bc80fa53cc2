package com.wanshifu.order.push.enums;

import com.wanshifu.framework.core.BusException;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/4 17:56
 */
public enum OrderPushClearType {

    /**
     * 修改订单
     */
    MODIFY_ORDER("modifyOrder"),


    /**
     * 有师傅报价后，修改订单
     * 该类型场景：当没有修改订单地址，也就是不需要重新推单的场景，业务只会调这个类型
     */
    OFFER_MODIFY_ORDER("offerModifyOrder"),

    /**
     * 抢单
     */
    GRAB_DEFINITE("grabDefinite"),

    /**
     * 报价
     */
    OFFER_PRICE("offerPrice"),

    /**
     * 取消报价
     */
    CANCEL_OFFER_PRICE("cancelOfferPrice"),

    /**
     * 一口价报价
     */
    DEFINITE_OFFER_PRICE("definiteOfferPrice"),


    /**
     * 达到最高报价人数
     */
    LAST_OFFER_PRICE("last_offer_price"),

    /**
     * 关单
     */
    CLOSE_ORDER("closeOrder"),

    /**
     * 信息订单关单
     */
    CLOSE_INFO_ORDER("closeInfoOrder"),

    /**
     * 雇佣师傅
     */
    HIRE_MASTER("hireMaster"),

    /**
     * 取消指派模式
     */
    CANCEL_METHOD("cancelMethod"),

    /**
     * 取消雇佣
     */
    CANCEL_APPOINT("cancelAppoint"),

    /**
     * 信息订单清除orderPush
     */
    INFO_ORDER_BASE_CLEAR_PUSH("infoOrderBaseClearPush"),

    /**
     * 师傅不感兴趣订单
     */
    MASTER_DISINTEREST_ORDER("masterDisinterestOrder"),

    /**
     * 取消师傅转单
     */
    CANCEL_MASTER_TRANSFER_ORDER("cancelMasterTransferOrder"),

    ;
    public final String type;


    OrderPushClearType(String type) {
        this.type = type;
    }

    public static OrderPushClearType of(String type){
        return Stream.of(OrderPushClearType.values()).filter(x -> x.type.equals(type)).findFirst().orElseThrow(() -> new BusException(PushBusinessCode.REQ_VALIDATION_ERROR.code, "未支持的删除orderPush场景"));
    }

}
