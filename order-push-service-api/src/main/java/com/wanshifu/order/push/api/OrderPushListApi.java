package com.wanshifu.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.order.push.request.*;
import com.wanshifu.order.push.request.push.MasterAppletListOrderPushRqt;
import com.wanshifu.order.push.request.push.TmplCityOrderPushRqt;
import com.wanshifu.order.push.response.*;
import com.wanshifu.order.push.response.push.MasterAppletListOrderPushResp;
import com.wanshifu.order.push.response.push.TmplCityOrderPushResp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 14:00
 */
@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/orderPush/list")
public interface OrderPushListApi {

    /**
     * 师傅待报价订单列表(特殊列表)
     * @param rqt WaitOfferSpecialListRqt
     * @return WaitOfferV2Resp
     * <AUTHOR>
     */
    @PostMapping("waitOfferSpecialList")
    SimplePageInfo<WaitOfferV2Resp> waitOfferSpecialList(@Valid @RequestBody WaitOfferSpecialListRqt rqt);


    /**
     * 师傅待报价订单列表V2
     * @param waitOfferRqt WaitOfferV2Rqt
     * @return SimplePageInfo<WaitOfferResp>
     * <AUTHOR>
     */
    @PostMapping("waitOfferV2")
    SimplePageInfo<WaitOfferV2Resp> waitOfferV2(@Valid @RequestBody WaitOfferV2Rqt waitOfferRqt);

    /**
     * 家庭模版城市订单推单列表
     * @param rqt
     * @return
     */
    @PostMapping("/tmplCityOrderPushList")
    SimplePageInfo<TmplCityOrderPushResp> tmplCityOrderPushList(@Valid @RequestBody TmplCityOrderPushRqt rqt);


    /**
     * 师傅待报价订单列表不分页
     * @param waitOfferNoPageRqt WaitOfferNoPageRqt
     * @return SimplePageInfo<WaitOfferResp>
     * <AUTHOR>
     */
    @PostMapping("waitOfferNoPage")
    List<WaitOfferNoPageResp> waitOfferNoPage(@RequestBody @Valid WaitOfferNoPageRqt waitOfferNoPageRqt);

    /**
     * IOC(智能运营)活动待报价-筛选数据
     *
     * @param rqt IocWaitOfferFilterRqt
     * @return IocWaitOfferFilterResp
     */
    @PostMapping("getIocWaitOfferList")
    List<IocWaitOfferFilterResp> getIocWaitOfferList(@Valid @RequestBody IocWaitOfferFilterRqt rqt);

    /**
     * 批量获取订单待报价信息（最大20个订单）
     * @param batchGetOrderWaitOfferInfoReq batchGetOrderWaitOfferInfoReq
     * @return WaitOfferV2Resp
     */
    @PostMapping("batchGetOrderWaitOfferInfo")
    List<WaitOfferV2Resp> batchGetOrderWaitOfferInfo(@RequestBody @Validated BatchGetOrderWaitOfferInfoReq batchGetOrderWaitOfferInfoReq);


    /**
     * 获取订单推单数据（master-notice-service 调用）
     * @param provinceNextId 省下级地址id
     * @param orderId orderId
     * @return BatchOrderPushResp
     */
    @PostMapping("getOrderPushForNotice")
    List<BatchOrderPushResp> getOrderPushForNotice(@RequestParam("orderId") Long orderId, @RequestParam("provinceNextId") Long provinceNextId);


    /**
     * 获取推单列表（master-order-distribute-service） 调用
     *
     * @param provinceNextId 省下级地址id
     * @param orderId orderId 订单id
     * @return BatchOrderPushResp
     */
    @PostMapping("getOrderPushList")
    List<BatchOrderPushResp> getOrderPushList(@RequestParam("orderId") Long orderId, @RequestParam("provinceNextId") Long provinceNextId);

    /**
     * 师傅小程序游客模式获取推单列表
     * 返回20条
     * @param masterAppletListOrderPushRqt masterAppletListOrderPushRqt
     * @return List<MasterAppletListOrderPushResp>
     */
    @PostMapping("unLoginGetOrderPushList")
    List<MasterAppletListOrderPushResp> unLoginGetOrderPushList(@RequestBody @Valid MasterAppletListOrderPushRqt masterAppletListOrderPushRqt);


    /**
     *
     * @param waitOfferNoPageRqt WaitOfferNoPageRqt
     * @return SimplePageInfo<WaitOfferResp>
     * <AUTHOR>
     */
    @PostMapping("getMasterOrderPushList")
    List<BatchOrderPushResp> getMasterOrderPushList(@RequestBody @Valid WaitOfferNoPageRqt waitOfferNoPageRqt);


    @PostMapping("getMasterOrderPushByAccount")
    List<BatchOrderPushResp> getMasterOrderPushByAccount(@RequestBody @Valid GetMasterOrderPushByAccountRqt rqt);


    /**
     * 查询订单推送师傅列表(供AI通知师傅接单使用)
     * @return List<BatchOrderPushResp>
     */
    @PostMapping("getOrderPushForAiNotice")
    List<BatchOrderPushResp> getOrderPushForAiNotice(@RequestBody @Valid GetOrderPushForAiNoticeRqt rqt);


    @PostMapping("getFamilyOrderPushList")
    List<BatchOrderPushResp> getFamilyOrderPushList(@RequestBody @Valid GetFamilyOrderPushListRqt rqt);


    /**
     * 查询推送师傅详情数据
     * @param rqt
     * @return
     */
    @PostMapping("getPushMasterList")
    List<PushMasterListResp> getPushMasterList(@RequestBody @Valid GetPushMasterListRqt rqt);


    /**
     * 查询推送已推送的有经验师傅
     * @param rqt
     * @return
     */
    @PostMapping("getPushedExperiencedMasterList")
    List<BatchOrderPushResp> getPushedExperiencedMasterList(@RequestBody @Valid GetPushedExperiencedMasterListRqt rqt);


    /**
     * 网站订单详情获取附近推单师傅
     *
     * @param rqt
     * @return
     */
    @PostMapping("getSiteOrderDetailMasterList")
    SimplePageInfo<SiteOrderDetailMasterListResp> getSiteOrderDetailMasterList(@RequestBody @Valid SiteOrderDetailMasterListReq rqt);

    /**
     * 网站订单详情获取订单可推单的全部师傅数
     * @param rqt
     * @return
     */
    @PostMapping("getPushMasterCountBySiteOrderDetail")
    SiteOrderDetailPushMasterCountResp getPushMasterCountBySiteOrderDetail(@RequestBody @Valid SiteOrderDetailPushMasterCountReq rqt);
}
