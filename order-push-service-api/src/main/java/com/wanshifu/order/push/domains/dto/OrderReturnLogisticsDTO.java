package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderReturnLogisticsDTO {
    /**
     * 返回物流ID
     */
    private Long returnId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人手机号
     */
    private String receiverPhone;

    /**
     * 收货地址
     */
    private String receiverAddress;

    /**
     * 物流公司(下单时选择)
     */
    private String company;

    /**
     * 保价费用
     */
    private BigDecimal insuredAmount;

    /**
     * 送货方式：
     * 1：送货上门；2：物流点自提
     */
    private Integer deliveryMode;

    /**
     * 是否需要师傅自带包装：
     * 0：不需要；1：需要
     */
    private Integer needPack;

    /**
     * 是否需要师傅打木架：
     * 0：不需要；1：需要
     */
    private Integer needWoodPack;

    /**
     * 是否需要师傅打包：
     * 0：不需要：1：需要
     */
    private Integer needMasterPack;

    /**
     * 付款方式：
     * 1：下单方到付；2：师傅代付（默认300元）
     */
    private Integer payMethod;

    /**
     * 物流电话（师傅确认完成时填入）
     */
    private String logisticsPhone;

    /**
     * 物流费用（师傅确认完成时填入）
     */
    private BigDecimal logisticsFee;

    /**
     * 物流公司（师傅确认完成时填入）
     */
    private String logisticsCompanyName;

    /**
     * 物流单号（师傅确认完成时填入）
     */
    private String logisticsNo;

    /**
     * 备注说明
     */
    private String information;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}