package com.wanshifu.order.push.response;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class GetInterfereOrderPushListResp {

    /**
     * 全局订单id
     */
    private Long globalOrderTraceId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 该订单师傅评分值
     */
    private BigDecimal score;

    /**
     * 创建时间
     */
    private Date createTime;


}
