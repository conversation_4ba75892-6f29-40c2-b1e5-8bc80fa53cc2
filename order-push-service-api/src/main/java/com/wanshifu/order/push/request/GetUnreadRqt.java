package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/6/26 10:24
 */
@Data
public class GetUnreadRqt {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}
