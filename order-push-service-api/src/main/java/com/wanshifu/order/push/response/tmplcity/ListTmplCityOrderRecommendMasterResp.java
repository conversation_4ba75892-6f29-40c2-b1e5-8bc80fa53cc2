package com.wanshifu.order.push.response.tmplcity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 获取样板城市订单推荐师傅resp
 * @date 2024/6/25 14:19
 */
@Data
public class ListTmplCityOrderRecommendMasterResp {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 全局订单id
     */
    private Long globalOrderTraceId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 该订单师傅排序值
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
