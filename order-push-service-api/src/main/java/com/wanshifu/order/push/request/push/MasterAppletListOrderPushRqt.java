package com.wanshifu.order.push.request.push;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 师傅小程序获取待报价列表rqt
 * @date 2024/4/25 15:50
 */
@Data
public class MasterAppletListOrderPushRqt {

    /**
     * 师傅地址id
     */
    @NotNull
    private Long masterDivisionId;

    /**
     * 推单数据省下级地址id
     * (当前业务传订单区域id或者师傅区域id就行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;


    /**
     * 师傅来源类型，tob: B端师傅，toc: C端师傅，不传查询所有
     */
    private String masterSourceType;
}
