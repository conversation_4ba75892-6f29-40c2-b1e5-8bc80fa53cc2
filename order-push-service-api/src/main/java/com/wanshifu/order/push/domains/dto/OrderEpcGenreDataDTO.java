package com.wanshifu.order.push.domains.dto;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Data
public class OrderEpcGenreDataDTO {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 服务内容,多个逗号隔开
     */
    private String serviceItem;

    /**
     * 进场时间
     */
    private Date courseDate;

    /**
     * 施工工期
     */
    private Integer constructionDays;

    /**
     * 工期单位,天、小时
     */
    private String daysUnit;

    /**
     * 施工类型（学校、酒店..）
     */
    private String constructionType;

    /**
     * 商品数量
     */
    private Integer goodsNum;

    /**
     * 备注信息
     */
    private String note;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除状态（0否，1是）
     */
    private Integer isDelete;

}