package com.wanshifu.order.push.request.push;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/1 15:12
 */
@Data
public class UpdatePullOrderDistanceRqtV2 {

    @NotNull
    @Min(value = 1L)
    private Long orderId;

    @NotNull
    @Min(value = 1L)
    private Long masterId;

    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    @NotNull
    private Integer isPullOrderDistance;
}
