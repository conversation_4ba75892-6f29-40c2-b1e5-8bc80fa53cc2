package com.wanshifu.order.push.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-11-29 16:38
 * @Description
 * @Version v1
 **/
@Data
public class BatchGetOrderWaitOfferInfoReq {

    @Size(max = 10)
    @NotEmpty
    private List<Long> orderIds;

    @NotNull
    private Long masterId;

    @NotNull
    private Long provinceNextId;
}
