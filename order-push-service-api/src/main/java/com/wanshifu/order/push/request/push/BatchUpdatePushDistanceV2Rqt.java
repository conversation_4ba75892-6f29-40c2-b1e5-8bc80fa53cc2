package com.wanshifu.order.push.request.push;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 描述 :  批量更新师傅推单距离和经纬度.
 * @date 2024/6/12 17:13
 */
@Data
public class BatchUpdatePushDistanceV2Rqt {

    /**
     * 师傅ID
     */
    @NotNull
    @Min(1)
    private Long masterId;

    /**
     * 师傅经度
     */
    @NotNull
    private BigDecimal masterLongitude;

    /**
     * 师傅纬度
     */
    @NotNull
    private BigDecimal masterLatitude;

    @NotNull
    @Valid
    private List<BatchUpdatePushDistanceV2Rqt.MasterAddressInfoList> masterAddressInfoList;


    /**
     * 省下级地址id
     * 前业务传的是师傅所在区域id，order-push-service计算省下级地址id
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    @Data
    @Validated
    public static class MasterAddressInfoList {

        /**
         * 师傅常驻地址和客户之间的路径规划距离
         */
        @NotNull
        private Long orderId;

        /**
         * 师傅常驻地址和客户之间的路径规划距离
         */
        private Long pushDistance = 0L;
        /**
         * 推单距离类型 1：导航距离(默认) 2:直线距离
         */
        private Integer pushDistanceType;

    }
}
