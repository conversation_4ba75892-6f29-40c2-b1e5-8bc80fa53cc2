package com.wanshifu.order.push.request.special;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

/**
 * Title：清理特殊订单
 */
@Data
@AllArgsConstructor
public class ClearSpecialOrderDTO {

    /**
     * 查询过期时间小于 fromDayNumber 的数据
     */
    @NotNull
    private int fromDayNumber = 3;

    /**
     * 每次查询数量
     */
    @Max(4000)
    @NotNull
    private int queryNumber = 4000;

    /**
     * 分页删除数量
     */
    @NotNull
    private int perDeleteNumber = 200;
}
