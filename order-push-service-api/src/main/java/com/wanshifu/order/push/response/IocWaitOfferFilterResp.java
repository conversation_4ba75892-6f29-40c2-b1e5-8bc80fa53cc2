package com.wanshifu.order.push.response;

import lombok.Data;

import java.util.Date;

/**
 * IOC(智能运营)活动待报价-展示数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/8/25 15:27
 */
@Data
public class IocWaitOfferFilterResp {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单创建时间
     */
    private Date orderCreateTime;

    /**
     * 下单人账号类型
     */
    private String accountType;

    /**
     * 来源(客户端)site:网站,enterprise_system:总包外部订单,weixin:微信,ikea:宜家,thirdpart:第三方平台,applet:小程序
     */
    private String orderFrom;

    /**
     * 指派类型
     */
    private Integer appointType;

    /**
     * 商品数量
     */
    private Integer goodsNumber;

    /**
     * 订单三级地址
     */
    private Long thirdDivisionId;

    /**
     * 订单四级地址
     */
    private Long fourthDivisionId;

    /**
     * 服务IDS
     */
    private String serveIds;
}
