package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DefiniteGoodsPriceDTO implements Serializable {
    /**
     * 商品价格ID，主键自增
     */
    private Long goodsPriceId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单商品ID
     */
    private Long orderGoodsId;

    /**
     * 安装费用(元)
     */
    private BigDecimal servePrice;

    /**
     * 安装商品数量
     */
    private Short serveNumber;

    /**
     * 其他费用(JSON)
     */
    private String otherPrice;

    /**
     * 优惠费用(元)
     */
    private BigDecimal discountPrice;

    /**
     * 订单商品小计费用
     */
    private BigDecimal subtotalPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}