package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
public class OrderGoodsImageRelaDTO {
    /**
     * 商品图片ID，主键自增
     */
    private Long goodsImageId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单商品ID
     */
    private Long orderGoodsId;

    /**
     * 图片AID
     */
    private Long imageAid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 图片类型（goods:商品图片，environment:安装环境图；默认:goods）
     */
    private String imageType;
}