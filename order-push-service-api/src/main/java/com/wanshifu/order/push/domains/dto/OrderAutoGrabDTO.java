package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/12 10:30
 */
@Data
public class OrderAutoGrabDTO {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 自动模式 grab:抢单;offer:报价
     */
    private String autoMold;

    /**
     * 抢单标签 auto_locking_order:自动锁单； auto_grab_order:自动抢单；support_locking_order:扶持师傅锁单
     */
    private String grabLabel;

    /**
     * 抢单状态: wait_confirm:待确认；canceled:已取消;confirmed:已确认; abnormal-异常
     */
    private String grabStatus;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 抢单截止时间
     */
    private Date endTime;

    /**
     * 取消锁单时间
     */
    private Date cancelLockTime;

    /**
     * 抢单时间
     */
    private Date grabTime;

    /**
     * 抢单价格
     */
    private BigDecimal grabPrice;

    /**
     * 备注
     */
    private String note;

    /**
     * 删除状态(1:删除0:未删除)
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
