package com.wanshifu.order.push.request.push;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2019/8/20 16:20
 */
@Data
public class ResendOrderPushRqt {

    /**
     * 订单ID
     */
    @NotNull
    @Min(1)
    private Long masterId;

    /**
     * 师傅的服务区域【更新前】 （新地址库id）
     */
    //@NotEmpty
    private String oldServeThirdDivisionIds = "";

    /**
     * 师傅的服务区域【更新后】（新地址库id）
     */
    //@NotEmpty
    private String newServeThirdDivisionIds = "";

    /**
     * 师傅的服务区域【更新前】 （新地址库id）
     */
    //@NotEmpty
    private String oldServeFourthDivisionIds = "";

    /**
     * 师傅的服务区域【更新后】（新地址库id）
     */
    //@NotEmpty
    private String newServeFourthDivisionIds = "";

    /**
     * 师傅移除的技能
     */
    private String removeTechnologyIds;

    /**
     * 移除后师傅已选择技能
     */
    private String selectedTechnologyIds;

    /**
     * 推单数据省下级地址id
     * (当前业务传订单区域id或者师傅区域id就行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    private List<Long> provinceNextIdList;

}
