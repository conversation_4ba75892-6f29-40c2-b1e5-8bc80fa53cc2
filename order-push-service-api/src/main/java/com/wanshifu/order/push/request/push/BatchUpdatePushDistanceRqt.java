package com.wanshifu.order.push.request.push;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 :  批量更新师傅推单距离和经纬度.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2022-06-14 09:42
 */
@Data
public class BatchUpdatePushDistanceRqt {

    /**
     * 师傅ID
     */
    @NotNull
    @Min(1)
    private Long masterId;

    /**
     * 师傅经度
     */
    @NotNull
    private BigDecimal masterLongitude;

    /**
     * 师傅纬度
     */
    @NotNull
    private BigDecimal masterLatitude;

    @NotNull
    private List<MasterAddressInfoList> masterAddressInfoList;

    @Data
    @Validated
    public static class MasterAddressInfoList {

        /**
         * 推送id
         */
        @NotNull
        private Long pushId;

        /**
         * 师傅常驻地址和客户之间的路径规划距离
         */
        @NotNull
        private Long orderId;

        /**
         * 师傅常驻地址和客户之间的路径规划距离
         */
        private Long pushDistance = 0L;
        /**
         * 推单距离类型 1：导航距离(默认) 2:直线距离
         */
        private Integer pushDistanceType;

    }
}