package com.wanshifu.order.push.api;

import com.wanshifu.order.push.request.push.CreateSmartSortPositionConfigRqt;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 17:37
 */
@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/smartSort/positionConfig")
public interface SmartSortPositionConfigApi {

    /**
     * 创建或者更新配置
     * @param createRqt
     */
    @PostMapping("/insertOrUpdateConfig")
    void insertOrUpdateConfig(@Valid @RequestBody CreateSmartSortPositionConfigRqt createRqt);

    /**
     * 删除配置
     * @param deleteCityIds
     */
    @PostMapping("/deleteConfig")
    void deleteConfig(@RequestBody List<Integer> deleteCityIds);
}
