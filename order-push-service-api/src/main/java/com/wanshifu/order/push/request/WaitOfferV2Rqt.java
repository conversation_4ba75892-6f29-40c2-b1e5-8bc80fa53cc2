package com.wanshifu.order.push.request;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2019/8/21 16:16
 */
@Data
public class WaitOfferV2Rqt {

    /**
     * 师傅ID
     */
    @NotNull
    @Min(value = 1L)
    private Long masterId;

    /**
     * 排序规则(newOrder:新单,arrival:已到货,pushDistance:推单距离,residentDistance: 常驻地距离,smart:智能排序)
     */
    @ValueIn("newOrder,arrival,pushDistance,residentDistance,smart")
    private String sortQueryType;
    /**
     * 下单人id
     */
    private Long fromAccount;
    /**
     * 过滤的订单--
     */
    private Long orderId;

    /**
     * 区域ID Long
     */
    private List<Number> divisionId;

    /**
     * 指派模式(2:发布任务,3:直接指派,4:一口价,5:预付款,6:意向单) Integer
     */
    private List<Number> appointType;

    /**
     * 订单来源,1:商家,2:总包,3:宜家,4:家庭 Integer
     */
    private List<Number> orderFrom;

    /**
     * 推送来源,1:智能推单,2:ocs后台
     */
    private Integer pushFrom;

    /**
     * 休息开始时间pushFrom为2时必传   师傅休息中加急单查询
     */
    private Date restStartTime;

    /**
     * 技能类型集合,1:清洁/保养/治理/美缝,4:配送并安装,5:维修服务,6:家装施工,8:安装服务,10:测量服务,13:定制家具/门类/测量/安装,16:管道疏通,17:搬运服务,18:拆旧服务,19:房屋维修
     */
    //@ValueIn(value = "1,4,5,6,8,10,13,16,17,18,19,20") Integer
    private List<Number> techniqueTypeId;

    /**
     * 订单类目id
     */
    private List<Number> categoryId;

    /**
     * 是否到货,1:已到货,2:未到货 非必传
     */
    //@ValueIn(value = "1,2")
    private List<Number> isArrived;

    /**
     * 当前页数，默认1
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数，默认5
     */
    private Integer pageSize = 5;

    /**
     * 城市师傅ID(二级地址) ---v7.11顺路单
     *
     * @return
     */
    private Long cityId;

    /**
     * 推单标识 0：正常推单 1：附近推单 4： 附近红包单 5：附近更多
     */
    private Integer pushFlag;

    /**
     * 菜单类别 1:指派专区, 2:悬赏分区,3：必接分区, 4: 未看分区，5：金牌维修师傅专区, 6: 技能验证单,7: 专属好单分区，8：全时专属单分区
     */
    private Integer menuCategory;

    /**
     * 指派专区竞争少标识(0:否;1:是)
     */
    private Integer lessContendFlag;

    /**
     * 推单数据省下级地址id
     * (当前业务传订单区域id或者师傅区域id就行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    /**
     * 样板城市订单师傅身份
     * 1：主力师傅
     * 2：储备师傅
     * 3.普通师傅
     */
    private Integer tmplCityMasterRole;


    /**
     * 订单标签：1：未看订单, 2：必接订单 ,3:暂无报价
     */
    private List<Number> orderFlags;


    /**
     * 师傅类型，
     * toc:c端师傅
     * tob:b端师傅
     */
    private String masterSourceType;


}
