package com.wanshifu.order.push.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 查询指定下单人账号的师傅推单记录
 */
@Data
public class GetMasterOrderPushByAccountRqt {

    /**
     * 师傅ID
     */
    @NotNull
    private Long masterId;

    /**
     * 省下级地址ID
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    /**
     * 下单人账号id
     */
    @NotNull
    private Long accountId;

    /**
     * 下单人账号类型
     */
    @NotEmpty
    private String accountType;
}
