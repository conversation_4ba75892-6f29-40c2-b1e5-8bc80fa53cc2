package com.wanshifu.order.push.domains.dto;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


@Data
public class OrderAwardDTO {


    private Long orderAwardId;


    private Long orderId;

    /**
     * 奖励类型,timer_order:定时单
     */
    private String awardType;

    /**
     * 奖励金额
     */
    private BigDecimal awardAmount;

    /**
     * 奖励状态,0:未奖励,1:已奖励
     */
    private Integer awardStatus;

    /**
     * 奖励时间
     */
    private Date awardTime;

    /**
     * 删除状态(1:删除 0:未删除)
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}