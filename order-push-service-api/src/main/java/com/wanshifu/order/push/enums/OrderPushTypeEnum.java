package com.wanshifu.order.push.enums;

import lombok.Getter;

@Getter
public enum OrderPushTypeEnum {

    NEW_MODEL_CORE_MASTER_PUSH("new_model_core_master_push", "样板城市推单-主力师傅"),

    NEW_MODEL_RESERVE_MASTER_PUSH("new_model_reserve_master_push", "样板城市推单-储备师傅"),

    AGENT_PUSH("agent_push", "代理商推单"),

    FAMILY_AGREEMENT_MASTER_PUSH("family_agreement_master_push", "家庭协议师傅推单"),

    NORMAL_PUSH("normal_push", "普通推单");

    private final String pushType;
    private final String desc;

    OrderPushTypeEnum(String desc, String pushType) {
        this.desc = desc;
        this.pushType = pushType;
    }
}
