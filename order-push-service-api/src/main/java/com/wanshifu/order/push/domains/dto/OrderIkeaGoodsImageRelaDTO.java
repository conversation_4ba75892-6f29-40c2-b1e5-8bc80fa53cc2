package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderIkeaGoodsImageRelaDTO {
    /**
     * 宜家订单商品图片ID，主键自增
     */
    private Long ikeaGoodsImageId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 宜家订单商品ID
     */
    private Long orderIkeaGoodsId;

    /**
     * 图片AID
     */
    private Long imageAid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}