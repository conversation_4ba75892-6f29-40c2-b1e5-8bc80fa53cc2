package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class GetInterfereOrderPushListRqt {

    /**
     * 全局订单id
     */
    @NotNull
    @Min(1L)
    private Long globalOrderTraceId;

    /**
     * 返回数量限制，1~1000
     */
    @NotNull
    @Min(1)
    @Max(1000)
    private Integer returnLimit;

    /**
     * 排序类型，1: 降序，2: 升序, 默认按评分降序
     */
    private Integer sortType = 1;
}
