package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 描述 :  查询师傅待报价列表可筛选订单类目request.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2022-07-18 14:45
 */
@Data
public class GetMasterCategorySelectorRqt {

    /**
     * 师傅ID
     */
    @NotNull
    @Min(1)
    private Long masterId;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}