package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024-02-21 14:06
 * @Description
 * @Version v1
 **/
@Data
public class OrderExtraDataDTO {
    private Long orderExtraId;
    private Long orderId;
    private String buyerName;
    private String projectManagerName;
    private String buyerPhone;
    private String buyerAddress;
    private Integer hasLift;
    private Integer floorNum;
    private String clearanceType;
    private String clearanceNo;
    private Date clearanceCancelTime;
    private Integer needConstructConfirm;
    private Date expectCompleteTime;
    private BigDecimal expectOfferMinPrice;
    private BigDecimal expectOfferMaxPrice;
    private String buyerNote;
    private String buyerVoiceData;
    private String contactName;
    private String contactPhone;
    private String emergencyContactName;
    private String emergencyContactPhone;
    private Integer needConfirmFixOnWall;
    private Integer snType;
    private Integer isSendBack;
    private String buyerHouseNumber;
    private Integer buyerGender;
    private Integer destinationGender;
    private Long destinationDivisionId;
    private String destinationAddress;
    private String destinationHouseNumber;
    private String destinationContactPhone;
    private String destinationContactName;
    private Date expectDoorInStartDate;
    private Date expectDoorInEndDate;
    private BigDecimal buyerAddressLongitude;
    private BigDecimal buyerAddressLatitude;
    private BigDecimal destinationLongitude;
    private BigDecimal destinationLatitude;
    private String mapType;
    private Integer destinationHasLift;
    private Integer destinationFloorNum;
    private Integer isExemptInformationServeFee;
    private String measureWay;
    private BigDecimal platformServiceFeeToAccount;
    private Integer personalizedReceiptFlag;
    private Integer timerFlag;
    private Integer emergencyOrderFlag;
    private Integer onTimeOrderFlag;
    private Integer warrantyType;
    private String orderPushExcludeMasterIds;
    private Integer isParts;
    private Integer lessTransportCapacityFlag;
    private String demolishType;
    private String demolishGoods;
    private Integer isSendBackOld;
    private String sendBackOldGoods;
    private String generalField;
    private String expectDoorInDate;
    private String jsonGeneralField;

}
