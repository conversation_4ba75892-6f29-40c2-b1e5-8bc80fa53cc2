package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @title wshifu-master-order-service
 * @date 2021/7/16 15:59
 */
@Data
public class WaitOfferNoPageRqt {

    /**
     * 师傅ID
     */
    @NotNull
    @Min(value = 1L)
    private Long masterId;

    /**
     * 订单区域id集合
     */
    private List<Long> thirdDivisionIds;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    /**
     * 样板城市订单师傅身份
     * 1：主力师傅
     * 2：储备师傅
     * 3.普通师傅
     */
    private Integer tmplCityMasterRole;


}
