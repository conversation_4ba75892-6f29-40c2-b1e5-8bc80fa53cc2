package com.wanshifu.order.push.request.push;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/8 10:48
 */
@Data
public class UpdatePullViewTimeRqt {

    @NotNull
    private List<Long> orderIdList;

    @NotNull
    private Long masterId;

    @NotNull
    private Date viewTime;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}
