package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024-02-21 14:03
 * @Description
 * @Version v1
 **/
@Data
public class OrderBaseDTO {

    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 下单人账号ID
     */
    private Long accountId;

    /**
     * 下单子账号ID
     */
    private Long subAccountId;

    /**
     * 下单人账号类型
     */
    private String accountType;

    /**
     * 订单全局ID
     */
    private Long globalOrderTraceId;

    /**
     * 来源(客户端)site:网站,enterprise_system:总包外部订单,weixin:微信,ikea:宜家,thirdpart:第三方平台
     */
    private String orderFrom;

    /**
     * 订单类目id---商品类目
     */
    private Integer categoryId;

    /**
     * 下单服务类型id(config_order_server_type.id)
     */
    private Integer serveTypeId;

    /**
     * 服务类型(1:送货到楼下,2:送货到家,3:送货到家并安装,4:安装,5:维修,6:返货,7:保养,8:清洗,9:测量)
     */
    private Integer serveType;

    /**
     * 订单状态(trading-交易进行中,close-订单关闭,finish-交易完成)
     */
    private String orderStatus;

    /**
     * 下单地区ID
     */
    private Integer regionId;


    /**
     * 下单地区ID--三级
     */
    private Long thirdDivisionId;



    /**
     * 下单地区ID（四级地址）
     */
    private Long fourthDivisionId;

    /**
     * 订单标签(空字符串:默认,rigorous_selection:严选)
     */
    private String orderLabel;

    /**
     * 订单下单时间
     */
    private Date orderCreateTime;

    /**
     * 订单修改时间
     */
    private Date orderModifyTime;

    /**
     * 风控检测状态(wait_check:待检测,check_normal:检测正常,check_abnormal:检测异常)
     */
    private String riskControlStatus;

    /**
     * 推送师傅数量
     */
    private Integer pushNumber;

    /**
     * 是否天猫宜家订单标识(0:否,1:是)
     */
    private Integer isTmallIkeaFlag;

    /**
     * 服务集合(多个逗号隔开)
     */
    private String serveIds;

    /**
     * 服务集合(多个逗号隔开)
     */
    private String serviceIds;


    /**
     * 服务一级服务id集合
     */
    private String serveLevel1Ids;


    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 下单服务绑定的技能快照ID集合(1,3|1,4|2,3|2,4)
     */
    private String bindingTechnologyIds;

    /**
     * 专属订单标记（1专属订单，2转换后的普通订单，0普通订单，默认0）
     */
    @Deprecated
    private Integer exclusiveFlag;

    /**
     * 标签名,exclusive:专属,direct_appointment:直约,preferred:优选,contract:合约(专属),brand:品牌
     */
    @Deprecated
    private String recruitTagName;


    /**
     * 订单服务版本(0-默认版本 ; 1-动态版本)
     */
    private Integer orderServeVersion;

    /**
     * 订单操作版本（0-默认创建 ; 修改递增+1）
     */
    private Integer orderOperationVersion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
