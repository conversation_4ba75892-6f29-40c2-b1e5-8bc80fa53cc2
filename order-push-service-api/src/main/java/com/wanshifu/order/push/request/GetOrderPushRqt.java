package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/11 13:39
 */
@Data
public class GetOrderPushRqt {
    /**
     * 订单ID
     */
    @NotNull
    private Long orderId;

    /**
     * 师傅ID
     */
    @NotNull
    private Long masterId;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}
