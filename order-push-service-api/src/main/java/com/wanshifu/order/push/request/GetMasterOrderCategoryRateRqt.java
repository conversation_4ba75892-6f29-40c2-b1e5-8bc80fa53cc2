package com.wanshifu.order.push.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  查询师傅待报价列表订单类目占比.
 *
 * 当前arms上发现没有外部调用，如果有外部调用，则需保证该批师傅是同一个城市的师傅，因为order_push分表是按城市分表的
 * 目前只有在推单时计算排序时使用，推单时能够保证该批师傅都是同一个城市的师傅。
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-07-06 16:22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetMasterOrderCategoryRateRqt {
    @NotEmpty
    private List<Long> masterIdList;

    /**
     * 推单数据省下级地址id
     * (当前业务传订单区域id或者师傅区域id就行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}