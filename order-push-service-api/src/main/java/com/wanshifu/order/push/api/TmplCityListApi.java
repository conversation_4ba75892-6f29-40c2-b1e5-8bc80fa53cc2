package com.wanshifu.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderPushLogRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqtV2;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderPushLogResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterRespV2;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25 13:50
 */
@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/tmplCity/list")
public interface TmplCityListApi {

    /**
     * 分页获取家庭样板城市订单推荐师傅列表
     * @param rqt ListTmplCityOrderRecommendMasterRqt
     * @return SimplePageInfo<ListTmplCityOrderRecommendMasterResp>
     */
    @PostMapping("/orderRecommendMasterList")
    SimplePageInfo<ListTmplCityOrderRecommendMasterResp> orderRecommendMasterList(@Valid @RequestBody ListTmplCityOrderRecommendMasterRqt rqt);

    /**
     * 获取家庭样板城市订单推荐师傅列表，按score倒序，限制返回1000条
     * @param rqt ListTmplCityOrderRecommendMasterRqtV2
     * @return List<ListTmplCityOrderRecommendMasterRespV2>
     */
    @PostMapping("/orderRecommendMasterListV2")
    List<ListTmplCityOrderRecommendMasterRespV2> orderRecommendMasterListV2(@Valid @RequestBody ListTmplCityOrderRecommendMasterRqtV2 rqt);

    /**
     * 查询家庭样板城市订单推单记录日志
     * @param rqt ListTmplCityOrderPushLogRqt
     * @return List<ListTmplCityOrderPushLogResp>
     */
    @PostMapping("/orderPushLogList")
    List<ListTmplCityOrderPushLogResp> orderPushLogList(@Valid @RequestBody ListTmplCityOrderPushLogRqt rqt);

}
