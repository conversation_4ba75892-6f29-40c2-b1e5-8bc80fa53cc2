package com.wanshifu.order.push.request.push;

import com.wanshifu.order.push.enums.PushScenarioType;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2019/8/20 16:20
 */
@Data
public class PushedToMasterRqt {

    /**
     * 订单ID
     */
    @NotNull
    @Min(1)
    private Long orderId;

    /**
     * 推单模式
     *   1.专属师傅推单模式
     *     pre_exclusive: 专属普通模式
     *     pre_exclusive_single :专属单笔模式。
     *     pre_exclusive_single_transfer_grab_offer_price : 专属转一口价抢单
     *   2.品牌师傅推单模式
     *     brand: 品牌师傅
     *   3.代理商推单模式
     *     agent: 代理商模式
     *   4.订单包推单模式
     *     package_order: 订单包模式
     *   5.普通推单模式
     *     normal:普通推单模式 (默认)
     *     new_model:样板城市推单
     *     new_model_single:样板城市推单（只推主力师傅）
     *
     */
    private String pushMold = "normal" ;

    /**
     * 推单场景
     * @see com.wanshifu.order.push.enums.PushScenarioType
     * /
     */
    @NotNull
    private PushScenarioType pushScenarioType;

    /**
     * 推送师傅ID集合
     */
    @Valid
    @NotNull
    @Size(min = 1, max = 100)
    private List<MasterAddressInfo> masterAddressInfoList;

    /**
     * 推送的地址级别（3：三级地址推单，4：四级地址推单）
     */
    @NotNull
    @Value("3,4")
    private Integer pushDivisionLevel;

    /**
     * 推送状态 0：正常推单 1：附近推单
     */
    private Integer pushFlag = 0;

    /**
     * 是否按照师傅和订单距离推送推送 0:否,1:是
     */
    private Integer accordingDistancePushFlag = 0;


    /**
     * 代理商推单师傅
     */
    private String agentMasterList;

    /**
     * 专属订单标签
     */
    @Valid
    private ExclusiveOrderLabel exclusiveOrderLabel;

    /**
     * 推单数据省下级地址id,
     * 手动推单需校验推单师傅所在区域与订单区域要在同一个城市
     * (当前业务传订单区域id,order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    /**
     * 师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    private String masterSourceType;


}
