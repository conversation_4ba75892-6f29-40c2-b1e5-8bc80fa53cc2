package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 描述 :  分页查询 订单已推师傅id.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-07-13 14:25
 */
@Data
public class GetWaitOfferMasterIdsByOrderIdRqt {
    /**
     * 订单id
     */
    @NotNull
    private Long orderId;

    /**
     * 当前页数，默认1
     */
    @NotNull
    private Integer pageNum = 1;

    /**
     * 每页记录数，默认5
     */
    @NotNull
    private Integer pageSize = 500;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     *
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

}