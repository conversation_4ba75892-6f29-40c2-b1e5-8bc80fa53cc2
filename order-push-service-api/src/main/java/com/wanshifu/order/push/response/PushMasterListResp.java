package com.wanshifu.order.push.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 查询推单师傅数据
 * <AUTHOR>
 */
@Data
public class PushMasterListResp {

    /**
     * 师傅id
     */
    public Long masterId;


    /**
     * 推单距离（师傅常住地和客户地址直线距离）,单位：米
     */
    public Long distance;


    /**
     * 师傅常住地纬度
     */
    private BigDecimal masterLatitude;

    /**
     * 师傅常住地经度
     */
    private BigDecimal masterLongitude;

    /**
     * 是否查看订单,1: 已查看，0：未查看
     */
    private Integer isViewOrder;


}
