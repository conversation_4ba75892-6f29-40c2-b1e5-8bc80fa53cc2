package com.wanshifu.order.push.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-11-28 17:30
 * @Description
 * @Version v1
 **/
@Data
public class WaitOfferCountResp {
    /**
     * 分析结果 1-20 2-10
     */
    private List<CountResp> countRespList;


    @Data
    public static class CountResp{
        private Integer statisticsMode;
        private Integer count;

        public CountResp(Integer statisticsMode, Integer count) {
            this.statisticsMode = statisticsMode;
            this.count = count;
        }
    }
}
