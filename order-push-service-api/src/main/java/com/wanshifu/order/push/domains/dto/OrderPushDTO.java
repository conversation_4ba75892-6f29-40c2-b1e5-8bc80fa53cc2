package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 10:38
 */
@Data
public class OrderPushDTO {
    /**
     * 主键ID
     */
    private Long pushId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 师傅ID
     */
    private Long masterId;

    /**
     * 指派类型(2:发布任务,3:直接雇佣,4:一口价)
     */
    private Integer appointType;

    /**
     * 来源客户端(site:网站,backend:后台,weixin:微信,ikea:宜家)
     */
    private String orderFrom;

    /**
     * 下单人账号类型
     */
    private String accountType;
    /**
     * 下单账号id（）
     */
    private Long fromAccount;

    /**
     * 订单标签(空字符串:默认,rigorous_selection:严选)
     */
    private String orderLabel;

    /**
     * 报价数量
     */
    private Integer offerNumber;

    /**
     * 备注
     */
    private String note;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 报价时间
     */
    private Date offerTime;

    /**
     * 雇佣时间
     */
    private Date hireTime;

    /**
     * 报价限制(1:可以报价,2:不可报价)
     */
    private Integer limitOffer;


    /**
     * 推送的地址级别(3:三级地址推单,4:四级地址推单)
     */
    private Integer pushDivisionLevel;


    /**
     * 删除状态(1:删除0:未删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 师傅第一次拉取待报价订单时间
     */
    private Date firstPullTime;

    /**
     * 师傅第一次查看待报价订单详情时间
     */
    private Date firstViewTime;

    /**
     * 截止报价时间
     */
    private Date stopOfferTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 到货状态(1:已到货,2:未到货),默认0
     */
    private Integer isArrived;

    /**
     * 是否是意向单(1:是,0:不是),默认0
     */
    private Integer isIntention;

    /**
     * 订单区域(三级)
     */
    private Long orderDivisionId;

    /**
     * 师傅纬度
     */
    private BigDecimal masterLatitude;

    /**
     * 师傅经度
     */
    private BigDecimal masterLongitude;

    /**
     * 推单距离(当时推单师傅和客户得距离),单位米
     */
    private Long pushDistance;

    /**
     * 推单距离类型 1：导航距离(默认) 2:直线距离
     */
    private Integer pushDistanceType;

    /**
     * 推送来源(1:智能推单,2:ocs后台)
     */
    private Integer pushFrom;

    /**
     * 专属订单标记（1专属订单，2转换后的普通订单，0普通订单，默认0）
     */
    private Integer exclusiveFlag;

    /**
     * 技能类型集合,1:清洁/保养/治理/美缝,4:配送并安装,5:维修服务,6:家装施工,8:安装服务,10:测量服务,13:定制家具/门类/测量/安装,16:管道疏通,17:搬运服务,18:拆旧服务,19:房屋维修
     */
    private String techniqueTypeIds;

    /**
     * 订单类目id
     */
    private Integer categoryId;

    /**
     * 推单标识 0：正常推单 1：附近推单
     */
    private Integer pushFlag;

    /**
     * 菜单类别 0：默认 1：指派专区
     */
    private Integer menuCategory;

    /**
     * 加急单标识 0、普通订单 1、加急单 2、加急单转成普通订单
     */
    private Integer emergencyOrderFlag;

    /**
     * 合作商标识 0、非合作商 1、家庭合作商
     */
    private Integer agentOrderFlag;

    /**
     * 是否拉取订单距离,1:是,0:否
     */
    private Integer isPullOrderDistance;

    /**
     * 是否按照师傅和订单距离推送 1:是,0:否
     */
    private Integer accordingDistancePushFlag;


    /**
     * 是否按照师傅技能推送 0:否,1:是
     */
    private Integer accordingTechnologyPushFlag;

    /**
     * 竞争少标识(0:否;1:是)
     */
    private Integer lessContendFlag;

}