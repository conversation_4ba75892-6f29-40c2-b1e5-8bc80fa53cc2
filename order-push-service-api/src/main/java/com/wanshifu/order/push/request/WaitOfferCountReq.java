package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023-11-28 17:28
 * @Description
 * @Version v1
 **/
@Data
public class WaitOfferCountReq extends BaseWaitOfferCount {


    /**
     * 统计模式 0-推荐 1-附近更多  2-合作专区 3-我的菜单 支持多个查询，默认查询全部 6-悬赏分区,7-必接分区,8-金牌维修师傅专区,9:技能专属单分区，10：专属好单分区
     * （非必填）
     */
    private Set<Integer> statisticsMode;

    /**
     * 自定义菜单条件
     */
    private CustomizeMenusCondition customizeMenusCondition;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     *
     */
    @Min(value = 1L)
    private Long provinceNextId;


    @Data
    public static class CustomizeMenusCondition{

        private List<Integer> appointTypes;
        private List<Integer> techniqueTypeIds;
        private List<Integer> goodsIsArrived;
        private List<Integer> orderAccountLabel;
        private List<Long> divisionIds;
        private List<Integer>  categoryIds;
        private Integer pushFlag;
    }
}
