package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/28 17:36
 */
@Data
public class OrderBaseCompositeDTO {
/******************订单基础信息***********************/
    /**
     * 订单基础信息
     */
    private OrderBaseDTO orderBaseDTO;

    /**
     * 订单扩展信息
     */
    private OrderExtraDataDTO orderExtraDataDTO;

    /**
     * 订单物流信息
     */
    private OrderLogisticsInfoDTO orderLogisticsInfoDTO;

    /**
     * 订单指派信息
     */
    private OrderGrabDTO orderGrabDTO;

    /******************订单其他信息************************/

    /**
     * 订单初始费用
     */
    private OrderInitFeeDTO orderInitFeeDTO;

    /**
     * 订单好评返现信息
     */
    private OrderRateAwardDTO orderRateAwardDTO;

    /**
     * 订单文件关联信息
     */
    private List<OrderFileRelaDTO> orderFileRelaDTOList;

    /**
     * 订单奖励信息
     */
    private OrderAwardDTO orderAwardDTO;

    /**
     * 订单工程类数据--工程单特有
     */
    private OrderEpcGenreDataDTO orderEpcGenreDataDTO;

    /**
     * 加急单
     */
    private EmergencyOrderDTO emergencyOrderDTO;

    /**
     * 订单配件信息
     */
    private List<OrderPartsDTO> orderPartsDTOListList;

    /**
     * 订单关单信息
     */
    private  OrderCloseDTO orderCloseDTO;
}
