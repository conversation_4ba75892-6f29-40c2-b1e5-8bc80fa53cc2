package com.wanshifu.order.push.response;

import com.wanshifu.order.push.domains.dto.*;
import lombok.Data;

import java.util.List;

/**
 * IOC(智能运营)活动待报价-展示数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/8/25 15:43
 */
@Data
public class IocWaitOfferResp {

    /**
     * 指派类型(2:发布任务,3:直接雇佣,4:一口价,5:预付款
     */
    private Integer appointType;

    /**
     * 订单基础信息
     */
    private OrderBaseDTO orderBaseDTO;

    /**
     * 订单额外信息
     */
    private OrderExtraDataDTO orderExtraDataDTO;

    /**
     * 订单物流信息
     */
    private OrderLogisticsInfoDTO orderLogisticsInfoDTO;

    /**
     * 订单初始费用
     */
    private OrderInitFeeDTO orderInitFeeDTO;

    /**
     * 宜家订单商品信息组合集合
     */
    private List<IkeaOrderGoodsCompositeDTO> ikeaOrderGoodsCompositeDTOS;

    /**
     * 订单商品信息组合集合
     */
    private List<OrderGoodsCompositeDTO> orderGoodsCompositeDTOS;

    /**
     * 订单服务属性信息
     */
    private List<OrderServiceAttributeInfoDTO> orderServiceAttributeInfoDTOS;

}
