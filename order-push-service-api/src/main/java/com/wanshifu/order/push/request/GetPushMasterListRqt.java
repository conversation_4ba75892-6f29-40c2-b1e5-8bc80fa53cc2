package com.wanshifu.order.push.request;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import javax.persistence.Transient;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 查询推单师傅数据
 * <AUTHOR>
 */
@Data
public class GetPushMasterListRqt {

    /**
     * 全局订单i
     */
    @NotNull
    @Min(1L)
    private Long globalOrderTraceId;


    /**
     * 订单客户地址与师傅常住地直线距离，单位为米
     */
    private Integer distance;


    @Min(0)
    public Integer pageNum = 1;


    @Max(500)
    public Integer pageSize = 10;


}
