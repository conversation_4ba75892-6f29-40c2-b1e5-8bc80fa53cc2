package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
public class OrderGoodsDoorHoleInfoDTO {
    /**
     * 门吊ID
     */
    private Long doorHoleInfoId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单商品ID
     */
    private Long orderGoodsId;

    /**
     * 门洞编号
     */
    private String doorHoleNo;

    /**
     * 门洞宽度
     */
    private Integer doorHoleWide;

    /**
     * 门洞高度
     */
    private Integer doorHoleHigh;

    /**
     * 门洞厚度
     */
    private Integer doorHoleThickness;

    /**
     * 测量门洞尺寸(json格式)
     */
    private String doorHoleSize;

    /**
     * 附属配件名称(door_head_plate:门头板,fixed_transom:摇头窗,none:无门头板/无摇头窗)
     */
    private String auxiliaryPartName;

    /**
     * 附属配件高度
     */
    private Integer auxiliaryPartHigh;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}