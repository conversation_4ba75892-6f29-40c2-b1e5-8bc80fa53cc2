package com.wanshifu.order.push.api;

import com.wanshifu.order.push.domains.dto.OrderDistanceDTO;
import com.wanshifu.order.push.request.special.*;
import com.wanshifu.order.push.response.special.GetOrderNearbyNumberResp;
import com.wanshifu.order.push.response.special.SpecialOrderCountResp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2024-02-02 16:43
 * @Description
 * @Version v1
 **/
@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/specialOrder/resource")
public interface SpecialOrderResourceApi {

    /**
     * 特殊订单统计（包含顺路单、附近单）
     * @param rqt
     * @return AsyncNewWaitOfferResp
     */
    @PostMapping("count")
    SpecialOrderCountResp specialOrderCount(@Valid @RequestBody SpecialOrderCountDTO rqt);



    /**
     * 获取报价订单附近单数量
     * @param rqt
     * @return
     */
    @PostMapping("getOrderNearbyNumber")
    GetOrderNearbyNumberResp getOrderNearbyNumber(@RequestBody @Valid GetOrderNearbyNumberDTO rqt);

    /**
     * 检查是否满足订单距离配置
     * @param checkNearbyOrderDistanceConfigRqt
     * @return
     */
    @PostMapping("checkNearbyOrderDistanceConfig")
    Boolean checkNearbyOrderDistanceConfig(@RequestBody @Valid CheckNearbyOrderDistanceConfigRqt checkNearbyOrderDistanceConfigRqt);


    /**
     * 顺路单过滤配置条件
     * @param checkSideOrderDistanceConfigRqt
     * @return
     */
    @PostMapping("checkSideOrderDistanceConfig")
    Boolean checkSideOrderDistanceConfig(@Valid @RequestBody CheckSideOrderDistanceConfigRqt checkSideOrderDistanceConfigRqt);

    /**
     * 查询订单距离信息通过订单id
     * @param getOrderDistanceRqt
     * @return
     */
    @PostMapping("selectOrderDistanceByOrderId")
    OrderDistanceDTO selectOrderDistanceByOrderId(@RequestBody @Valid GetOrderDistanceRqt getOrderDistanceRqt);
}
