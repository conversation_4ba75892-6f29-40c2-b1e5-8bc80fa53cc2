package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
public class InfoOrderExtraDataDTO {
    /**
     * PK
     */
    private Long orderExtraId;

    /**
     * 师傅订单ID
     */
    private Long orderId;

    /**
     * 你的称呼
     */
    private String contactName;

    /**
     * 性别（male：男，female：女）
     */
    private String gender;


    /**
     * 客户手机号码
     */
    private String contactPhone;

    /**
     * 上门地址
     */
    private String address;

    /**
     * 上门日期
     */
    private Date visitDate;

    /**
     * 推送师傅数量
     */
    private Integer pushNumber;

    /**
     * 意向师傅数量
     */
    private Integer intentionNumber;

    /**
     * 拨打电话师傅数量
     */
    private Integer callNumber;

    /**
     * 施工总量
     */
    private Integer totalConstruction;

    /**
     * 施工总量单位
     */
    private String totalConstructionUnit;

    /**
     * 需求描述
     */
    private String requirement;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}