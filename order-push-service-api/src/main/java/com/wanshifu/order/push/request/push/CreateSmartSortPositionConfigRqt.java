package com.wanshifu.order.push.request.push;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/25 17:41
 */
@Data
public class CreateSmartSortPositionConfigRqt {

    /**
     * 新增的城市配置
     */
    private List<Integer> insertOrUpdateCityIds;

    /**
     * 移除的城市配置
     */
    private List<Integer> deleteCityIds;

    /**
     * 干预位置类型，1：固定位置，2：动态位置
     */
    @NotNull
    @ValueIn("2")
    private Integer positionType;

    /**
     * 动态位置参数，每？个单
     */
    private Integer dynamicEveryFewSize;

    /**
     * 动态位置参数，第？个位置
     */
    private Integer dynamicPositionIndex;
}
