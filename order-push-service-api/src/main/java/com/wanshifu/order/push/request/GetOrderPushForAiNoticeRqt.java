package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 查询订单推送师傅列表（提供给AI通知接单使用）
 */
@Data
public class GetOrderPushForAiNoticeRqt {

    @NotNull
    private Long orderId;

    @NotNull
    @Max(2000)
    private Integer limitCount;


    /**
     * 省下级地址id
     * (当前业务传市、区、街道级别的地址都行)
     */
    @Min(value = 1L)
    @NotNull
    private Long provinceNextId;
}
