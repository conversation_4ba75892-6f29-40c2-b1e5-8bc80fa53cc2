package com.wanshifu.order.push.request.push;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-09-22 09:22
 * @Description
 * @Version v1
 **/
@Data
public class MasterAddressInfo {

    /**
     * 推送师傅ID
     */
    @Min(1)
    @NotNull
    private Long masterId;

    /**
     * 师傅经度
     */
    private BigDecimal masterLongitude;

    /**
     * 师傅纬度
     */
    private BigDecimal masterLatitude;

    /**
     * 师傅常驻地址和客户之间的路径规划距离
     */
    private Long pushDistance;

    /**
     * 推单距离类型 1：导航距离(默认) 2:直线距离
     */
    private Integer pushDistanceType;

    /**
     * 是否按照技能推单 1:是 0:否
     */
    private Integer accordingTechnologyPushFlag = 1;


    /**
     * 标签名称: contract：直约 ，brand:品牌
     */
    private String tagName;

    /**
     * 标签扩展信息
     */
    private String tagExpand;


    /**
     * 招募id
     */
    private String recruitId;

    /**
     * 推送标签
     * main_master:主力师傅
     * reserve_master:储备师傅
     */
    private String pushTag;

    /**
     * 样板城市订单，该订单师傅排序值
     */
    private Integer sort;

    /**
     * 排序分值
     */
    private BigDecimal score;

    /**
     * 1: 跨城市推单，0：非跨城市推单
     */
    private Integer crossCityPush;


    /**
     * 自动接单顺序
     */
    private Integer autoOfferSort;


    /**
     * 自动报价金额
     */
    private BigDecimal autoPrice;


    /**
     * 必接订单标记
     */
    private Integer mustOrderFlag;


    /**
     * 师傅类别，tob: B端师傅，toc: C端师傅
     */
    private String masterSourceType;

    /**
     * 分时师傅分流标记，0：未分流，1：分流
     */
    private Integer shuntFlag;


    /**
     * 全时/分时师傅标记,0: 未明确身份，1：全时师傅，2：分时师傅
     */
    private Integer masterTimeType;


}
