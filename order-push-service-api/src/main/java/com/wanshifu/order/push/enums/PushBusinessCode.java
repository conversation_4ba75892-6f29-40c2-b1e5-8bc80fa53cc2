package com.wanshifu.order.push.enums;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-04-24 16:41
 * @Description 业务编码
 * @Version v1
 **/
public enum PushBusinessCode {

    //4. 程序信息编码

    //---4xxxxx  校验错误

    //---41xxxx  请求参数校验错误
    REQ_VALIDATION_ERROR(410000,"请求参数校验错误"),

    //---42xxxx  业务校验错误
    BUS_VALIDATION_ERROR(420000,"业务校验错误"),

    //---43xxx  权限校验错误
    ACC_VALIDATION_ERROR(430000,"权限校验错误"),


    //--- 5xxxx 内部异常
    //--- 51xxx 内部接口异常

    INTERFACE_SERVER_ERROR(510000,"内部接口异常"),

    DEAD_LOCK_ERROR(510001, "业务死锁异常需要重试"),
    //----52xxx 外部接口调用异常
    REMOTE_CALL_SERVER_ERROR(520000,"外部接口调用异常"),

    //---6xxxx 忽略异常
    IGNORE_ERROR(600000,"服务忽略异常"),
    //---61xxx mq消息忽略
    MQ_IGNORE_ERROR(610000,"mq消息忽略的异常"),

    //---62xxx mq忽略当前消息
    MQ_FILTER_MESSAGE_ERR0R(620000,"mq忽略掉当前消息")
;

    PushBusinessCode(Integer code, String message) {
        this.code =  code.toString();
        this.message = message;

    }

    public  final String code;

    public final String message;


    //1.基础编码系统(默认01)
    private static final String  BASE_CODE = "01" ;

    //2.FT业务线编码(订单FT:01)
    private static final String FT_CODE = "01";

    //3.服务编码(user-order-service: 01)
    private static final String SERVER_CODE = "01";

    public  String commonPrefix  = String.format("%s%s%s",BASE_CODE , FT_CODE , SERVER_CODE);

    /**
     * 过滤掉飞书预警的code
     */
    public static List<String> filterFeShuWarnBusinessCode = Arrays.asList(
            MQ_IGNORE_ERROR.code,
            MQ_FILTER_MESSAGE_ERR0R.code);

    /**
     * 是否过滤掉mq飞书预警
     * @param code
     * @return
     */
    public static boolean isFilterFeShuWarn(String code){
        return filterFeShuWarnBusinessCode.contains(code);
    }

    /**
     * 是否过滤mq消息
     * @param code
     * @return
     */
    public static boolean isFilterMqMessage(String code) {
        return MQ_FILTER_MESSAGE_ERR0R.code.equals(code);
    }
}
