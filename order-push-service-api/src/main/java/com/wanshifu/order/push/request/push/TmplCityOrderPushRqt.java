package com.wanshifu.order.push.request.push;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 家庭样板城市订单列表查询入参
 * @date 2024/6/19 16:31
 */
@Data
public class TmplCityOrderPushRqt {

    /**
     * 师傅ID
     */
    @NotNull
    @Min(value = 1L)
    private Long masterId;

    /**
     * 当前页数，默认1
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数，默认5
     */
    private Integer pageSize = 5;

    /**
     * 推单数据省下级地址id,待报价列表分表分片字段
     * (当前业务传师傅区域id，order-push-service负责计算省下级地址id)
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    private List<Long> provinceNextIdList;
}
