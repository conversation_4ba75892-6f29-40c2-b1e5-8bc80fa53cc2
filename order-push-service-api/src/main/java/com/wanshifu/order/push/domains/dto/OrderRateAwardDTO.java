package com.wanshifu.order.push.domains.dto;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 订单好评返现表
 */
@Data
public class OrderRateAwardDTO {

    /**
     * 主键id
     */
    private Long orderRateAwardId;

    /**
     * 评价类型,[word:纯文字好评,image:带图好评,video:带视频好评,img_video:带图+视频好评]
     */
    private String rateType;

    /**
     * 好评返现金额初始值
     */
    private BigDecimal rateAwardFee;

    /**
     * 订单id(order_base.order_id)
     */
    private Long orderId;

    /**
     * 雇佣师傅之前得退款状态(0:未退款,1:已退款)
     */
    private Integer applyRefundStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 好评返现备注
     */
    private String rateAwardRemark;

    /**
     * 奖励用户金额
     */
    private BigDecimal customerAwardFee;
}