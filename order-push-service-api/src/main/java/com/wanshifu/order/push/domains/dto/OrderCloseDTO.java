package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
public class OrderCloseDTO implements Serializable {
    private Long closeId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 操作人账号id
     */
    private Long accountId;

    /**
     * 操作人账号类型
     */
    private String accountType;

    /**
     * 操作类型(user:用户关单,enterprise:企业服务商关单,master:师傅主动放弃订单,customer:客服关单,system:系统关单,refund:全额退款/仲裁支持全额退款导致关单)
     */
    private String actionType;

    /**
     * 订单关闭原因
     */
    private String reason;

    /**
     * 关单时间
     */
    private Date closeTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}