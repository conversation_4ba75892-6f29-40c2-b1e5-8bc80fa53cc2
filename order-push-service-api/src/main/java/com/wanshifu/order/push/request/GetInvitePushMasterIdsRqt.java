package com.wanshifu.order.push.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 描述 :  查询订单推荐师傅列表.
 *
 * <AUTHOR> xinze<PERSON>@wshifu.com
 * @date : 2023-07-19 15:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetInvitePushMasterIdsRqt {
    @NotNull
    private Long globalOrderTraceId;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     *
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}