package com.wanshifu.order.push.request.tmplcity;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 16:40
 */
@Data
public class SaveTmplCityOrderRecommendMasterRqt {

    /**
     * 全局订单id
     */
    @NotNull
    @Min(1L)
    private Long globalOrderTraceId;

    /**
     * 订单id
     */
    @NotNull
    @Min(1L)
    private Long orderId;


    private List<Long> provinceNextId;

    /**
     * 推荐师傅列表
     */
    @NotEmpty
    @Size(min = 1, max = 100)
    private List<RecommendMasterInfo> recommendMasterInfoList;

    @Data
    @Validated
    public static class RecommendMasterInfo {

        /**
         * 师傅id
         */
        @NotNull
        @Min(1L)
        private Long masterId;

        /**
         * 该订单师傅排序值
         */
        @NotNull
        private Integer sort;

        /**
         * 排序分值
         */
        @NotNull
        private BigDecimal score;


        /**
         * 师傅来源类型，tob: B端师傅，toc: C端师傅
         */
        private String masterSourceType;

    }
}
