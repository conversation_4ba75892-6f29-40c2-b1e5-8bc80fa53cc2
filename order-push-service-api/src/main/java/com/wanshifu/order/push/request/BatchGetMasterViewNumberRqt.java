package com.wanshifu.order.push.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 描述 :  批量获取订单已查看师傅数量.
 *
 * <AUTHOR> xinze<PERSON>@wshifu.com
 * @date : 2022-06-14 10:24
 */
@Data
public class BatchGetMasterViewNumberRqt {

    /**
     * 订单全局IDs
     */
    @NotNull
    @Size(min = 1,max = 20)
    private List<Long> globalOrderTraceIds;

    /**
     * 下单人账号ID
     */
    @NotNull
    private Long accountId;

    /**
     * 下单人账号类型
     */
    @NotEmpty
    private String accountType;
}