package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @title wshifu-master-order-service
 * @date 2021/7/8 14:33
 */
@Data
public class BatchOrderPushRqt {

    /**
     * 订单ID
     */
    @NotNull
    @Min(1)
    private Long orderId;

    /**
     * 师傅ID
     */
    private List<Long> masterId;

    /**
     * 省下级地址id
     * 目前业务传的是订单区域id，order-push-service计算省下级地址id
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}
