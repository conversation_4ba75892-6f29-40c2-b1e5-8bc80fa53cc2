package com.wanshifu.order.push.enums;

/**
 * 模板城市订单推送日志操作人类型枚举
 * <AUTHOR>
 * @date 2024/8/29 16:06
 */
public enum TmplCityOrderPushLogOperatorTypeEnum {

    /**
     * 系统
     */
    SYSTEM("system", "系统");

    private final String code;

    private final String name;

    TmplCityOrderPushLogOperatorTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
