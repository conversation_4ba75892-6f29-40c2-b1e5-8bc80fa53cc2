package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 获取推送时间【批量】
 *
 * <AUTHOR>
 * Date 2021/7/31 14:13
 */
@Data
public class BatchGetPushTimeRqt {

    @NotNull
    private Long masterId;

    @NotNull
    @Size(min = 1)
    private List<Long> orderIds;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     *
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

}
