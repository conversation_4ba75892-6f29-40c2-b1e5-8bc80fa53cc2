package com.wanshifu.order.push.enums;

/**
 * 模板城市订单推送日志操作事件枚举
 * <AUTHOR>
 * @date 2024/8/29 16:01
 */
public enum TmplCityOrderPushLogEventEnum {

    /**
     * 推送主力和储备师傅
     */
    PUSH_MAIN_AND_RESERVE("push_main_and_reserve", "推送主力和储备师傅"),


    /**
     * 推送众包师傅
     */
    PUSH_NORMAL("push_normal","推送众包师傅");

    private final String code;

    private final String name;

    TmplCityOrderPushLogEventEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
