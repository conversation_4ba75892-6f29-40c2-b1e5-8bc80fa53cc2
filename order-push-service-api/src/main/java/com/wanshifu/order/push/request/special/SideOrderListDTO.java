package com.wanshifu.order.push.request.special;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Title：顺路单-订单列表
 */
@Data
@AllArgsConstructor
public class SideOrderListDTO {

    /**
     * 师傅ID
     */
    @NotNull
    @Min(value = 1L)
    private Long masterId;

    /**
     * 报价订单id
     */
    @NotNull
    private Long officeOrderIds;

}
