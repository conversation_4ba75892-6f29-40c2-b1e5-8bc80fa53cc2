package com.wanshifu.order.push.request.push;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023-09-22 09:23
 * @Description
 * @Version v1
 **/
@Data
public class ExclusiveOrderLabel {

    /**
     * 专属订单标记:101:系统报价,102:抢单报价单,103成品报价,104家庭,105总包,106系统报价转抢单报价单
     */
    @ValueIn(value = "101,102,103,104,105,106", required = true)
    private Integer exclusiveFlag;
    /**
     * 标签名,exclusive:专属,direct_appointment:直约,preferred:优选,contract:合约(专属),brand:品牌
     */
    @ValueIn(value = "exclusive,direct_appointment,preferred,contract,brand")
    private String recruitTagName;
}
