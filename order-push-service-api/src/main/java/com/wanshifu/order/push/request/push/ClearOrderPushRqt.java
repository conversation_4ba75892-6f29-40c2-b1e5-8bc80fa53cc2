package com.wanshifu.order.push.request.push;

import com.wanshifu.order.push.enums.OrderPushClearType;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/4 17:28
 */
@Data
public class ClearOrderPushRqt {

    /**
     * 业务场景类型
     * {@link OrderPushClearType}
     */
    @NotNull
    private String businessType;

    /**
     * 订单id
     */
    @NotNull
    private Long orderId;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 师傅id集合
     */
    private Set<Long> masterIds;

    /**
     * 报价/抢单时间
     */
    private Date offerTime;

    /**
     * 关单时间
     */
    private Date closeTime;

    /**
     * 追加备注
     */
    private String appendNote;

    /**
     * 是否需要异步Mq物理删除
     */
    private Boolean isNeedAsyncDelete = false;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     * 注意：modifyOrder、offerModifyOrder修改订单时，应传订单的原地址id
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;

    private List<Long> provinceNextIds;

    /**
     * 业务参数
     */
    private List<String> businessParams;
}
