package com.wanshifu.order.push.request.tmplcity;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 获取样板城市订单推荐师傅rqt
 * @date 2024/6/25 14:01
 */
@Data
public class ListTmplCityOrderRecommendMasterRqt {

    /**
     * 全局订单id
     */
    @NotNull
    @Min(1L)
    private Long globalOrderTraceId;

    /**
     * 当前页数，默认1
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数，默认5
     */
    private Integer pageSize = 5;


}
