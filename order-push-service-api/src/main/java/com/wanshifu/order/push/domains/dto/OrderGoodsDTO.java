package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderGoodsDTO{
    /**
     * 订单商品ID，主键自增
     */
    private Long orderGoodsId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 第三方商品ID
     */
    private Long thirdGoodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型
     */
    private String goodsCategory;

    /**
     * 商品数量
     */
    private String number;

    /**
     * 商品重量，单位：千克(kg)
     */
    private String weight;

    /**
     * 商品体积，单位：立方米(m^3)
     */
    private String bulk;

    /**
     * 尺寸属性
     */
    private String bulkAttr;

    /**
     * 是否需要拆卸
     * 0：不需要(默认)；
     * 1：需要
     */
    private Integer isDisassemble;

    /**
     * 商品二级类目
     */
    private Integer categoryChild;

    /**
     * 商品品牌
     */
    private String goodsBrand;

    /**
     * 尺寸额外信息
     */
    private String bulkExtra;

    /**
     * 商品单位
     */
    private String goodsUnit;

    /**
     * 尺寸单位
     */
    private String bulkUnit;

    /**
     * 安装要求(JSON)
     */
    private String installRequire;


    /**
     * 安装位置(json)
     */
    private String installPosition;

    /**
     * 商品属性(JSON)
     */
    private String goodsAttr;

    /**
     * 保养类型(JSON)
     */
    private String maintainType;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 视频类型
     * inside[内部],outside[外部]
     */
    private String videoType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 门洞数量
     */
    private Integer doorHoleNumber;

    /**
     * 安装类型{"flatOpen":{"singleOpen":""}}
     */
    private String installType;

    /**
     * 附属设施;{"doorHeadPlate":[{"name":"门头板1","location":"主卧"},{"name":"门头板2","location":"次卧"}],"fixedTransom":[{"name":"摇头窗1","location":"主卧"},{"name":"摇头窗2","location":"次卧"}]}
     */
    private String ancillaryFacilities;

    /**
     * 详细说明，重量、体积等详细说明信息
     */
    private String note;

    /**
     * 商品额外信息
     */
    private String goodsInfo;

    /**
     * 智能锁所属门类别
     */
    private String doorCategory;

    /**
     * 维修类型和配件
     */
    private String repairType;

    /**
     * 上墙固定
     */
    private String wallFixed;

    /**
     * 是否需要扫SN码(0:不需要;1:需要)
     */
    private Integer snFlag;

    /**
     * 卫浴测量要求
     */
    private String measureRequire;

    /**
     * 附属商品(JSON)
     */
    private String ancillaryGoods;

    /**
     * 更换配件属性信息（json格式）
     */
    private String sendBackAndReplacePart;

    /**
     * 通顶门数量(适用门类)
     */
    private Integer tipDoorNumber;

    /**
     * 安装特殊属性(json格式，适用门类)
     * 轨道类型（地轨、吊轨）
     * 开门方式（单开门、子母门、双开门）
     * 是否异型,0:否,1:是
     */
    private String installAttr;

    /**
     * 增项服务(json格式)
     */
    private String addedService;

    /**
     * 厨柜明细(json格式)
     */
    private String galleyDresserDetail;

    /**
     * 商品材质(json格式)
     */
    private String texture;

    /**
     * 商品包含信息(json)(全屋定制)
     */
    private String goodsIncludeDetail;

    /**
     * 商品拓展属性json格式 [{"key": "属性key","option": {"key":"属性选择值key"}}](通用字段)
     */
    private String goodsConditionDetail;

    /**
     * 售后商品id
     */
    private Long afterSalesGoodsId;

    /**
     * 需要sn码的数量
     */
    private Integer snCodeNum;

    /**
     * sn码标题与示例图json
     */
    private String snSampleGraph;

    /**
     * 示例图说明文案
     */
    private String sampleGraphText;
}