package com.wanshifu.order.push.request;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * Title：待报价订单列表与服务中订单距离计算
 */
@Data
@AllArgsConstructor
public class WaitOfferAndServingOrderDistanceCountRqt {

    /**
     * 师傅ID
     */
    @NotNull
    @Min(value = 1L)
    private Long masterId;

    /**
     * 待报价订单ids集合
     */
    @NotNull
    @Size(min = 1)
    private List<Long> officeOrderIds;

    /**
     * 师傅服务中订单ids集合
     */
    @NotNull
    @Size(min = 1)
    private List<Long> servingOrderIds;

}
