package com.wanshifu.order.push.request.special;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-02-02 16:58
 * @Description
 * @Version v1
 **/
@Data
public class SpecialOrderCountDTO {
    /**
     * 师傅ID
     */
    @NotNull
    @Min(value = 1L)
    private Long masterId;

    /**
     * 订单ids集合（最大20个）
     */
    @NotNull
    @Size(min = 1,max = 20)
    private List<Long> orderIds;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    @Min(value = 1L)
    @NotNull
    private Long provinceNextId;

    /**
     * 样板城市订单师傅身份
     * 1：主力师傅
     * 2：储备师傅
     * 3.普通师傅
     */
    private Integer tmplCityMasterRole;
}
