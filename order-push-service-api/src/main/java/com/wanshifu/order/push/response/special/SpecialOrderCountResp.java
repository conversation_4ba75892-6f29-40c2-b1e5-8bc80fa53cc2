package com.wanshifu.order.push.response.special;

import lombok.Data;

import java.util.List;

/**
 * Title：师傅待报价订单列表--异步信息部分
 */
@Data
public class SpecialOrderCountResp {
    /**
     * 报价-服务中[顺路单]数量   ------- v7.11-spring01
     */
    private List<SideOrder> sideOrderList;

    /**
     * 报价-附近单数量   ---------------7.12--425迭代
     */
    private List<SideOrder> nearbyOrderList;

    /**
     * 带配件业务   ---------------7.13--0516迭代
     */
    private List<PartsInfo> partsInfoList;

    @Data
    public static class SideOrder {
        /**
         * 待报价订单ID
         */
        private Long orderId;
        /**
         * 服务中订单数
         */
        private Integer number = 0;
    }


    @Data
    public static class PartsInfo {
        /**
         * 待报价订单ID
         */
        private Long orderId;
        /**
         * 带配件业务文案
         */
        private String partsInfoText;
    }
}
