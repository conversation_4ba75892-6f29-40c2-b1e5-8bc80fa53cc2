package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderIkeaGoodsDTO {
    /**
     * 宜家订单商品ID，主键自增
     */
    private Long orderIkeaGoodsId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 下单方宜家商品ID
     */
    private Long thirdIkeaGoodsId;

    /**
     * 宜家商品ID，0表示未选择商品(直接通过类型下单)，对应ikea_goods_service库的宜家商品ID
     */
    private Long ikeaGoodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型ID
     */
    private Integer goodsClassify;

    /**
     * 商品数量
     */
    private Integer number;

    /**
     * 是否需要上墙 1: 是；0：否(默认)
     */
    private Integer needOnWall;

    /**
     * 量词
     */
    private String quantifier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 详细说明
     */
    private String note;
}