package com.wanshifu.order.push.domains.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
public class OrderIkeaGoodsAdditionalDTO {
    /**
     * 附加安装配置ID，主键自增
     */
    private Long ikeaAdditionalId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 宜家订单商品ID
     */
    private Long orderIkeaGoodsId;


    /**
     * 附件安装类型名称
     */
    private String additionalTypeName;

    /**
     * 附加安装数量
     */
    private Integer number;

    /**
     * 量词
     */
    private String quantifier;

    /**
     * 值输入方式 (1:下拉选项 2:数值输入,默认数值输入,兼容以前的)
     */
    private Integer inputMode;

    /**
     * 选择范围(左闭右闭区间)input_mode为1时的范围
     */
    private String selectRange;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}