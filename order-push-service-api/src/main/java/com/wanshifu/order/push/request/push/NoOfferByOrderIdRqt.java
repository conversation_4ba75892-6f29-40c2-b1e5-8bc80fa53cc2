package com.wanshifu.order.push.request.push;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/21 16:08
 */
@Data
public class NoOfferByOrderIdRqt {


    @NotNull
    private Long orderId;

    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;


    private List<Long> provinceNextIds;

    private Date currentDateTime;

    /**
     * 当前页数，默认1
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数，默认20
     */
    private Integer pageSize = 20;
}
