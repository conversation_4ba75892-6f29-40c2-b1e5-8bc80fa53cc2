package com.wanshifu.order.push.domains.dto;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 加急
 *
 * <AUTHOR>
 */
@Data
public class EmergencyOrderDTO {

    /**
     *
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 加急单状态 success:成功  fail:失败 cancel:取消
     */
    private String
            emergencyOrderStatus;

    /**
     * door_in:按时上门 finish:按时完工 "": 默认空
     */
    private String emergencyRequireType;

    /**
     * 加急费
     */
    private BigDecimal emergencyFee;

    /**
     * 加急费状态 0:默认  1：获得加急费成功 2:获得加急费失败
     */
    private Integer emergencyFeeStatus;

    /**
     * 期望完成时间
     */
    private Date expectCompleteTime;

    /**
     * 期望上门开始时间
     */
    private Date expectDoorInStartDate;

    /**
     * 期望上门截至时间
     */
    private Date expectDoorInEndDate;

    /**
     * 未获得加急费原因
     */
    private String notAwardReason;

    /**
     * 获得加急费时间
     */
    private Date awardTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}