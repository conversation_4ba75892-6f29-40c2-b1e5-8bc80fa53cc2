package com.wanshifu.order.push.response;

import com.wanshifu.order.push.domains.dto.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2019/8/21 16:25
 */
@Data
public class WaitOfferV2Resp {

    /**
     * 指派类型(2:发布任务,3:直接雇佣,4:一口价,5:预付款
     */
    private Integer appointType;

    /**
     * 订单报价数量
     */
    @Deprecated
    private Integer offerNumber;

    /**
     * 商品图片数量
     */
    private Integer imageCount;

    /**
     * 干预位标记
     * 1： 干预位
     * 0： 非干预位
     */
    private Integer exposurePositionFlag;

    /**
     * 订单是否有视频
     */
    private boolean hasVideo = false;

    /**
     * 是否是意向单(1:是,0:不是),默认0
     */
    private Integer isIntention;

    /**
     * 菜单类别 0：默认 1：合作专区, 2:悬赏分区
     */
    private Integer menuCategory;

    /**
     * 报价名额
     */
    private Integer remainOfferNumber;

    /**
     * 截止报价时间
     */
    private Date stopOfferTime;

    /**
     * 最大可报价人数
     */
    private Integer maxOfferNumber;

    /**
     * 好评返现金额初始值
     */
    private BigDecimal rateAwardFee;

    /**
     * 推单距离:师傅常驻地址和客户地址距离
     */
    private Long pushDistance;

    /**
     * 推单类型 1:导航距离(默认) 2:直线距离
     */
    private Integer pushDistanceType;

    /**
     * 师傅第一次拉取待报价订单时间
     */
    private Date firstPullTime;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 是否是企业一口价前置支付订单
     */
    private Boolean isPrePayOrderBeforeAppoint = false;

    /**
     * 指派表的城市id
     */
    private Long secondDivisionId;

    /**
     * 订单基础信息
     */
    private OrderBaseDTO orderBaseDTO;


    /**
     * 订单额外信息
     */
    private OrderExtraDataDTO orderExtraData;

    /**
     * 订单奖励信息
     */
    private OrderAwardDTO orderAwardDTO;

    /**
     * 订单工程类数据
     */
    private OrderEpcGenreDataDTO orderEpcGenreDataDTO;

    /**
     * 返货物流信息表
     */
    private OrderReturnLogisticsDTO orderReturnLogisticsDTO;

    /**
     * 订单物流信息
     */
    private OrderLogisticsInfoDTO orderLogisticsInfoDTO;


    /**
     * 订单初始费用
     */
    private OrderInitFeeDTO orderInitFeeDTO;

    /**
     * 订单基础信息(意向单)
     */
    private InfoOrderBaseDTO infoOrderBaseDTO;

    /**
     * 订单扩展信息(意向单)
     */
    private InfoOrderExtraDataDTO infoOrderExtraDataDTO;

    /**
     * 订单附件信息(意向单)
     */
    private List<InfoOrderAttachmentDTO> infoOrderAttachmentDTOS;

    /**
     * 订单商品信息(意向单)
     */
    private List<InfoOrderGoodsCompositeDTO> infoOrderGoodsCompositeDTOS;

    /**
     * 宜家订单商品信息组合集合
     */
    private List<IkeaOrderGoodsCompositeDTO> ikeaOrderGoodsCompositeDTOS;

    /**
     * 订单商品信息组合集合
     */
    private List<OrderGoodsCompositeDTO> orderGoodsCompositeDTOS;

    /**
     * 加急单
     */
    private EmergencyOrderDTO emergencyOrderDTO;

    /**
     * 订单服务属性信息
     */
    private List<OrderServiceAttributeInfoDTO> orderServiceAttributeInfoDTOS;


    /**
     * 是否合作商订单 1:是 0:否
     */
    private Integer isAgentOrder;


    /**
     * 竞争少订单标签
     */
    private String pushNoticeLabel;

    /**
     * 是否拉取订单距离,1:拉取,0:不拉取
     */
    private Integer isPullOrderDistance = 0;

    /**
     * 是否清理订单距离,1:清理,0:不清理
     */
    private Integer isClearOrderDistance = 0;

    /**
     * 订单支付状态,0:默认,1:一口价前置支付
     */
    private Integer orderPayStatus = 0;

    /**
     * 指派专区竞争少标识(0:否;1:是)
     */
    private Integer lessContendFlag;

    /**
     * 订单标签
     */
    private List<OrderExclusiveTagDTO> orderExclusiveTagDTOS;

    /**
     * 增项服务
     */
    private List<OrderAddItemServiceInfoDTO> orderAddItemServiceInfoDTOList;

    /**
     * 锁单记录
     */
    private List<OrderAutoGrabDTO> orderAutoGrabDTOList;

    /**
     * 剩余报价数量
     */
    private Integer remainRecommendOfferNum;

    /**
     * 订单配件信息
     */
    private List<OrderPartsDTO> orderPartsDTOList;

    /**
     * 订单悬赏信息
     */
    private OrderBountyInfoDTO orderBountyInfo;

}
