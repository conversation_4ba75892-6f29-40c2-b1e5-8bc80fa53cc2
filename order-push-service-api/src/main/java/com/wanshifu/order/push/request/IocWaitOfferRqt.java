package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * IOC(智能运营)活动待报价-展示数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/8/25 15:41
 */
@Data
public class IocWaitOfferRqt {

    /**
     * IOC(智能运营)活动待报价-展示数据
     */
    @NotNull
    @Size(min = 1, max = 10)
    private List<Long> orderIds;


}
