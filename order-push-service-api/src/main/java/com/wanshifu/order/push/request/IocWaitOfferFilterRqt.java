package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * IOC(智能运营)活动待报价-筛选数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/8/25 15:31
 */
@Data
public class IocWaitOfferFilterRqt {

    /**
     * 查询数量，最大不能超过300
     */
    @NotNull
    @Min(1)
    @Max(300)
    private Integer queryNumber;

    /**
     * 师傅ID
     */
    @NotNull
    private Long masterId;

    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     *
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}
