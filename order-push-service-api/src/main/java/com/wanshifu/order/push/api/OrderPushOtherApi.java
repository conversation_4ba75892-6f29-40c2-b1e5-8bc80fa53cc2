package com.wanshifu.order.push.api;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/1 10:52
 */
@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/orderPush/other")
public interface OrderPushOtherApi {

    /**
     * 获取下沉开关
     * @return
     */
    @PostMapping("/getSinkIterationSwitch")
    Boolean getSinkIterationSwitch();


    @PostMapping("getPushDockingHandoffTag")
    public String getPushDockingHandoffTag(@RequestParam("thirdDivisionId")  Long  thirdDivisionId);
}
