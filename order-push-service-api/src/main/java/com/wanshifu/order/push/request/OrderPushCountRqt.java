package com.wanshifu.order.push.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2025-06-26 17:28
 * @Description
 * @Version v1
 **/
@Data
public class OrderPushCountRqt {

    /**
     * 师傅ID
     */
    @NotNull
    private Long globalOrderTraceId;

    /**
     * 统计模式 0-推送师傅，1：已查看师傅
     * （非必填）
     */
    @NotEmpty
    private Set<Integer> statisticsMode;

}
