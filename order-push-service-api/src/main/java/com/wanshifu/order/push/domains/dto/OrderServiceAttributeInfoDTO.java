package com.wanshifu.order.push.domains.dto;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 订单服务属性信息表
 * <AUTHOR>
 */
@Data
public class OrderServiceAttributeInfoDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 第三方服务ID
     */
    private Long thirdServiceId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 服务ID
     */
    private Long serviceId;

    /**
     * 服务版本
     */
    private String serviceVersion;

    /**
     * 下单服务信息-JSON格式
     */
    private String serviceInfos;

    /**
     * 师傅服务信息-JSON格式
     */
    private String masterServiceInfos;

    /**
     *  服务价格信息-JSON格式
     */
    private String servicePriceInfos;

    /**
     *  售后商品ID
     */
    private Long afterSalesGoodsId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}