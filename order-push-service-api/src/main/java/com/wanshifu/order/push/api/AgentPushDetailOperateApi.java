package com.wanshifu.order.push.api;

import com.wanshifu.order.push.request.agent.UpdateAgentByDisinterestOrderRqt;
import com.wanshifu.order.push.request.agent.UpdateAgentByGrabRqt;
import com.wanshifu.order.push.request.agent.UpdateAgentByOfferPriceRqt;
import com.wanshifu.order.push.request.agent.ViewOrderRqt;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 代理商推送记录操作
 * @date 2024/3/11 14:48
 */
@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/agentPush/operate")
public interface AgentPushDetailOperateApi {

    /**
     * 查看订单
     * @param viewOrderRqt
     * @return
     */
    @PostMapping("viewOrder")
    void viewOrder(@RequestBody @Valid ViewOrderRqt viewOrderRqt);

    /**
     * 报价
     * @param updateAgentByOfferPriceRqt
     */
    @PostMapping("offerPrice")
    void offerPrice(@RequestBody @Valid UpdateAgentByOfferPriceRqt updateAgentByOfferPriceRqt);

    /**
     * 一口价抢单
     * @param updateAgentByGrabRqt
     */
    @PostMapping("grabOrder")
    void grabOrder(@RequestBody @Valid UpdateAgentByGrabRqt updateAgentByGrabRqt);


    /**
     * 师傅不感兴趣订单
     * @param updateAgentByDisinterestOrderRqt
     */
    @PostMapping("disinterestOrder")
    void disinterestOrder(@RequestBody @Valid UpdateAgentByDisinterestOrderRqt updateAgentByDisinterestOrderRqt);
}
