package com.wanshifu.order.push.domains.dto;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 订单文件关联表
 */
@Data
public class OrderFileRelaDTO {

    /**
     * 订单文件关联ID
     */
    private Long fileRelaId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 关联类型,drawing_file:图纸文件
     */
    private String relaType;

    /**
     * 文件类型,image:图片,video:视频,document:文档
     */
    private String fileType;

    /**
     * 文件id(图片为aid，视频、文档为文件id)
     */
    private Long fileId;

    /**
     * 文件链接
     */
    private String fileUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}