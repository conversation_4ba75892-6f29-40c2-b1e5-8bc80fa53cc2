package com.wanshifu.order.push.domains.dto;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 订单配件表
 */
@Data
public class OrderPartsDTO {

    /**
     * 主键id
     */
    private Long orderPartsId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 配件id（用户配置的）
     */
    private Long partsConfigId;

    /**
     * 配件品牌
     */
    private String partsBrand;

    /**
     * 配件品牌中文名
     */
    private String partsBrandCn;

    /**
     * 配件类型
     */
    private String partsType;

    /**
     * 配件孔类型
     */
    private String partsLockHoleType;

    /**
     * 配件规格
     */
    private String partsSpecification;

    /**
     * 配件数量
     */
    private Integer partsNumber;

    /**
     * 配件总价格
     */
    private BigDecimal partsPrice;

    /**
     * 师傅配件总价格
     */
    private BigDecimal masterPartsPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}