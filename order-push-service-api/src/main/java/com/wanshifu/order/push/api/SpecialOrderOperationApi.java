package com.wanshifu.order.push.api;

import com.wanshifu.order.push.request.special.ClearSpecialOrderDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2024-02-02 16:43
 * @Description
 * @Version v1
 **/
@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/specialOrder/operation")
public interface SpecialOrderOperationApi {


    /**
     * 清理3天之前拉取的顺路单订单历史数据
     * @param dto
     * @return Integer
     * @interface
     * <AUTHOR>
     */
    @PostMapping("clearSideOrder")
    Integer clearSideOrder(@Valid @RequestBody ClearSpecialOrderDTO dto);


    /**
     * 清理3天之前报价订单附近单历史数据
     * @param dto
     * @return Integer
     * @interface
     * <AUTHOR>
     */
    @PostMapping("clearNearbyOrder")
    Integer clearNearbyOrder(@Valid @RequestBody ClearSpecialOrderDTO dto);


}
