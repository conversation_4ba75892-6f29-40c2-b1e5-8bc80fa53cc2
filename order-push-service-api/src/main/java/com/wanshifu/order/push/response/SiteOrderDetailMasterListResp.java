package com.wanshifu.order.push.response;

import com.wanshifu.order.push.enums.SiteOrderDetailMasterListMasterMainLabelEnum;
import com.wanshifu.order.push.enums.SiteOrderDetailMasterListMasterSubLabelEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/8 15:17
 */
@Data
public class SiteOrderDetailMasterListResp {


    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 师傅主标签
     * @see SiteOrderDetailMasterListMasterMainLabelEnum
     */
    private String masterMainLabel;


    /**
     * 师傅子标签
     * @see SiteOrderDetailMasterListMasterSubLabelEnum
     */
    private String masterSubLabel;

    /**
     * 推单评分
     */
    private BigDecimal pushScore;

    /**
     * 推单距离(当时推单师傅和客户得距离),单位米
     */
    private Long pushDistance;

    /**
     * 师傅所在二级地址该师傅是否是省钱师傅，1：省钱师傅，0：默认否
     */
    private Integer masterSecondDivisionIdIsSaveMoneyMaster;

    /**
     * 二级服务是否服务好，1：服务好，0：默认否
     */
    private Integer serveLv2IsGoodService;

    /**
     * 师傅与商家是否合作过，1：合作过，0：默认否
     */
    private Integer userIdIsCooperation;

    /**
     * 商家是否收藏师傅，1：已收藏，0：默认否
     */
    private Integer userIdIsCollectMaster;

    /**
     * 三级服务完工单量
     */
    private Integer serveLv3FinishOrderCnt;

    /**
     * 师傅所在二级地址该师傅是否常报最低价，1：是，0：默认否，
     */
    private Integer masterSecondDivisionIdIsUsualOfferLowPrice;

    /**
     * 师傅所在二级地址该师傅是否常抢一口价单，1：是，0：默认否
     */
    private Integer masterSecondDivisionIdIsUsualGrabOrder;

    /**
     * 师傅所在二级地址该师傅最近90天报价最低单量
     */
    private Integer masterSecondDivisionIdLatest90DayLowOfferCnt;

    /**
     * 师傅所在二级地址该师傅最近90天报价最低单量排名
     */
    private Integer masterSecondDivisionIdRankByLatest90DayLowOfferCnt;

    /**
     * 师傅三级地址三级服务完工单量排名
     */
    private Integer masterThirdDivisionIdServeLv3IdRankByFinishOrderCnt;



}
