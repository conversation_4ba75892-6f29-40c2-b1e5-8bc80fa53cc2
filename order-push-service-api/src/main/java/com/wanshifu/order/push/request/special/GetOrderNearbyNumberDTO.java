package com.wanshifu.order.push.request.special;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 获取报价订单附近单数量
 *
 * <AUTHOR>
 */
@Data
public class GetOrderNearbyNumberDTO {

    /**
     * 报价订单集合
     */
    @NotNull
    @Size(min = 1)
    private List<Long> orderIds;

    /**
     * 师傅id
     */
    @NotNull
    private Long masterId;


    /**
     * 推单数据省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    @Min(value = 1L)
    private Long provinceNextId;

    /**
     * 样板城市订单师傅身份
     * 1：主力师傅
     * 2：储备师傅
     * 3.普通师傅
     */
    private Integer tmplCityMasterRole;

}
