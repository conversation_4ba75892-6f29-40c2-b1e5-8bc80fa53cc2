package com.wanshifu.order.push.response.special;

import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderExtraDataDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * Title：顺路单-订单列表
 */
@Data
@AllArgsConstructor
public class SideOrderListResp {

    /**
     * 列表类型,待预约订单列表：wait_reserve_customer,待提货订单列表：wait_logistics_sign,待上门订单列表：wait_serve_sign,待完成订单列表：wait_complete
     */
    private String orderType;

    /**
     * 服务中订单信息
     */
    private OrderInfo orderInfo;

    /**
     * 订单基础信息
     */
    private OrderBaseDTO orderBaseDTO;

    /**
     * 订单额外信息
     */
    private OrderExtraDataDTO orderExtraDataDTO;

    /**
     * 订单服务信息
     */
    private OrderServeCompositeInfo orderServeCompositeInfo;

    @Data
    public static class OrderInfo {

        /**
         * 指派类型
         */
        private Integer appointType;

        /**
         * 订单地址距离(订单之间的距离),单位米
         */
        private Long orderAddressDistance;

        /**
         * 物流提货地址
         */
        private String pickupAddress;
    }

    @Data
    public static class OrderServeCompositeInfo {

        /**
         * 下一步服务节点
         */
        private String nextServeNode;

        /**
         * 预约开始时间
         */
        private Date reserveStartTime;

        /**
         * 预约结束时间
         */
        private Date reserveEndTime;

        /**
         * 二次上门预约开始时间
         */
        private Date secondReserveStartTime;

        /**
         * 二次上门预约结束时间
         */
        private Date secondReserveEndTime;
    }


}
