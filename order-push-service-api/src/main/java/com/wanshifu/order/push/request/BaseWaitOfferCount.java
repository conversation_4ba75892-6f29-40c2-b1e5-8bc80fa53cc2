package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-11-28 17:28
 * @Description
 * @Version v1
 **/
@Data
public class BaseWaitOfferCount {

    /**
     * 师傅ID
     */
    @NotNull
    private Long masterId;

    /**
     * 区域ID
     */
    private List<Long> divisionId;


    /**
     * 指派模式(2:发布任务,3:直接指派,4:一口价,5:预付款)
     */
    private List<Integer> appointType;


    /**
     * 订单账号标签,1:商家(site/thirdpart),2:总包(accountType=enterprise),3:宜家(ikea),4:家庭(applet)
     */
    private List<Integer> orderAccountLabel;


    /**
     * 推送来源,1:智能推单,2:ocs后台
     */
    private Integer pushFrom;


    /**
     * 休息开始时间pushFrom为2时必传   师傅休息中加急单查询
     */
    private Date restStartTime;

    /**
     * 技能类型集合,1:清洁/保养/治理/美缝,
     * 4:配送并安装,5:维修服务,6:家装施工,8:安装服务,10:测量服务,13:定制家具/门类/测量/安装,
     * 16:管道疏通,17:搬运服务,18:拆旧服务,19:房屋维修
     */
    private List<Integer> techniqueTypeId;

    /**
     * 订单类目id
     */
    private List<Integer> categoryId;

    /**
     * 是否到货,1:已到货,2:未到货
     */
    private List<Integer> isArrived;

    /**
     * 指派专区竞争少标识(0:否;1:是)
     */
    private Integer lessContendFlag;


    /**
     * 订单标签：1：未看，2：必接,3:暂无报价
     */
    private List<Integer> orderFlags;


    /**
     * 师傅类型，
     * toc:c端师傅
     * tob:b端师傅
     */
    private String masterSourceType;
}
