package com.wanshifu.order.push.api;

import com.wanshifu.order.push.request.special.SideOrderCombinationDTO;
import com.wanshifu.order.push.request.special.SideOrderListDTO;
import com.wanshifu.order.push.request.special.SpecialOrderCountDTO;
import com.wanshifu.order.push.response.special.SideOrderCombinationResp;
import com.wanshifu.order.push.response.special.SideOrderCountResp;
import com.wanshifu.order.push.response.special.SideOrderListResp;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-02-02 16:43
 * @Description
 * @Version v1
 **/
@FeignClient(value = "order-push-service", url = "${wanshifu.order-push-service.url}", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
        , path = "/specialOrder/list")
public interface SpecialOrderListApi {

    /**
     * 顺路单-订单列表
     *
     * @param rqt
     * @return List<SideOrderListResp>
     * @interface
     * <AUTHOR>
     */
    @PostMapping("sideOrderList")
    @Deprecated
    List<SideOrderListResp> sideOrderList(@Valid @RequestBody SideOrderListDTO rqt);

    /**
     * 新顺路单-订单列表
     *
     * @param dto
     * @return
     */
    @PostMapping("sideOrderListForES")
    List<SideOrderListResp> sideOrderListForES(@Valid @RequestBody SideOrderListDTO dto);

    /**
     * 新顺路单-数量统计
     *
     * @param dto
     * @return
     */
    @PostMapping("sideOrderCount")
    List<SideOrderCountResp> sideOrderCount(@Valid @RequestBody SpecialOrderCountDTO dto);


    /**
     * 顺路单组合列表
     * @param dto
     * @return
     */
    @PostMapping("sideOrderCombinationList")
    SideOrderCombinationResp sideOrderCombinationList(@Valid @RequestBody SideOrderCombinationDTO dto);


    }
