package com.wanshifu.order.push.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class SelectPushOrderByMasterIdRqt {
    /**
     * 师傅ID
     */
    @NotNull
    @Min(1)
    private Long masterId;

    /**
     * 省下级地址id
     * 目前业务传的是师傅所在区域id，order-push-service计算省下级地址id
     */
    @NotNull
    @Min(value = 1L)
    private Long provinceNextId;
}
