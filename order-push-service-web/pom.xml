<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>order-push-service</artifactId>
        <groupId>com.wanshifu</groupId>
        <version>1.0.48-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>order-push-service-web</artifactId>
    <packaging>war</packaging>
    <version>1.0.48-SNAPSHOT</version>


    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>

        <!--接口依赖-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-push-service-api</artifactId>
            <version>1.0.48-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-offer-service-api</artifactId>
            <version>1.65</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>base-address-service-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- master-order-service -->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-order-service-api</artifactId>
            <version>1.166</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>master-order-search-service-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>order-offer-service-api</artifactId>
                </exclusion>
            </exclusions>

        </dependency>

        <!--平台订单搜索服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-order-search-service-api</artifactId>
            <version>1.21</version>
        </dependency>

        <!--公共依赖-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-all-spring-boot-starter</artifactId>
        </dependency>

        <!-- apollo -->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!--framework test-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--H2-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.4.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-zipkin</artifactId>
        </dependency>

        <!--地址服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-address-service-api</artifactId>
            <version>1.0.33</version>
        </dependency>

        <!--    阿里云限流    -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>ahas-sentinel-client</artifactId>
            <version>1.10.5</version>
        </dependency>

        <!-- 经纬度算距离-->
        <dependency>
            <groupId>org.gavaghan</groupId>
            <artifactId>geodesy</artifactId>
            <version>1.1.3</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-manage-config-service-api</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>enterprise-order-service-api</artifactId>
            <version>1.10.39</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>master-order-service-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>user-order-service-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>enterprise-order-config-adapter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--订单配置-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-config-service-api</artifactId>
            <version>2.0.74</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-information-service-api</artifactId>
            <version>1.0.159</version>
        </dependency>

        <!-- es -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>6.3.2</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>6.3.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <version>4.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.hbase</groupId>
            <artifactId>alihbase-client</artifactId>
            <version>2.8.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-information-service-api</artifactId>
            <version>1.0.159</version>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <finalName>order-push-service</finalName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!--跳过测试步骤-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>