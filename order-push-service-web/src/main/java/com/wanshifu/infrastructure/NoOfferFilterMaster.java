package com.wanshifu.infrastructure;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 11:12
 */
@Data
public class NoOfferFilterMaster {

    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 全局订单id
     */
    @Column(name = "global_order_id")
    private Long globalOrderId;

    /**
     * 师傅id
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 师傅推单评分
     */
    @Column(name = "score")
    private BigDecimal score;

    /**
     * 打上可联系订单标签标记，1：已打标签，0：未打标签
     */
    @Column(name = "put_tag_flag")
    private Integer putTagFlag;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}
