package com.wanshifu.infrastructure;

import com.wanshifu.order.push.enums.TmplCityOrderPushLogEventEnum;
import com.wanshifu.order.push.enums.TmplCityOrderPushLogOperatorTypeEnum;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 样板城市订单推单操作记录表
 * @date 2024/8/29 16:08
 */
@Data
@Table(name = "tmpl_city_order_push_log")
public class TmplCityOrderPushLog {

    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * {@link TmplCityOrderPushLogEventEnum}
     * 操作事件，push_main_and_reserve:推送主力和储备师傅，push_normal:推送众包师傅
     */
    @Column(name = "push_event")
    private String pushEvent;

    /**
     * {@link TmplCityOrderPushLogOperatorTypeEnum}
     * 操作人类型，system:系统
     */
    @Column(name = "operator_type")
    private String operatorType;

    /**
     * 操作人
     */
    @Column(name = "operator")
    private String operator;

    /**
     * 主力师傅ids,以英文逗号分隔
     */
    @Column(name = "main_master_ids")
    private String mainMasterIds;

    /**
     * 储备师傅ids,以英文逗号分隔
     */
    @Column(name = "reserve_master_ids")
    private String reserveMasterIds;

    /**
     * 众包师傅ids,以英文逗号分隔
     */
    @Column(name = "normal_master_ids")
    private String normalMasterIds;

    /**
     * 操作时间
     */
    @Column(name = "create_time")
    private Date createTime;
}
