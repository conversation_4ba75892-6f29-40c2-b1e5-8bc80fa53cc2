package com.wanshifu.infrastructure.provider;

import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.infrastructure.repository.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/31 16:54
 */
public abstract class MapperProvider {

    @Resource
    protected OrderMqLogRepository orderMqLogRepository;
    @Resource
    protected OrderPushRepository orderPushRepository;
    @Resource
    protected OrderDistanceConditionConfigRepository orderDistanceConditionConfigRepository;
    @Resource
    protected OrderDistanceRepository orderDistanceRepository;
    @Resource
    protected MasterOrderDistanceRepository masterOrderDistanceRepository;
    @Resource
    protected MessageSenderService messageSenderService;
    @Resource
    protected CallGateway callGateway;
    @Resource
    protected OrderPushDeleteLogRepository orderPushDeleteLogRepository;
    @Resource
    protected AgentPushDetailRepository agentPushDetailRepository;
    @Resource
    protected TmplCityOrderRecommendMasterRepository tmplCityOrderRecommendMasterRepository;

    @Resource
    protected TmplCityOrderPushLogRepository tmplCityOrderPushLogRepository;

    @Resource
    protected NoOfferFilterMasterRepository noOfferFilterMasterRepository;

    @Resource
    protected FirstPushMatchMasterRepository firstPushMatchMasterRepository;
}
