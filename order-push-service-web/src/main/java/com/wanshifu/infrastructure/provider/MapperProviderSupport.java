package com.wanshifu.infrastructure.provider;


import com.google.common.base.Strings;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.OrderDistance;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.order.offer.domains.enums.AppointType;
import org.springframework.util.Assert;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024-02-19 15:38
 * @Description
 * @Version v1
 **/
public abstract class MapperProviderSupport extends MapperProvider {

    /**
     * 获取附近单记录
     *
     * @param orderId
     * @return
     */
    protected OrderDistance selectByOrderId(Long orderId) {
        //附近单记录 //20230826 下沉移除apollo开关配置
        return this.orderDistanceRepository.selectOrderDistanceByOrderId(orderId);
    }

    /**
     * 公共的dml语句结果判断
     */
    protected void dmlAssert(int result, String errorMessage) {
        Assert.state(result > 0, errorMessage);
    }


    /**
     * 是否为指派专区订单
     * 指派专区存放的订单来源包含：
     * 1. 用户直接指派订单：为企业下单用户直接指派师傅的订单
     * 2. 家庭代理商订单：家庭业务推送订单为合作商订单，此类订单仅针对合作商师傅才会体现
     * 1. 当合作商订单被合作商师傅点击不感兴趣或触发自动任务推送其他普通师傅，该订单仍保留在指派专区；
     * 2. 当合作商师傅变成非合作商师傅时，已推送的合作商订单仍显示在指派专区；
     * 3. 宜家订单：订单来源为宜家业务线
     * 1. 宜家订单也是只针对宜家师傅才会体现；
     * 2. 当宜家师傅变成非宜家师傅，已推送的宜家订单仍显示在指派专区；
     *
     * 样板城市推单入合作专区
     *
     * @param orderPush
     * @param pushMold new_model:样板城市推单 new_model_single:样板城市推单（只推主力师傅） 样板城市推单入合作专区
     * @return
     */
    protected Integer isAppointCategory(OrderPush orderPush, String pushMold) {
        if (orderPush == null) {
            return 0;
        }
        boolean isAppointCategory = StringUtils.equals(orderPush.getOrderFrom(), "ikea")
                || (orderPush.getAgentOrderFlag() != null && orderPush.getAgentOrderFlag() == 1)
                || (StringUtils.equals(orderPush.getOrderFrom(), "site") && orderPush.getAppointType() != null && Objects.equals(orderPush.getAppointType(), AppointType.NORMAL.value))
                || (!Strings.isNullOrEmpty(pushMold) && ("new_model".equals(pushMold) || "new_model_single".equals(pushMold)));
        return isAppointCategory ? 1 : 0;
    }

}
