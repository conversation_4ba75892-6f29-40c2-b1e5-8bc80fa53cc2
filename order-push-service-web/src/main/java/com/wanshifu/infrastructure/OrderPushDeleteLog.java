package com.wanshifu.infrastructure;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "order_push_delete_log")
public class OrderPushDeleteLog {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "log_id")
    private Long logId;

    /**
     * 师傅ID
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 订单ID
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 操作类型(modify_serve_region:修改服务地区,modify_serve_type:修改服务类型)
     */
    @Column(name = "handle_type")
    private String handleType;

    /**
     * 操作参数
     */
    @Column(name = "request_data")
    private String requestData;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 获取主键ID
     *
     * @return log_id - 主键ID
     */
    public Long getLogId() {
        return logId;
    }

    /**
     * 设置主键ID
     *
     * @param logId 主键ID
     */
    public void setLogId(Long logId) {
        this.logId = logId;
    }

    /**
     * 获取师傅ID
     *
     * @return master_id - 师傅ID
     */
    public Long getMasterId() {
        return masterId;
    }

    /**
     * 设置师傅ID
     *
     * @param masterId 师傅ID
     */
    public void setMasterId(Long masterId) {
        this.masterId = masterId;
    }

    /**
     * 获取订单ID
     *
     * @return order_id - 订单ID
     */
    public Long getOrderId() {
        return orderId;
    }

    /**
     * 设置订单ID
     *
     * @param orderId 订单ID
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取操作类型(modify_serve_region:修改服务地区,modify_serve_type:修改服务类型)
     *
     * @return handle_type - 操作类型(modify_serve_region:修改服务地区,modify_serve_type:修改服务类型)
     */
    public String getHandleType() {
        return handleType;
    }

    /**
     * 设置操作类型(modify_serve_region:修改服务地区,modify_serve_type:修改服务类型)
     *
     * @param handleType 操作类型(modify_serve_region:修改服务地区,modify_serve_type:修改服务类型)
     */
    public void setHandleType(String handleType) {
        this.handleType = handleType;
    }

    /**
     * 获取操作参数
     *
     * @return request_data - 操作参数
     */
    public String getRequestData() {
        return requestData;
    }

    /**
     * 设置操作参数
     *
     * @param requestData 操作参数
     */
    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}