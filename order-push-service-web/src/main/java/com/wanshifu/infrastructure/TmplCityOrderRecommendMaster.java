package com.wanshifu.infrastructure;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 样板城市订单推荐师傅表
 * @date 2024/6/24 16:16
 */
@Data
@Table(name = "tmpl_city_order_recommend_master")
public class TmplCityOrderRecommendMaster {

    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 全局订单id
     */
    @Column(name = "global_order_trace_id")
    private Long globalOrderTraceId;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 师傅id
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 该订单师傅排序值
     */
    @Column(name = "sort")
    private Integer sort;

    /**
     * 排序分值
     */
    @Column(name = "score")
    private BigDecimal score;

    /**
     * 是否推單，1：已推單，0：未推單
     */
    @Column(name = "is_push")
    private Integer isPush;


    /**
     * 师傅类别，tob: B端师傅，toc: C师傅
     */
    @Column(name = "master_source_type")
    private String masterSourceType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
