package com.wanshifu.infrastructure;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * mq消息日志表
 */
@Data
@ToString
@Table(name = "order_mq_log")
public class OrderMqLog {

    /**
     * 消息偏移量
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "log_offset_id")
    private Long logOffsetId;

    /**
     * 来源类型 send-发送 accept-接收
     */
    @Column(name = "source_type")
    private String sourceType;

    /**
     * 消息topic
     */
    @Column(name = "topic")
    private String topic;

    /**
     * 消息tag
     */
    @Column(name = "tag")
    private String tag;

    /**
     * 消息内容描述
     */
    @Column(name = "message_desc")
    private String messageDesc;

    /**
     * 消息id
     */
    @Column(name = "message_id")
    private String messageId;

    /**
     * 全局id
     */
    @Column(name = "global_order_trace_id")
    private Long globalOrderTraceId;

    /**
     * 数据内容
     */
    @Column(name = "data_details")
    private String dataDetails;

    /**
     * 处理状态 success-成功;fail-失败
     */
    @Column(name = "deal_status")
    private String dealStatus;

    /**
     * 失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 备注信息
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 添加时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}