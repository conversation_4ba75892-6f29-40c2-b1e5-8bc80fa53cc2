package com.wanshifu.infrastructure.gatewayimpl;

import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.sdk.offer.gateway.OrderOfferGateway;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.order.offer.api.NormalOrderOperationApi;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.domains.api.request.UpdateOrderPushedNumberReq;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024-03-03 16:07
 * @Description
 * @Version v1
 **/
@Component
public class OrderOfferGatewayImpl implements OrderOfferGateway {
    @Resource
    private NormalOrderOperationApi normalOrderOperationApi;
    @Resource
    private CallGateway callGateway;


    @Override
    public int updateOrderPushNumber(Long orderId, Integer pushNumber) {
        if (Objects.isNull(orderId) || Objects.isNull(pushNumber)) {
            throw new BusException("缺失必传参数 orderId、pushNumber");
        }
        if (pushNumber <= 0 ) {
         return 1;
        }
        UpdateOrderPushedNumberReq req = new UpdateOrderPushedNumberReq();
        req.setOrderId(orderId);
        req.setPushNumber(pushNumber);
        return callGateway.catchLog(()->normalOrderOperationApi.updateOrderPushNumber(req),"updateOrderPushNumber",req);

    }
}
