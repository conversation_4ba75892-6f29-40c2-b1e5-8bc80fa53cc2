package com.wanshifu.infrastructure.gatewayimpl;

import com.google.common.collect.Lists;
import com.wanshifu.domain.tmplcity.bo.SaveTmplCityOrderRecommendMasterRqtBo;
import com.wanshifu.domain.tmplcity.gateway.TmplCityOperateGateway;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.TmplCityOrderRecommendMaster;
import com.wanshifu.infrastructure.provider.MapperProviderSupport;
import com.wanshifu.infrastructure.repository.OrderPushRepository;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 16:58
 */
@Service
public class TmplCityOperateGatewayImpl extends MapperProviderSupport implements TmplCityOperateGateway {


    @Value("${tmpl.master.query.orderPush.pageSize:200}")
    private int tmplMasterQueryOrderPushPageSize;


    @Resource
    private OrderPushRepository orderPushRepository;

    @Override
    public void saveTmplCityOrderRecommendMaster(SaveTmplCityOrderRecommendMasterRqtBo rqtBo) {
        //先删除
        tmplCityOrderRecommendMasterRepository.deleteByGlobalOrderTraceId(rqtBo.getGlobalOrderTraceId());
        //再新增
        List<TmplCityOrderRecommendMaster> poList = Lists.newArrayList();
        List<SaveTmplCityOrderRecommendMasterRqtBo.RecommendMasterInfo> boList = rqtBo.getRecommendMasterInfoList();


        List<Long> masterIdList = boList.stream().map(SaveTmplCityOrderRecommendMasterRqtBo.RecommendMasterInfo::getMasterId).distinct().collect(Collectors.toList());

        List<OrderPush> orderPushList = new ArrayList<>();
        List<List<Long>> masterList = ListUtils.partition(masterIdList,tmplMasterQueryOrderPushPageSize);
        for(List<Long> batchList : masterList){
            List<OrderPush> orderPushes = orderPushRepository.selectByOrderIdsAndMasterId(rqtBo.getProvinceNextId(),rqtBo.getOrderId(),new HashSet<>(batchList));
            if(CollectionUtils.isNotEmpty(orderPushes)){
                orderPushList.addAll(orderPushes);
            }
        }

        Set<Long> pushedMasterIdSet = CollectionUtils.isNotEmpty(orderPushList) ? orderPushList.stream().map(OrderPush::getMasterId).collect(Collectors.toSet()) : new HashSet<>();


        Date now = new Date();
        for (SaveTmplCityOrderRecommendMasterRqtBo.RecommendMasterInfo bo : boList) {
            TmplCityOrderRecommendMaster tmplCityOrderRecommendMaster = new TmplCityOrderRecommendMaster();
            tmplCityOrderRecommendMaster.setOrderId(rqtBo.getOrderId());
            tmplCityOrderRecommendMaster.setGlobalOrderTraceId(rqtBo.getGlobalOrderTraceId());
            tmplCityOrderRecommendMaster.setMasterId(bo.getMasterId());
            tmplCityOrderRecommendMaster.setSort(bo.getSort());
            tmplCityOrderRecommendMaster.setScore(bo.getScore());
            tmplCityOrderRecommendMaster.setMasterSourceType(bo.getMasterSourceType());
            tmplCityOrderRecommendMaster.setCreateTime(now);
            tmplCityOrderRecommendMaster.setUpdateTime(now);
            tmplCityOrderRecommendMaster.setIsPush(pushedMasterIdSet.contains(bo.getMasterId()) ? 1 : 0);
            poList.add(tmplCityOrderRecommendMaster);
        }
        if (poList.size() > 0) {
            tmplCityOrderRecommendMasterRepository.insertList(poList);
        }
    }

    @Override
    public void deleteTmplCityOrderRecommendMasterByGlobalOrderTraceId(Long globalOrderTraceId) {
        tmplCityOrderRecommendMasterRepository.deleteByGlobalOrderTraceId(globalOrderTraceId);
    }

    @Override
    public void updateIsPush(Long globalOrderTraceId, List<Long> masterIdList) {
        List<List<Long>> masterList = ListUtils.partition(masterIdList,100);
        for(List<Long> batchList : masterList){
            tmplCityOrderRecommendMasterRepository.updateIsPush(globalOrderTraceId,batchList);
        }
    }
}
