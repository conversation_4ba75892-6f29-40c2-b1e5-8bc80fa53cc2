package com.wanshifu.infrastructure.gatewayimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.wanshifu.domain.base.model.RedisKeyConstant;
import com.wanshifu.domain.base.tools.ExceptionHelper;
import com.wanshifu.domain.base.tools.FeiShuUtils;
import com.wanshifu.domain.agent.gateway.AgentPushDetailManagerGateway;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.gateway.OrderPushOperateGateway;
import com.wanshifu.domain.push.model.*;
import com.wanshifu.domain.sdk.address.CommonAddressService;

import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.CollectionUtils;

import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.*;
import com.wanshifu.infrastructure.provider.MapperProviderSupport;
import com.wanshifu.master.order.domains.po.ServeStop;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;

import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.api.response.offer.PushingOrderCompositeResp;
import com.wanshifu.order.offer.domains.enums.*;
import com.wanshifu.order.offer.domains.po.*;

import com.wanshifu.order.push.enums.TmplCityOrderPushLogEventEnum;
import com.wanshifu.order.push.enums.TmplCityOrderPushLogOperatorTypeEnum;
import com.wanshifu.order.push.request.push.*;
import com.wanshifu.order.push.response.push.BatchUpdatePushDistanceResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/29 14:45
 */
@Component
@Slf4j
public class OrderPushOperateGatewayImpl  extends MapperProviderSupport implements OrderPushOperateGateway {

    @Resource
    private AsyncConfigurer asyncConfigurer;

    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;
    @Resource
    private AgentPushDetailManagerGateway agentPushDetailManagerGateway;

    @Resource
    private CommonAddressService commonAddressService;

    /**
     * 过期推单记录清理分表配置
     */
    @Value("${order.orderPush.clearExpiredPush.suffixTableConfig:}")
    private String clearExpiredPushSuffixTableConfig;

    @Resource(name = "clearExpireOrderPushExecutor")
    private Executor clearExpireOrderPushExecutor;

    /**
     * 死锁优化开关
     */
    @Value("${order.orderPush.pushDeadLockSwitch:on}")
    private String pushDeadLockSwitch;


    @Resource(name = "asyncUpdateExecutor")
    private Executor executor;

    @Value("${order.orderPush.update.limitCount:10}")
    private int updateLimitCount;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private RedisHelper redisHelper;


    @Resource
    private Executor otherBusDiscardPolicyExecutor;

    @Resource(name = "orderPushExecutor")
    private Executor orderPushExecutor;

    @Value("${wanshifu.rocketMQ.order-match-master-topic}")
    private String orderMatchMasterTopic;

    @Resource
    private RocketMqSendService rocketMqSendService;


    @Override
    public Set<Long> pushedToMaster(List<Long> provinceNextId, PushedToMasterGatewayRqt pushOrderRqt) {
        PushingOrderCompositeResp orderComposite = pushOrderRqt.getOrderComposite();

        Integer pushFrom = pushOrderRqt.getPushScenarioType().isManualPush() ? 2 : 1;

        String pushMold = pushOrderRqt.getPushMold();
        Set<Long> pushMasterIds = this.executePush(provinceNextId, pushMold, orderComposite, pushOrderRqt.getMasterAddressInfoList(),
                pushOrderRqt.getPushTime(), pushOrderRqt.getPushDivisionLevel(), pushFrom,
                pushOrderRqt.getTechniqueTypeIds(), pushOrderRqt.getAgentOrderFlag(),
                pushOrderRqt.getPushFlag(),
                pushOrderRqt.getAccordingDistancePushFlag(),
                pushOrderRqt.getServeStops(),
                pushOrderRqt.getExclusiveFlag(),
                pushOrderRqt.getMasterSourceType());

        if (CollectionUtils.isNotEmpty(pushOrderRqt.getAgentPushMasterList()) && pushOrderRqt.getNobodyOfferHour() != null) {

            CompletableFuture.runAsync(() -> {
                agentPushDetailManagerGateway.addPushRecording(pushOrderRqt.getOrderId(),
                        pushOrderRqt.getAgentPushMasterList(),
                        pushOrderRqt.getNobodyOfferHour(),
                        pushOrderRqt.getPushTime());
            }, otherBusDiscardPolicyExecutor);



        }
        return pushMasterIds;
    }

    @Override
    public void recordTmplCityOrderPushLog(OrderPushRqt orderPushRqt) {
        log.info("recordTmplCityOrderPushLog,orderId:{},params:{}", orderPushRqt.getOrderId(), JSONUtil.toJsonStr(orderPushRqt));
        Long orderId = orderPushRqt.getOrderId();
        String pushMode = orderPushRqt.getPushMode();
        List<MasterAddressInfo> masterAddressInfoList = orderPushRqt.getMasterAddressInfoList();
        if (CollectionUtil.isEmpty(masterAddressInfoList)) {
            log.warn("recordTmplCityOrderPushLog,masterAddressInfoList is empty!");
            return;
        }
        if ("new_model_transfor_normal".equals(pushMode)) {
            //样板城市订单推普通师傅
            String normalMasterIds = masterAddressInfoList.stream().map(MasterAddressInfo::getMasterId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            TmplCityOrderPushLog tmplCityOrderPushLog = new TmplCityOrderPushLog();
            tmplCityOrderPushLog.setOrderId(orderId);
            tmplCityOrderPushLog.setNormalMasterIds(normalMasterIds);
            tmplCityOrderPushLog.setMainMasterIds("");
            tmplCityOrderPushLog.setReserveMasterIds("");
            tmplCityOrderPushLog.setCreateTime(new Date());
            tmplCityOrderPushLog.setPushEvent(TmplCityOrderPushLogEventEnum.PUSH_NORMAL.getCode());
            tmplCityOrderPushLog.setOperatorType(TmplCityOrderPushLogOperatorTypeEnum.SYSTEM.getCode());
            tmplCityOrderPushLog.setOperator("-");
            tmplCityOrderPushLogRepository.insert(tmplCityOrderPushLog);
        } else if ("new_model".equals(pushMode)) {

            //推送的主力师傅
            List<MasterAddressInfo> mainMaterIdList = masterAddressInfoList.stream().filter(masterAddressInfo -> "main_master".equals(masterAddressInfo.getPushTag()))
                    .collect(Collectors.toList());
            String mainMasterIds = "";
            if (CollectionUtil.isNotEmpty(mainMaterIdList)) {
                mainMasterIds = mainMaterIdList.stream().map(MasterAddressInfo::getMasterId)
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
            }
            //推送的储备师傅
            List<MasterAddressInfo> reserveMaterIdList = masterAddressInfoList.stream().filter(masterAddressInfo -> "reserve_master".equals(masterAddressInfo.getPushTag()))
                    .collect(Collectors.toList());
            String reserveMasterIds = "";
            if (CollectionUtil.isNotEmpty(reserveMaterIdList)) {
                reserveMasterIds = reserveMaterIdList.stream().map(MasterAddressInfo::getMasterId)
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
            }

            TmplCityOrderPushLog tmplCityOrderPushLog = new TmplCityOrderPushLog();
            tmplCityOrderPushLog.setOrderId(orderId);
            tmplCityOrderPushLog.setMainMasterIds(mainMasterIds);
            tmplCityOrderPushLog.setReserveMasterIds(reserveMasterIds);
            tmplCityOrderPushLog.setNormalMasterIds("");
            tmplCityOrderPushLog.setCreateTime(new Date());
            tmplCityOrderPushLog.setPushEvent(TmplCityOrderPushLogEventEnum.PUSH_MAIN_AND_RESERVE.getCode());
            tmplCityOrderPushLog.setOperatorType(TmplCityOrderPushLogOperatorTypeEnum.SYSTEM.getCode());
            tmplCityOrderPushLog.setOperator("-");
            tmplCityOrderPushLogRepository.insert(tmplCityOrderPushLog);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InnovateBusinessPushResp pushedInnovateToMaster(List<Long> provinceNextId, InnovateBusinessPushGatewayRqt rqt) {

        Long orderId = rqt.getOrderId();
        InfoOrderBase infoOrderBase = rqt.getInfoOrderBase();
        Set<Long> masterIdList = new HashSet<>(rqt.getMasterIdList());

        InnovateBusinessPushResp businessPushResp = new InnovateBusinessPushResp();
        businessPushResp.setInfoOrderBase(infoOrderBase);
        businessPushResp.setPushMasterIds(rqt.getMasterIdList());

        List<OrderPush> infoOrderPushs = orderPushRepository.selectByOrderIdsAndMasterId(provinceNextId, orderId, masterIdList);

        //已经推过的师傅更新推送时间
        if (CollectionUtils.isNotEmpty(infoOrderPushs)) {
            List<Long> existMasterIdList = infoOrderPushs.stream().map(OrderPush::getMasterId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existMasterIdList)) {
                orderPushRepository.updateByOrderIdAndMasters(provinceNextId, orderId, existMasterIdList, rqt.getPushTime());
                //剔除已存在的infoOrderPush中的masterId
                masterIdList.removeAll(existMasterIdList);
            }
        }
        Map<Long, MasterAddressInfo> masterAddressInfoMap = rqt.getMasterAddressInfoList().stream().collect(Collectors.toMap(MasterAddressInfo::getMasterId, Function.identity(), (x1, x2) -> x2));

        if (CollectionUtils.isNotEmpty(masterIdList)) {
            List<OrderPush> infoOrderPushList = new ArrayList<>();
            //tips: info_order_push 表增加字段需要注意设置字段属性值
            masterIdList.forEach(masterId -> {
                OrderPush infoOrderPush = new OrderPush();
                if (Objects.nonNull(masterAddressInfoMap.get(masterId))
                        && Objects.nonNull(masterAddressInfoMap.get(masterId).getCrossCityPush())
                        && masterAddressInfoMap.get(masterId).getCrossCityPush() == 1) {
                    //跨城推单
                    infoOrderPush.setProvinceNextId(99999L);
                } else {
                    infoOrderPush.setProvinceNextId(provinceNextId.get(0));
                }
                infoOrderPush.setMasterId(masterId);
                infoOrderPush.setOrderId(orderId);
                infoOrderPush.setPushTime(rqt.getPushTime());
                infoOrderPush.setIsDelete(0);
                infoOrderPush.setCreateTime(new Date());
                infoOrderPush.setUpdateTime(new Date());
                infoOrderPush.setIsArrived(1);
                infoOrderPush.setOfferNumber(0);
                infoOrderPush.setIsIntention(1);
                infoOrderPush.setAppointType(2);
                infoOrderPush.setAccountType(infoOrderBase.getAccountType());
                infoOrderPush.setOrderFrom(StrUtil.EMPTY);
                infoOrderPush.setOrderLabel(StrUtil.EMPTY);
                infoOrderPush.setLimitOffer(0);
                infoOrderPush.setNote(DateUtils.getDateTime() + ":添加新订单 ");
                infoOrderPush.setPushDivisionLevel(3);
                infoOrderPush.setOrderDivisionId(infoOrderBase.getThirdDivisionId());
                infoOrderPush.setMasterLatitude(new BigDecimal(0));
                infoOrderPush.setMasterLongitude(new BigDecimal(0));
                infoOrderPush.setPushDistance(0L);
                infoOrderPush.setPushDistanceType(0);
                infoOrderPush.setPushFrom(1);
                infoOrderPush.setExclusiveFlag(0);
                infoOrderPush.setEmergencyOrderFlag(0);
                infoOrderPush.setCategoryId(infoOrderBase.getCategoryId());
                infoOrderPush.setTechniqueTypeIds(rqt.getTechniqueTypeIds());
                infoOrderPush.setAgentOrderFlag(0);
                infoOrderPush.setPushFlag(rqt.getPushFlag());
                infoOrderPush.setMenuCategory(super.isAppointCategory(infoOrderPush, null));
                infoOrderPush.setAccordingDistancePushFlag(0);
                infoOrderPush.setAccordingTechnologyPushFlag(1);
                //订单列表排序需要,添加
                infoOrderPush.setStopOfferTime(DateUtil.offsetDay(infoOrderBase.getCreateTime(), 30));
                infoOrderPush.setIsPullOrderDistance(0);
                infoOrderPush.setLessContendFlag(0);
                infoOrderPush.setScore(new BigDecimal(0));
                infoOrderPush.setExposureScore(new BigDecimal(0));
                infoOrderPush.setMasterSourceType(rqt.getMasterSourceType());
                infoOrderPushList.add(infoOrderPush);
            });

            orderPushRepository.insertOrderPushList(infoOrderPushList);
        }
        businessPushResp.setFirstPushMasterIds(masterIdList);
        return businessPushResp;
    }

    @Override
    public void resendOrderPush(ResendOrderPushRqt resendOrderPushRqt, Boolean isChangeDivision, Boolean isChangeTechnology, Long masterId, String removeTechnologyIds, List<Long> removeServeThirdDivisionIdList, List<Long> removeServeFourthDivisionIdList, List<OrderPush> masterNormalList, Map<Long, OrderBase> orderBaseMap) {
        if (isChangeDivision && isChangeTechnology) {
            asyncHandlerAllChange(resendOrderPushRqt, masterId, removeTechnologyIds, removeServeThirdDivisionIdList, removeServeFourthDivisionIdList, masterNormalList, orderBaseMap);
        }else if(isChangeDivision){
            asyncHandlerChangeDivision(resendOrderPushRqt, removeServeThirdDivisionIdList, removeServeFourthDivisionIdList, masterNormalList, orderBaseMap);
        }else if (isChangeTechnology) {
            asyncHandlerChangeTechnology(resendOrderPushRqt, masterId, removeTechnologyIds, masterNormalList, orderBaseMap);
        }
    }

    /**
     * 清理分表过期的推单记录
     * @param clearExpiredPushRqt clearExpiredPushRqt
     */
    @Override
    public Integer clearExpiredOrderPush(ClearExpiredPushRqt clearExpiredPushRqt) {

        return 1;
    }

    /**
     * 清理分表过期的推单记录
     * @param clearExpiredPushRqt clearExpiredPushRqt
     */
    @Override
    public void clearExpiredOrderPushV2(ClearExpiredPushRqt clearExpiredPushRqt) {
        if (Strings.isNullOrEmpty(clearExpiredPushSuffixTableConfig)) {
            return;
        }
        String[] suffixTable = StrUtil.split(clearExpiredPushSuffixTableConfig, ",");
        Date date = new Date();
        int fromDayNumber = clearExpiredPushRqt.getFromDayNumber();
        int queryNumber = clearExpiredPushRqt.getQueryNumber();
        int perDeleteNumber = clearExpiredPushRqt.getPerDeleteNumber();
        Date fromDayTime = fromDayNumber > 0 ? DateUtils.addDays(date, -fromDayNumber) : date;


        for (String suffixTableName : suffixTable) {
            //清理order_push
            List<Long> pushIds = orderPushRepository.selectExpiredOrderPush("order_push_".concat(suffixTableName), fromDayTime, queryNumber);
            if (CollectionUtil.isEmpty(pushIds)) {
                continue;
            }
            CompletableFuture.runAsync(() -> this.clearOrderPush("order_push_".concat(suffixTableName), pushIds, perDeleteNumber), clearExpireOrderPushExecutor);
        }
        /*for (String suffixTableName : suffixTable) {
            //清理order_push_score
            List<Long> scorePushIds = orderPushScoreRepository.selectExpiredOrderPushScore("order_push_score_".concat(suffixTableName), fromDayTime, queryNumber);
            if (CollectionUtil.isEmpty(scorePushIds)) {
                continue;
            }
            CompletableFuture.runAsync(() -> this.clearOrderPushScore("order_push_score_".concat(suffixTableName), scorePushIds, perDeleteNumber), clearExpireOrderPushScoreExecutor);
        }*/
    }

    /**
     * 清理分表order_push记录
     * @param tableName 分表表名
     * @param pushIds 分表清理的主键id
     * @param perDeleteNumber 每次清理的条数
     */
    private void clearOrderPush(String tableName, List<Long> pushIds, int perDeleteNumber) {
        int pushSize = pushIds.size();

        if (CollectionUtils.isNotEmpty(pushIds)) {

            if (pushSize <= perDeleteNumber) {
                orderPushRepository.deleteExpiredOrderPushByPushIds(tableName, pushIds);
            } else {
                for (int startIndex = 0; startIndex < pushSize; startIndex += perDeleteNumber) {
                    int endIndex = Math.min((startIndex + perDeleteNumber), pushSize);

                    try {
                        orderPushRepository.deleteExpiredOrderPushByPushIds(tableName, pushIds.subList(startIndex, endIndex));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    @Override
    @Deprecated
    public BatchUpdatePushDistanceResp batchUpdatePushDistance(BatchUpdatePushDistanceRqt batchUpdatePushDistanceRqt) {
        /*//师傅当前位置纬度
        BigDecimal masterLatitude = batchUpdatePushDistanceRqt.getMasterLatitude();
        //师傅当前位置经度
        BigDecimal masterLongitude = batchUpdatePushDistanceRqt.getMasterLongitude();
        List<BatchUpdatePushDistanceRqt.MasterAddressInfoList> masterAddressInfoList = batchUpdatePushDistanceRqt.getMasterAddressInfoList();
        if (CollectionUtils.isEmpty(masterAddressInfoList)) {
            return new BatchUpdatePushDistanceResp("1");
        }
        //分批更新order_push距离和经纬度信息
        orderPushManagerGateway.updatePushDistanceAndLngLat(masterAddressInfoList, masterLongitude, masterLatitude);*/
        return new BatchUpdatePushDistanceResp("1");
    }

    @Override
    public BatchUpdatePushDistanceResp batchUpdatePushDistanceV2(BatchUpdatePushDistanceV2Rqt batchUpdatePushDistanceV2Rqt) {
        //师傅当前位置纬度
        BigDecimal masterLatitude = batchUpdatePushDistanceV2Rqt.getMasterLatitude();
        //师傅当前位置经度
        BigDecimal masterLongitude = batchUpdatePushDistanceV2Rqt.getMasterLongitude();
        Long masterId = batchUpdatePushDistanceV2Rqt.getMasterId();
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(batchUpdatePushDistanceV2Rqt.getProvinceNextId(),
                "com.wanshifu.infrastructure.gatewayimpl.OrderPushOperateGatewayImpl.batchUpdatePushDistanceV2", JSONUtil.toJsonStr(batchUpdatePushDistanceV2Rqt));
        List<BatchUpdatePushDistanceV2Rqt.MasterAddressInfoList> masterAddressInfoList = batchUpdatePushDistanceV2Rqt.getMasterAddressInfoList();
        if (CollectionUtils.isEmpty(masterAddressInfoList)) {
            return new BatchUpdatePushDistanceResp("1");
        }
        //分批更新order_push距离和经纬度信息
        this.updatePushDistanceAndLngLat(provinceNextId, masterId, masterAddressInfoList, masterLongitude, masterLatitude);
        return new BatchUpdatePushDistanceResp("1");
    }

    @Override
    @Valid
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public int manualClearPush(Long orderId, Long masterId) {
        /*if (masterId != null && masterId > 0) {
            OrderPush orderPushCur = orderPushRepository.selectByOrderIdAndMasterId(0L, orderId, masterId);
            if (Objects.nonNull(orderPushCur) && orderPushCur.getIsDelete() == 0) {
                orderPushRepository.updateNote(0L, orderPushCur.getOrderId(), orderPushCur.getMasterId(), orderPushCur.getNote() + DateUtils.getDateTime() + ":手动删除推送 ");
            }
            orderPushManagerGateway.clearOrderPush(0L, orderId, masterId);
        } else {
            orderPushManagerGateway.softDeleteOrderPush(0L, orderId, DateUtils.getDateTime() + ":手动删除推送 ");
            orderPushManagerGateway.clearOrderPush(0L, orderId, masterId);
        }*/
        return 1;
    }

    private void asyncHandlerChangeTechnology(ResendOrderPushRqt resendOrderPushRqt, Long masterId, String removeTechnologyIds, List<OrderPush> masterNormalList, Map<Long, OrderBase> orderBaseMap) {
        CompletableFuture.supplyAsync(()->{
            removeTechnology(resendOrderPushRqt, masterId, removeTechnologyIds, orderBaseMap,
                    masterNormalList);
            return 1;
        }, executor).exceptionally(e ->{
            FeiShuUtils.sendTempMsg("异步处理技能变更", String.format("请求参数:%s", JSON.toJSONString(resendOrderPushRqt)), e.getMessage());
            return 0;
        });
    }

    private void asyncHandlerChangeDivision(ResendOrderPushRqt resendOrderPushRqt, List<Long> removeServeThirdDivisionIdList,
                                            List<Long> removeServeFourthDivisionIdList, List<OrderPush> masterNormalList, Map<Long, OrderBase> orderBaseMap) {
        CompletableFuture.supplyAsync(()->{
            removeServeDivision(resendOrderPushRqt,
                    removeServeThirdDivisionIdList,
                    removeServeFourthDivisionIdList,
                    masterNormalList, orderBaseMap);

            return 1;

        }, executor).exceptionally(e ->{
            FeiShuUtils.sendTempMsg("异步处理地区变更", String.format("请求参数:%s",JSON.toJSONString(resendOrderPushRqt)), e.getMessage());
            return 0;
        });
    }

    private void asyncHandlerAllChange(ResendOrderPushRqt resendOrderPushRqt, Long masterId, String removeTechnologyIds,
                                       List<Long> removeServeThirdDivisionIdList, List<Long> removeServeFourthDivisionIdList,
                                       List<OrderPush> masterNormalList, Map<Long, OrderBase> orderBaseMap) {
        CompletableFuture.supplyAsync(() -> {
            List<Long> alreadyOrderIds = removeServeDivision(resendOrderPushRqt,
                    removeServeThirdDivisionIdList,
                    removeServeFourthDivisionIdList,
                    masterNormalList, orderBaseMap);

            //移除掉已经删除的订单id;
            if (CollectionUtils.isNotEmpty(alreadyOrderIds)) {
                Map<Long, OrderBase> removeTechnologyOrderBaseMap = new HashMap<>();
                orderBaseMap.keySet().forEach(it ->{
                    if (!alreadyOrderIds.contains(it)){
                        removeTechnologyOrderBaseMap.put(it, orderBaseMap.get(it));
                    }
                });

                orderBaseMap.clear();
                orderBaseMap.putAll(removeTechnologyOrderBaseMap);
                removeTechnologyOrderBaseMap.clear();
            }

            removeTechnology(resendOrderPushRqt, masterId, removeTechnologyIds, orderBaseMap,
                    masterNormalList);
            return 1;
        }, executor).exceptionally(e ->{
            FeiShuUtils.sendTempMsg("异步处理地区和技能变更", String.format("请求参数:%s",JSON.toJSONString(resendOrderPushRqt)), e.getMessage());
            return  0;
        });
    }

    /**
     * 移除服务地址
     * @param resendOrderPushRqt
     * @param removeServeThirdDivisionIdList
     * @param removeServeFourthDivisionIdList
     * @param masterOrderPushList
     * @param orderBaseMap
     */
    private List<Long>  removeServeDivision(ResendOrderPushRqt resendOrderPushRqt,
                                            List<Long> removeServeThirdDivisionIdList,
                                            List<Long> removeServeFourthDivisionIdList,
                                            List<OrderPush> masterOrderPushList,
                                            Map<Long, OrderBase> orderBaseMap) {

        Long masterId = resendOrderPushRqt.getMasterId();
        List<Long> orderIdList = orderPushManagerGateway.modifyMasterServeRegions(resendOrderPushRqt.getProvinceNextIdList(), masterId,
                removeServeThirdDivisionIdList, removeServeFourthDivisionIdList, masterOrderPushList, orderBaseMap);
        //记录因修改服务地区删除的推单
        if (CollectionUtils.isNotEmpty(orderIdList)) {
            log.info("因修改服务地区删除的推单,orderId={}", JSON.toJSONString(orderIdList));
            this.batchInsertDeletePushLog(orderIdList, "modify_serve_region", resendOrderPushRqt);
        }
        return  orderIdList;
    }

    /**
     * 批量插入师傅删除推单记录
     *
     * @param orderIdList
     * @param handleType
     * @param resendOrderPushRqt
     */
    private void batchInsertDeletePushLog(List<Long> orderIdList, String handleType, ResendOrderPushRqt resendOrderPushRqt) {
        List<OrderPushDeleteLog> orderPushDeleteLogs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderIdList)) {
            orderIdList.forEach(orderId -> {
                OrderPushDeleteLog orderPushDeleteLog = new OrderPushDeleteLog();
                orderPushDeleteLog.setMasterId(resendOrderPushRqt.getMasterId());
                orderPushDeleteLog.setOrderId(orderId);
                orderPushDeleteLog.setHandleType(handleType);
                orderPushDeleteLog.setRequestData(JSONObject.toJSONString(resendOrderPushRqt));
                orderPushDeleteLog.setCreateTime(new Date());
                orderPushDeleteLog.setUpdateTime(new Date());
                orderPushDeleteLogs.add(orderPushDeleteLog);
            });
        }

        if (CollectionUtils.isNotEmpty(orderPushDeleteLogs)) {
            orderPushDeleteLogRepository.insertList(orderPushDeleteLogs);
        }
    }

    private List<Long> removeTechnology(ResendOrderPushRqt resendOrderPushRqt,
                                        Long masterId, String removeTechnologyStrIds,
                                        Map<Long, OrderBase> orderBaseMap,
                                        List<OrderPush> orderPushList) {


        if (StringUtils.isEmpty(removeTechnologyStrIds)) {
            return new ArrayList<>();
        }

        List<Long> removeTechnologyIds = StringUtils.splitCommaToList(removeTechnologyStrIds, Long::valueOf);
        List<Long> orderIdList = orderPushManagerGateway.removeTechnologys(resendOrderPushRqt.getProvinceNextIdList(), masterId, removeTechnologyIds,
                orderBaseMap, orderPushList,
                resendOrderPushRqt.getSelectedTechnologyIds());
        //记录因移除技能删除的推单
        if (CollectionUtils.isNotEmpty(orderIdList)) {
            log.info("因移除技能删除的推单,orderId=[{}]", JSON.toJSONString(orderIdList));
            this.batchInsertDeletePushLog(orderIdList, "remove_technology", resendOrderPushRqt);
        }

        return orderIdList;
    }

    @Override
    public int updateOrderPushMenuCategory(List<Long> provinceNextId, Long orderId, Long masterId, Integer menuCategory) {
        return orderPushRepository.updateMenuCategory(provinceNextId, orderId, masterId, menuCategory);
    }

    @Override
    public int updateOrderPushMenuCategory(List<Long> provinceNextId, Long orderId, List<Long> masterIds, Integer menuCategory) {
        return orderPushRepository.updateMenuCategory(provinceNextId, orderId, masterIds, menuCategory);
    }

    @Override
    public PushLessContendOrderResp pushLessContendOrder(PushLessContendOrderRqt rqt) {
        PushLessContendOrderResp resp = new PushLessContendOrderResp();
        int record = orderPushRepository.updateLssContendFlagByOrderIdAndMasterIdList(rqt.getProvinceNextIdList(), rqt.getOrderId(), rqt.getMasterIdList());
        resp.setUpdateSuccessRecord(record);
        return resp;
    }

    @Override
    public void orderPushClear(OrderPushClear orderPushClear) {
        Long orderId = orderPushClear.getOrderId();
        Long masterId = orderPushClear.getMasterId();
        List<Long> provinceNextId = orderPushClear.getProvinceNextIdList();
        if (Objects.nonNull(masterId)) {
            //master_id不为空，可以直接删除
            orderPushRepository.deleteByMasterIdAndOrderId(provinceNextId, masterId, orderId);
        } else {
            //master_id为空，直接按order_id删除
            CompletableFuture.runAsync(() -> {
                int limit = 200;
                int orderPushDeleteCount = orderPushRepository.deleteByOrderIdAndLimit(provinceNextId, orderId, 1, limit);
                while (orderPushDeleteCount > 0) {
                    orderPushDeleteCount = orderPushRepository.deleteByOrderIdAndLimit(provinceNextId, orderId, 1, limit);
                }

            },asyncConfigurer.getAsyncExecutor());
        }
    }

    @Override
    public Integer updateOrderPushOfferTime(List<Long> provinceNextId, UpdateOrderPushOfferTimeRqt updateOrderPushOfferTimeRqt) {

        return orderPushRepository.updateOfferTime(provinceNextId, updateOrderPushOfferTimeRqt.getOrderId(), updateOrderPushOfferTimeRqt.getMasterId(), updateOrderPushOfferTimeRqt.getOfferTime());
    }

    @Override
    public Integer updateOrderPushFirstViewTime(List<Long> provinceNextId, UpdateFirstViewTimeRqt updateFirstViewTimeRqt) {
        return orderPushRepository.updateFirstViewTimeByMasterOrderId(provinceNextId, updateFirstViewTimeRqt.getOrderId(), updateFirstViewTimeRqt.getMasterId(), updateFirstViewTimeRqt.getFirstViewTime());
    }

    @Override
    public Integer updatePullOrderDistance(List<Long> provinceNextId, UpdatePullOrderDistanceRqtV2 updatePullOrderDistanceRqt) {
        return orderPushRepository.updatePullOrderDistanceByMasterOrderId(provinceNextId, updatePullOrderDistanceRqt.getOrderId(), updatePullOrderDistanceRqt.getMasterId(), updatePullOrderDistanceRqt.getIsPullOrderDistance());
    }

    @Override
    public Integer updateIsArrivedByOrderId(List<Long> provinceNextId, Long orderId, Integer isArrived) {
        if (isArrived == 1) {

            while (true) {
                int updateCount = orderPushRepository.updateIsArrivedByOrderId(provinceNextId, orderId, updateLimitCount);
                if (updateCount < updateLimitCount) {
                    break;
                }
            }


        } else if (isArrived == 2) {

            while (true) {
                int updateCount = orderPushRepository.updateNotArrivedByOrderId(provinceNextId, orderId, updateLimitCount);
                if (updateCount < updateLimitCount) {
                    break;
                }
            }
        }

        return 1;
    }

    @Override
    public void updateMenuCategoryBatch(List<Long> provinceNextId, Long orderId, int menuCategory) {
        while (true) {
            int updateCount = orderPushRepository.updateMenuCategoryLimit(provinceNextId, orderId, menuCategory, updateLimitCount);
            if (updateCount < updateLimitCount) {
                break;
            }
        }
    }

    @Override
    public void updateMenuCategoryBatchV2(List<Long> provinceNextId, Long orderId) {
        while (true) {
            int updateCount = orderPushRepository.updateMenuCategoryLimitV2(provinceNextId, orderId, updateLimitCount);
            if (updateCount < updateLimitCount) {
                break;
            }
        }
    }


    @Override
    public void updateOfferBatch(List<Long> provinceNextId, Long orderId) {
        while (true) {
            int updateCount = orderPushRepository.updateOfferLimit(provinceNextId, orderId, updateLimitCount);
            if (updateCount < updateLimitCount) {
                break;
            }
        }
    }

    @Override
    public void agentPushFirstView(List<Long> provinceNextId, Long orderId, Long masterId) {
        log.info("after agentPush,than firstView,orderId:{},masterId:{}", orderId, masterId);

        //订单基本信息
        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        if (orderGrabByIdResp == null || orderGrabByIdResp.getOrderBase() == null) {
            return;
        }
        //推单信息
        OrderPush orderPush = orderPushRepository.selectByOrderIdAndMasterId(provinceNextId, orderId, masterId);
        if (Objects.isNull(orderPush)) {
            return;
        }

        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        Integer businessLineId = orderBase.getBusinessLineId();
        Integer appointType = orderGrab.getAppointType();
        Date orderModifyTime = orderBase.getOrderModifyTime();
        if (businessLineId != 2) {
            return;
        }

        //查询订单标签
        List<OrderExclusiveTagResp> orderExclusiveTagRespList = normalOrderResourceApi.getOrderExclusiveTag(orderId, masterId);
        if (CollectionUtil.isEmpty(orderExclusiveTagRespList)) {
            return;
        }

        List<String> tagNameList = orderExclusiveTagRespList.stream().map(OrderExclusiveTagResp::getTagName).collect(Collectors.toList());
        if (!tagNameList.contains("agent")) {
            return;
        }

        String agentFirstViewAboutString = redisHelper.get(RedisKeyConstant.ORDER_UPDATE_TIME.concat(orderId.toString()));
        log.info("after agentPush,agentPushFirstView,orderId:{},masterId:{},agentFirstViewAbout:{}", orderId, masterId, agentFirstViewAboutString);
        if (Strings.isNullOrEmpty(agentFirstViewAboutString)) {
            //师傅首次查看
            log.info("after agentPush,firstView,orderId:{},masterId:{}", orderId, masterId);
            AgentFirstViewAbout agentFirstViewAbout = new AgentFirstViewAbout();
            agentFirstViewAbout.setOrderModifyTime(orderModifyTime);
            redisHelper.set(RedisKeyConstant.ORDER_UPDATE_TIME.concat(orderId.toString()), JSONUtil.toJsonStr(agentFirstViewAbout), 2 * 24 * 60 * 60);

            this.doSendFirstViewMessage(orderId, masterId, appointType);

        } else {
            //非首次查看,也可能是订单修改后的首次查看
            AgentFirstViewAbout agentFirstViewAboutConfig = JSONObject.parseObject(agentFirstViewAboutString, AgentFirstViewAbout.class);
            log.info("after agentPush,agentPushFirstView,orderId:{},masterId:{},agentFirstViewAbout:{}", orderId, masterId, agentFirstViewAboutString);
            if (Objects.isNull(agentFirstViewAboutConfig) || Objects.isNull(agentFirstViewAboutConfig.getOrderModifyTime())) {
                return;
            }
            Date existsOrderModifyTime = agentFirstViewAboutConfig.getOrderModifyTime();
            if (orderModifyTime.getTime() > existsOrderModifyTime.getTime()) {
                log.info("after agentPush,orderUpdate than firstView,orderId:{},masterId:{}", orderId, masterId);
                //订单修改后的首次查看
                AgentFirstViewAbout agentFirstViewAbout = new AgentFirstViewAbout();
                agentFirstViewAbout.setOrderModifyTime(orderModifyTime);
                redisHelper.set(RedisKeyConstant.ORDER_UPDATE_TIME.concat(orderId.toString()), JSONUtil.toJsonStr(agentFirstViewAbout), 2 * 24 * 60 * 60);

                this.doSendFirstViewMessage(orderId, masterId, appointType);
            }

        }



    }


    /**
     * 获取代理商定向推送后首次查看后多少分钟无人接单再推普通师傅的配置并发送延迟消息
     * @param orderId
     * @param masterId
     * @param appointType
     */
    private void doSendFirstViewMessage(Long orderId, Long masterId, Integer appointType) {
        String agentFirstViewNoHiredRePushTimeString = redisHelper.get(RedisKeyConstant.AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME_KEY.concat(orderId.toString()));
        log.info("after agentPush,agentPushFirstView,orderId:{},masterId:{},agentFirstViewNoHiredRePushTimeString:{}", orderId, masterId, agentFirstViewNoHiredRePushTimeString);
        if (Strings.isNullOrEmpty(agentFirstViewNoHiredRePushTimeString)) {
            log.error("after agentPush,agentPushFirstView agentFirstViewNoHiredRePushTime config is empty!orderId:{},masterId:{}", orderId, masterId);
            return;
        }
        AgentFirstViewNoHiredRePushTime notHiredRePushTimeConfig = JSONObject.parseObject(agentFirstViewNoHiredRePushTimeString, AgentFirstViewNoHiredRePushTime.class);
        if (Objects.isNull(notHiredRePushTimeConfig) || Objects.isNull(notHiredRePushTimeConfig.getAgentFirstViewNoHiredRePushTime())) {
            return;
        }
        Integer agentFirstViewNoHiredRePushTime = notHiredRePushTimeConfig.getAgentFirstViewNoHiredRePushTime();

        this.sendAgentPushMessage(orderId, appointType, agentFirstViewNoHiredRePushTime * 60 * 1000L);
    }
    /**
     * 代理商推单后，查看后多少分钟无人接单推普通师傅
     * @param orderId
     * @param appointType
     * @param delayTime
     * @return
     */
    private boolean sendAgentPushMessage(Long orderId, Integer appointType, Long delayTime) {

        AgentPushRqt agentPushRqt = new AgentPushRqt();
        agentPushRqt.setMasterOrderId(orderId);
        agentPushRqt.setAppointType(appointType);
        agentPushRqt.setTouchType("firstView");
        log.info("sendAgentPushMessage by firstView,sendAgentPushMessage:{}", JSONUtil.toJsonStr(agentPushRqt));
        rocketMqSendService.sendDelayMessage(orderMatchMasterTopic, "agent_push", JSON.toJSONString(agentPushRqt), delayTime);
        return true;
    }


    private Set<Long> executePush(List<Long> provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite,
                                  List<MasterAddressInfo> masterAddressInfoList,
                                  Date pushTime, Integer pushDivisionLevel,
                                  Integer pushFrom, String techniqueTypeIds,
                                  Integer agentOrderFlag, Integer pushFlag,
                                  Integer accordingDistancePushFlag,
                                  List<ServeStop> serveStops, Integer exclusiveFlag,
                                  String masterSourceType) {

        OrderBase orderBase = pushOrderComposite.getOrderBaseComposite().getOrderBase();
        Set<Long> masterIdSet = masterAddressInfoList.stream().map(MasterAddressInfo::getMasterId).collect(Collectors.toSet());
        if (masterIdSet.size() > 0) {
            if (AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())) {
                this.enterpriseOrderFilterMaster(pushOrderComposite, masterAddressInfoList,
                        serveStops);
            }
            if ("on".equals(pushDeadLockSwitch)) {

                CompletableFuture.runAsync(() -> {
                    try {
                        orderPushManagerGateway.pushOrderHandle(provinceNextId, pushMold, pushOrderComposite, masterAddressInfoList,
                                pushTime, pushDivisionLevel, pushFrom,
                                techniqueTypeIds, agentOrderFlag,
                                pushFlag, accordingDistancePushFlag, exclusiveFlag, masterSourceType);
                    } catch (Exception e) {
                        //发飞书告警
                        FeiShuUtils.sendTempMsg("pushOrderHandle", "orderId:".concat(orderBase.getOrderId().toString()), "推单落库失败!请立即处理！\n".concat(ExceptionHelper.substrException(e)));
                    }
                }, orderPushExecutor);


            } else {
                if (masterIdSet.size() < 10) {
                    //小于10行直接同步写入表
                    orderPushManagerGateway.pushSync(provinceNextId, pushMold, pushOrderComposite, masterAddressInfoList,
                            pushTime, pushDivisionLevel, pushFrom,
                            techniqueTypeIds, agentOrderFlag,
                            pushFlag, accordingDistancePushFlag, exclusiveFlag, masterSourceType);
                } else {
                    orderPushManagerGateway.pushAsync(provinceNextId, pushMold, pushOrderComposite, masterAddressInfoList,
                            pushTime, pushDivisionLevel, pushFrom,
                            techniqueTypeIds, agentOrderFlag,
                            pushFlag, accordingDistancePushFlag, exclusiveFlag, masterSourceType);
                }
            }

        }

        return masterIdSet;
    }




    /**
     * 总包订单筛选师傅
     ** @param masterAddressInfoList
     * @return
     */
    private void enterpriseOrderFilterMaster(PushingOrderCompositeResp pushOrderComposite,
                                             List<MasterAddressInfo> masterAddressInfoList,
                                             List<ServeStop>  serveStops ) {

        OrderBase orderBase = pushOrderComposite.getOrderBaseComposite().getOrderBase();
        List<MasterOrderOfferPrice> orderOfferPrices = pushOrderComposite.getOrderOfferPrices();
        Set<Long> undesirableMasterId = new HashSet<>();

        Long orderId = orderBase.getOrderId();

        //总包外部订单:过滤掉已取消指派的师傅
        if (OrderFrom.ENTERPRISE_SYSTEM.valueEn.equals(orderBase.getOrderFrom())) {
            if (CollectionUtils.isNotEmpty(serveStops)) {
                undesirableMasterId.addAll(serveStops.stream().map(ServeStop::getMasterId).collect(Collectors.toList()));
            }
        } else { //总包非外部订单：过滤掉已经报过价的师傅
            if (CollectionUtils.isNotEmpty(orderOfferPrices)) {
                undesirableMasterId.addAll(orderOfferPrices.stream().map(OrderOfferPrice::getMasterId).collect(Collectors.toList()));
            }
        }

        if (CollectionUtils.isNotEmpty(undesirableMasterId)) {
            log.warn("总包订单：orderId={},不符合规则的师傅集合：{}", orderId, JSON.toJSONString(undesirableMasterId));
            masterAddressInfoList.removeIf(masterAddressInfo -> undesirableMasterId.contains(masterAddressInfo.getMasterId()));
        }
    }

    private void updatePushDistanceAndLngLat(List<Long> provinceNextId, Long masterId, List<BatchUpdatePushDistanceV2Rqt.MasterAddressInfoList> masterAddressInfoList, BigDecimal masterLongitude, BigDecimal masterLatitude) {
        List<List<BatchUpdatePushDistanceV2Rqt.MasterAddressInfoList>> masterAddressInfoLists = CollectionUtil.split(masterAddressInfoList, 20);
        masterAddressInfoLists.forEach(addressInfoList -> CompletableFuture.runAsync(() -> this.updateMasterAddress(provinceNextId, masterId, addressInfoList, masterLongitude, masterLatitude), executor));
    }

    private void updateMasterAddress(List<Long> provinceNextId, Long masterId, List<BatchUpdatePushDistanceV2Rqt.MasterAddressInfoList> masterAddressInfoList, BigDecimal masterLongitude, BigDecimal masterLatitude) {
        masterAddressInfoList.forEach(masterAddressInfo -> {
            orderPushRepository.updateMasterAddressByOrderId(provinceNextId, masterId, masterAddressInfo.getOrderId(),
                    masterLongitude, masterLatitude,
                    masterAddressInfo.getPushDistance(), masterAddressInfo.getPushDistanceType());
        });
    }


    /**
     * 更新orderPush的firstViewTime
     *
     * @param updatePullViewTimeRqt
     * @return
     */
    @Override
    public Integer updatePullViewTime(List<Long> provinceNextId, UpdatePullViewTimeRqt updatePullViewTimeRqt){
        return orderPushRepository.updateIsPullViewByMasterOrderId(provinceNextId, updatePullViewTimeRqt.getOrderIdList(), updatePullViewTimeRqt.getMasterId(), updatePullViewTimeRqt.getViewTime());
    }

    @Override
    public void recordFirstPushMatchMaster(OrderPushRqt orderPushRqt) {
        if (Objects.isNull(orderPushRqt)) {
            log.error("recordFirstPushMatchMaster error! because invalid params!");
            return;
        }

        Integer normalFirstTimeValidPush = orderPushRqt.getNormalFirstTimeValidPush();
        Integer matchMasterNum = orderPushRqt.getNormalFirstMatchMasterNum();

        if (Objects.nonNull(normalFirstTimeValidPush)
                && normalFirstTimeValidPush == 1
                && Objects.nonNull(matchMasterNum)
                && matchMasterNum > 0) {

            Long globalOrderId = orderPushRqt.getGlobalOrderTraceId();
            Long orderId = orderPushRqt.getOrderId();

            log.info("recordFirstPushMatchMaster orderId:{},globalOrderId:{},matchMasterNum:{}", orderId, globalOrderId, matchMasterNum);


            Date now = new Date();

            FirstPushMatchMaster matchMaster = new FirstPushMatchMaster();
            matchMaster.setOrderId(orderId);
            matchMaster.setGlobalOrderId(globalOrderId);
            matchMaster.setMatchMasterNum(matchMasterNum);
            matchMaster.setCreateTime(now);
            matchMaster.setUpdateTime(now);
            firstPushMatchMasterRepository.insertFirstPushMatchMaster(matchMaster);

        }


    }


}
