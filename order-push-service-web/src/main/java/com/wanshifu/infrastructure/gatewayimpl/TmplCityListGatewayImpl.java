package com.wanshifu.infrastructure.gatewayimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanshifu.domain.tmplcity.bo.*;
import com.wanshifu.domain.tmplcity.gateway.TmplCityListGateway;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.infrastructure.TmplCityOrderPushLog;
import com.wanshifu.infrastructure.TmplCityOrderRecommendMaster;
import com.wanshifu.infrastructure.provider.MapperProviderSupport;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 14:33
 */
@Service
public class TmplCityListGatewayImpl extends MapperProviderSupport implements TmplCityListGateway {

    @Override
    public SimplePageInfo<ListTmplCityOrderRecommendMasterRespBo> orderRecommendMasterList(ListTmplCityOrderRecommendMasterRqtBo rqt) {
        PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        List<TmplCityOrderRecommendMaster> tmplCityOrderRecommendMasterList = tmplCityOrderRecommendMasterRepository.listTmplCityOrderRecommendMaster(rqt);
        if (CollectionUtil.isEmpty(tmplCityOrderRecommendMasterList)) {
            return new SimplePageInfo<>();
        }
        PageInfo<TmplCityOrderRecommendMaster> simplePageInfo = new PageInfo<>(tmplCityOrderRecommendMasterList);
        List<ListTmplCityOrderRecommendMasterRespBo> listTmplCityOrderRecommendMasterRespBoList = tmplCityOrderRecommendMasterList.stream()
                .map(tmplCityOrderRecommendMaster -> BeanUtil.toBean(tmplCityOrderRecommendMaster, ListTmplCityOrderRecommendMasterRespBo.class))
                .collect(Collectors.toList());

        SimplePageInfo<ListTmplCityOrderRecommendMasterRespBo> respBoSimplePageInfo = new SimplePageInfo<>(listTmplCityOrderRecommendMasterRespBoList);
        respBoSimplePageInfo.setPageNum(simplePageInfo.getPageNum());
        respBoSimplePageInfo.setPageSize(simplePageInfo.getPageSize());
        respBoSimplePageInfo.setPages(simplePageInfo.getPages());
        respBoSimplePageInfo.setTotal(simplePageInfo.getTotal());

        return respBoSimplePageInfo;
    }

    @Override
    public List<ListTmplCityOrderRecommendMasterRespBoV2> orderRecommendMasterListV2(ListTmplCityOrderRecommendMasterRqtBoV2 rqt) {
        List<TmplCityOrderRecommendMaster> tmplCityOrderRecommendMasterList = tmplCityOrderRecommendMasterRepository.listTmplCityOrderRecommendMasterV2(rqt);
        if (CollectionUtil.isEmpty(tmplCityOrderRecommendMasterList)) {
            return new ArrayList<>();
        }
        return tmplCityOrderRecommendMasterList.stream().map(tmplCityOrderRecommendMaster -> BeanUtil.toBean(tmplCityOrderRecommendMaster, ListTmplCityOrderRecommendMasterRespBoV2.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<ListTmplCityOrderPushLogRespBo> orderPushLogList(ListTmplCityOrderPushLogRqtBo rqt) {
        List<TmplCityOrderPushLog> poList = tmplCityOrderPushLogRepository.listTmplCityOrderPushLog(rqt);
        if (CollectionUtil.isEmpty(poList)) {
            return new ArrayList<>();
        }

        return poList.stream().map(po -> BeanUtil.toBean(po, ListTmplCityOrderPushLogRespBo.class))
                .collect(Collectors.toList());
    }
}
