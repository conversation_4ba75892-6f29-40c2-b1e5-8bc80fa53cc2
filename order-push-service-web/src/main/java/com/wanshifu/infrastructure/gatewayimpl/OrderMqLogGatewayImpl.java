package com.wanshifu.infrastructure.gatewayimpl;

import com.wanshifu.domain.corn.model.MaxAndMinPrimaryIdBo;
import com.wanshifu.domain.corn.gateway.OrderMqLogGateway;
import com.wanshifu.infrastructure.repository.OrderMqLogRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Date 2023-11-01 10:24
 * @Description
 * @Version v1
 **/
@Component
public class OrderMqLogGatewayImpl implements OrderMqLogGateway {

    @Resource
    private OrderMqLogRepository orderMqLogRepository;
    public static  final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(4,4,0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>());



    @Override
    public Integer clear(Long branchNumber,Long everyTimeSumNumber,Long maxLogNumber) {
        Long startTime = System.currentTimeMillis();
        // 每天凌晨1点执行 、 2点 、 3点
        // 查询最小Id 和最大ID, 确定最大数据量， 每天清除分区出当天数量
        // 1 - 100万
        // 101万 - 200万
        MaxAndMinPrimaryIdBo maxAndMinPrimaryIdBo = orderMqLogRepository.selectMaxAndMinPrimaryId();
        if (Objects.isNull(maxAndMinPrimaryIdBo) || maxAndMinPrimaryIdBo.getSumCount() <=0) {
            return 0;
        }
        //数据库最大仅记录200万数据
        Long sumCount = maxAndMinPrimaryIdBo.getSumCount();
        if (sumCount <= maxLogNumber) {
            return 0 ;
        }
        Long minId = maxAndMinPrimaryIdBo.getMinId();
        Long maxId = maxAndMinPrimaryIdBo.getMaxId();
        //id都是采用连续自增的，计算待清理数量总数量
        Long waitClearNumber = sumCount - maxLogNumber;
        //对比每次清理的数据量，如果当前清理大于每次清理，则取前面
        if (waitClearNumber >= everyTimeSumNumber) {
            waitClearNumber = everyTimeSumNumber;
        }
        //剩余数量
        Long residueNumber = waitClearNumber % branchNumber ;
        //计算处理批次
        Long branchSize = residueNumber > 0 ?  waitClearNumber / branchNumber + 1 : waitClearNumber / branchNumber ;
        Long countMinId = minId;
        Long countMaxId = 0L;
        Map<Long,Long> allocationIdMap = new ConcurrentHashMap<>();

        for (int i=0;i < branchSize ; i++ ) {
            countMaxId = countMinId + branchNumber -1;
            if (i == branchSize - 1) {
                if (residueNumber > 0) {
                    countMaxId = countMinId + residueNumber - 1;
                }
            }
            allocationIdMap.put(countMinId,countMaxId);
            countMinId = countMaxId + 1;
        }

        if (allocationIdMap.size() == 0) {
            return 0 ;
        }
        CountDownLatch countDownLatch = new CountDownLatch(allocationIdMap.size());
        allocationIdMap.keySet().forEach( it ->{
            threadPoolExecutor.execute(()->{
                orderMqLogRepository.deleteByStartAndEndOfferId(it, allocationIdMap.get(it));
                //执行业务
                countDownLatch.countDown();
                System.out.println("thread name"+ Thread.currentThread().getName());
            });
            System.out.println(" main1 thread name"+ Thread.currentThread().getName());

        });
        try {
            //10分钟未处理完成，
            countDownLatch.await(10,TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            e.printStackTrace();
            return -1;
        }

        Long endTime = System.currentTimeMillis();

        Long time =  endTime - startTime;
        System.out.println(" main2 thread name"+ Thread.currentThread().getName());

        return time.intValue() /1000;
    }
}
