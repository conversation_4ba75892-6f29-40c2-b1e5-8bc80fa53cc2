package com.wanshifu.infrastructure.gatewayimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.base.model.RedisKeyConstant;
import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.push.gateway.OrderPushListGateway;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.model.*;
import com.wanshifu.domain.push.model.enums.BusinessLineIdEnum;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.smartsort.SmartSortPositionConfigService;
import com.wanshifu.domain.special.ability.SpecialOrderAbilityService;
import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DataUtils;
import com.wanshifu.infrastructure.FirstPushMatchMaster;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.SmartSortPositionConfig;
import com.wanshifu.infrastructure.client.HBaseClient;
import com.wanshifu.infrastructure.gatewayreq.push.MasterAppletListOrderPushGatewayRqt;
import com.wanshifu.infrastructure.provider.MapperProviderSupport;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.domain.api.request.common.GetMasterInfoListByIdsRqt;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoListByIdsResp;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.dto.serve.ServeIdSetReq;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.*;
import com.wanshifu.order.offer.domains.api.response.infoorder.InfoOrderBaseComposite;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.enums.OrderTagEnum;
import com.wanshifu.order.offer.domains.po.*;
import com.wanshifu.order.offer.domains.vo.infoorder.InfoOrderGoodsComposite;
import com.wanshifu.order.push.enums.PushBusinessCode;
import com.wanshifu.order.push.request.WaitOfferSpecialListRqt;
import com.wanshifu.order.push.request.push.TmplCityOrderPushRqt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/22 18:52
 */

@Component
@Slf4j
public class OrderPushListGatewayImpl extends MapperProviderSupport implements OrderPushListGateway {

    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    @Resource
    private SpecialOrderGateway specialOrderGateway;

    @Resource
    private SpecialOrderAbilityService specialOrderAbilityService;

    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;

    @Resource
    private SmartSortPositionConfigService smartSortPositionConfigService;

    @Resource
    private ServeServiceApi serveServiceApi;

    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;

    @Resource
    private CallGateway callGateway;

    @Resource
    private HBaseClient hBaseClient;

    @Autowired
    private RedisHelper redisHelper;

    @Resource(name = "updateTmplCityTipTimeExecutor")
    private Executor executor;

    @Resource(name = "concurrentProcessExecutor")
    private Executor concurrentProcessExecutor;

    @Value("${order.orderPush.exposurePositionFlagSwitch:on}")
    private String exposurePositionFlagSwitch;

    @Override
    public SimplePageInfo<WaitOfferV2RespBo> waitOfferSpecialList(List<Long> provinceNextIds, WaitOfferSpecialListRqt rqt) {
        SimplePageInfo<WaitOfferV2RespBo> simplePageInfo = new SimplePageInfo<>();

        //订单距离开关,附近单
        if (!specialOrderAbilityService.getSpecialOrderSwitch(2)) {
            return simplePageInfo;
        }
        //查询订单信息
        OrderBase orderBase;
        OrderGrab orderGrab;

        SimpleOrderGrab simpleOrderGrab = commonOrderOfferService.getSimpleOrderGrabByOrderId(rqt.getOfficeOrderId());
        if (Objects.isNull(simpleOrderGrab)) {
            throw new BusException(PushBusinessCode.BUS_VALIDATION_ERROR.code, "获取订单基础信息失败");
        }
        orderBase = simpleOrderGrab.getOrderBase();
        orderGrab = simpleOrderGrab.getOrderGrab();

        Assert.notNull(orderGrab, "订单指派记录不存在");


        Long pushDistance = null;

        if (!rqt.isSkipCheckOrderPushDistance()) {
            OrderPush push = orderPushRepository.selectByOrderIdAndMasterId(provinceNextIds, rqt.getOfficeOrderId(), rqt.getMasterId());
            if (ObjectUtil.isNull(push) || push.getPushDistance() == 0) {
                return simplePageInfo;
            }
            pushDistance = push.getPushDistance();
        }

        //样板城市订单二期项目，根据不同师傅角色查询推单
        List<Integer> tmplCityFlag = Lists.newArrayList();
        Integer tmplCityMasterRole = rqt.getTmplCityMasterRole();
        if (Objects.nonNull(tmplCityMasterRole)) {
            if (tmplCityMasterRole == 1) {
                //主力师傅只查样板城市订单
                tmplCityFlag.add(1);
            } else if (tmplCityMasterRole == 3) {
                //普通师傅只查非样板城市订单+样板城市订单转推普通师傅订单
                tmplCityFlag.add(0);
                tmplCityFlag.add(2);
            }
        }

        Integer specialSideType = rqt.getSpecialSideType();
        //附近单列表
        //查询师傅待报价列表push
        List<OrderPush> orderPush = specialOrderGateway.getFilterNearbyOrderPush(provinceNextIds, orderBase.getOrderId(),
                rqt.getMasterId(), orderGrab.getSecondDivisionId(), specialSideType, orderBase.getCategoryId(),
                pushDistance, rqt.isSkipCheckOrderPushDistance(), tmplCityFlag);

        //获取待报价列表组装数据
        List<WaitOfferV2RespBo> waitOfferRespList = this.getWaitOfficeResp(provinceNextIds, orderPush, orderGrab.getSecondDivisionId(), rqt.getMasterId(), 5);
        SimplePageInfo<WaitOfferV2RespBo> respSimplePageInfo = new SimplePageInfo<>(waitOfferRespList);
        return respSimplePageInfo;
    }

    /**
     * 获取待报价列表组装数据
     *
     * @param orderPushList
     * @param cityId
     * @param pageSize
     * @return
     */
    public List<WaitOfferV2RespBo> getWaitOfficeResp(List<Long> provinceNextIds, List<OrderPush> orderPushList,
                                                     Long cityId, Long masterId,
                                                     Integer pageSize) {
        List<WaitOfferV2RespBo> waitOfferRespList = new ArrayList<>(pageSize);
        if (CollectionUtils.isEmpty(orderPushList)) {
            return waitOfferRespList;
        }

        OrderInfoBatchComposite orderInfoBatchComposite = null;
        List<Long> orderIds = orderPushList.stream().filter(orderPush -> orderPush.getIsIntention() == 0).map(OrderPush::getOrderId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(orderIds)) {
            orderInfoBatchComposite = commonOrderOfferService.getOrderInfoBatchComposite(orderIds, masterId);
        }

        List<InfoOrderBaseComposite> infoOrderBaseCompositeList = new ArrayList<>();
        List<Long> intentionOrderIds = orderPushList.stream().filter(orderPush -> orderPush.getIsIntention() == 1).map(OrderPush::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(intentionOrderIds)) {
            infoOrderBaseCompositeList = commonOrderOfferService.batchGetInfoOrderBaseComposite(intentionOrderIds);
        }


        for (OrderPush orderPush : orderPushList) {
            Integer isIntention = orderPush.getIsIntention();
            WaitOfferV2RespBo waitOfferInfoResp;
            //是否是意向单,分开取订单信息
            if (1 == isIntention) {
                waitOfferInfoResp = this.setReceivingListV2Resp(orderPush, infoOrderBaseCompositeList);
            } else {
                waitOfferInfoResp = this.setWaitOfferV2Resp(provinceNextIds, orderPush, orderInfoBatchComposite, cityId);
                handlerSpecialOrder(waitOfferInfoResp);

            }
            waitOfferRespList.add(waitOfferInfoResp);
        }

        return waitOfferRespList;
    }

    private void handlerSpecialOrder(WaitOfferV2RespBo waitOfferV2Resp) {
        Integer isClearOrderDistance = waitOfferV2Resp.getIsClearOrderDistance();
        OrderBase orderBase = waitOfferV2Resp.getOrderBase();

        //V7.12首次拉取订单,幂等(订单距离表),附近单发送mq
        if (isClearOrderDistance == 1) {
            //发送附近单mq
            specialOrderGateway.nearbyOrderPushDistance(orderBase.getOrderId());
        }
    }


    /**
     * 查询意向单订单相关信息
     *
     * @param orderPush
     * @return
     */
    public WaitOfferV2RespBo setReceivingListV2Resp(OrderPush orderPush, List<InfoOrderBaseComposite> infoOrderBaseCompositeList) {
        Long orderId = orderPush.getOrderId();

        Map<Long, InfoOrderBaseComposite> infoOrderBaseMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(infoOrderBaseCompositeList)){
            infoOrderBaseMap = infoOrderBaseCompositeList.stream().collect(Collectors.toMap(infoOrderBaseComposite -> infoOrderBaseComposite.getInfoOrderBase().getOrderId(), Function.identity()));
        }


        WaitOfferV2RespBo waitOfferResp = new WaitOfferV2RespBo();

        InfoOrderBase infoOrderBase = Objects.nonNull(infoOrderBaseMap.get(orderId)) ? infoOrderBaseMap.get(orderId).getInfoOrderBase() : null;
        Assert.notNull(infoOrderBase, "创新业务-订单信息不存在");

        waitOfferResp.setInfoOrderBase(infoOrderBase);

        waitOfferResp.setIsIntention(orderPush.getIsIntention());
        waitOfferResp.setAppointType(orderPush.getAppointType());
        waitOfferResp.setOfferNumber(0);
        waitOfferResp.setStopOfferTime(orderPush.getStopOfferTime());
        waitOfferResp.setPushDistance(orderPush.getPushDistance());
        waitOfferResp.setPushDistanceType(orderPush.getPushDistanceType());
        waitOfferResp.setFirstPullTime(orderPush.getFirstPullTime());
        waitOfferResp.setPushTime(orderPush.getPushTime());
        waitOfferResp.setMenuCategory(orderPush.getMenuCategory());

        InfoOrderExtraData infoorderExtraData = Objects.nonNull(infoOrderBaseMap.get(orderId)) ? infoOrderBaseMap.get(orderId).getInfoOrderExtraData() : null;
        Assert.notNull(infoorderExtraData, "创新业务-订单扩展信息不存在");
        //屏蔽订单联系人手机号
        InfoOrderExtraData extraData = DataUtils.copyObject(infoorderExtraData, InfoOrderExtraData.class);
        extraData.setContactPhone(StrUtil.EMPTY);
        waitOfferResp.setInfoOrderExtraData(infoorderExtraData);

        waitOfferResp.setInfoOrderAttachment(Objects.nonNull(infoOrderBaseMap.get(orderId)) ? infoOrderBaseMap.get(orderId).getInfoOrderAttachment() : Collections.emptyList());

        ArrayList<InfoOrderGoodsComposite> compositeList = new ArrayList<>();
        List<InfoOrderGoodsComposite> infoOrderGoodsComposites = Objects.nonNull(infoOrderBaseMap.get(orderId)) ? infoOrderBaseMap.get(orderId).getInfoOrderGoodsComposite() : Collections.emptyList();
        if(CollectionUtils.isNotEmpty(infoOrderGoodsComposites)){
            for (InfoOrderGoodsComposite infoOrderGoodsComposite : infoOrderGoodsComposites) {
                InfoOrderGoodsComposite composite = new InfoOrderGoodsComposite();
                composite.setInfoOrderGoods(infoOrderGoodsComposite.getInfoOrderGoods());
                composite.setInfoOrderAttachment(infoOrderGoodsComposite.getInfoOrderAttachment());
                compositeList.add(composite);
            }
        }
        waitOfferResp.setInfoOrderGoodsComposite(compositeList);

        return waitOfferResp;
    }


    public WaitOfferV2RespBo setWaitOfferV2Resp(List<Long> provinceNextIds, OrderPush orderPush, OrderInfoBatchComposite orderInfoBatchComposite, Long cityId) {
        Long orderId = orderPush.getOrderId();
        Long masterId = orderPush.getMasterId();
        WaitOfferV2RespBo waitOfferResp = new WaitOfferV2RespBo();
        waitOfferResp.setIsIntention(orderPush.getIsIntention());
        waitOfferResp.setAppointType(orderPush.getAppointType());
        waitOfferResp.setOfferNumber(0);
        waitOfferResp.setStopOfferTime(orderPush.getStopOfferTime());
        waitOfferResp.setPushDistance(orderPush.getPushDistance());
        waitOfferResp.setPushDistanceType(orderPush.getPushDistanceType());
        waitOfferResp.setFirstPullTime(orderPush.getFirstPullTime());
        waitOfferResp.setPushTime(orderPush.getPushTime());
        waitOfferResp.setMenuCategory(orderPush.getMenuCategory());
        if (!Strings.isNullOrEmpty(orderPush.getNote()) && orderPush.getNote().contains("exposureOrder")) {
            waitOfferResp.setExposurePositionFlag(1);
        } else {
            waitOfferResp.setExposurePositionFlag(0);
        }

        OrderBase orderBase = null;
        OrderExtraData orderExtraData = null;
        OrderLogisticsInfo orderLogisticsInfo = null;
        OrderReturnLogistics orderReturnLogistics = null;
        OrderInitFee orderInitFee = null;
        OrderAward orderAward = null;
        OrderEpcGenreData orderEpcGenre = null;
        EmergencyOrder emergencyOrder = null;
        OrderTag orderTag = null;
        OrderTag noticeOrderTag = null;
        OrderGrab orderGrab = null;

        OrderBountyInfo orderBountyInfo = null;
        List<OrderExclusiveTagResp> orderTagResp = null;
        OrderRateAward orderRateAward = null;
        Integer imageCount = 0;
        boolean hasVideo = false;
        List<IkeaOrderGoodsComposite> ikeaOrderGoodsCompositeList = new ArrayList<>();
        List<OrderGoodsComposite> orderGoodsCompositeList = new ArrayList<>();
        List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = new ArrayList<>();
        List<OrderAddItemServiceInfo> orderAddItemServiceInfoList = new ArrayList<>();
        List<OrderAutoGrab> orderAutoGrabList = new ArrayList<>();
        List<OrderParts> orderPartsList = new ArrayList<>();
        Integer remainRecommendOfferNum = 0;

        if (orderInfoBatchComposite != null) {
            List<OrderBase> orderBases = orderInfoBatchComposite.getOrderBaseList();
            List<OrderExtraData> orderExtraDataList = orderInfoBatchComposite.getOrderExtraDataList();
            List<OrderLogisticsInfo> orderLogisticsInfos = orderInfoBatchComposite.getOrderLogisticsInfoList();
            List<OrderReturnLogistics> orderReturnLogisticList = orderInfoBatchComposite.getOrderReturnLogisticList();
            List<OrderInitFee> orderInitFeeList = orderInfoBatchComposite.getOrderInitFeeList();
            List<OrderGrab> orderGrabList = orderInfoBatchComposite.getOrderGrabList();
            List<OrderBountyInfo> orderBountyInfoList = orderInfoBatchComposite.getOrderBountyInfoList();

            orderInitFee = orderInitFeeList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElseThrow(() -> new BusException("订单初始费用信息不存在"));
            orderBase = orderBases.stream().filter(order -> order.getOrderId().equals(orderId)).findFirst().orElseThrow(() -> new BusException("订单基础信息不存在"));
            orderExtraData = orderExtraDataList.stream().filter(extra -> extra.getOrderId().equals(orderId)).findFirst().orElseThrow(() -> new BusException("订单扩展信息不存在"));
            orderGrab = orderGrabList.stream().filter(grab -> grab.getOrderId().equals(orderId)).findFirst().orElse(null);
            orderLogisticsInfo = orderLogisticsInfos.stream().filter(logistic -> logistic.getOrderId().equals(orderId)).findFirst().orElse(null);
            orderReturnLogistics = orderReturnLogisticList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            if (CollectionUtil.isNotEmpty(orderBountyInfoList)) {
                orderBountyInfo = orderBountyInfoList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }

            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getOrderAwardList())) {
                orderAward = orderInfoBatchComposite.getOrderAwardList().stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getOrderEpcGenreDataList())) {
                orderEpcGenre = orderInfoBatchComposite.getOrderEpcGenreDataList().stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getEmergencyOrderList())) {
                emergencyOrder = orderInfoBatchComposite.getEmergencyOrderList().stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getOrderTagList())) {
                orderTag = orderInfoBatchComposite.getOrderTagList().stream().filter(o -> o.getOrderId().equals(orderId) && Objects.equals(o.getTagValue(), OrderTagEnum.AGENT_ORDER.type)).findFirst().orElse(null);
                noticeOrderTag = orderInfoBatchComposite.getOrderTagList().stream().filter(o -> o.getOrderId().equals(orderId) && Objects.equals(o.getTagValue(), OrderTagEnum.LESS_CONTEND.type)).findFirst().orElse(null);
            }

            List<OrderExclusiveTagResp> orderTagResps = orderInfoBatchComposite.getOrderTagResp();
            if (CollectionUtils.isNotEmpty(orderTagResps)) {
                Map<Long, List<OrderExclusiveTagResp>> tagMap = orderTagResps.stream().collect(Collectors.groupingBy(OrderExclusiveTagResp::getOrderId));
                if (Objects.nonNull(tagMap)) {
                    orderTagResp = tagMap.get(orderId);
                }
            }

            List<OrderRateAward> orderRateAwardList = orderInfoBatchComposite.getOrderRateAwardList();
            if (CollectionUtils.isNotEmpty(orderRateAwardList)) {
                orderRateAward = orderRateAwardList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }

            Map<Long, Integer> imageCountMap = orderInfoBatchComposite.getImageCountMap();
            if (Objects.nonNull(imageCountMap)) {
                imageCount = imageCountMap.get(orderId);
            }
            Map<Long, Integer> videoNumberMap = orderInfoBatchComposite.getVideoNumberMap();
            if (Objects.nonNull(videoNumberMap) && Objects.nonNull(videoNumberMap.get(orderId))) {
                hasVideo = videoNumberMap.get(orderId) > 0;
            }


            if (OrderFrom.IKEA.valueEn.equals(orderBase.getOrderFrom())) {
                List<IkeaOrderGoodsComposite> ikeaOrderGoodsComposites = orderInfoBatchComposite.getIkeaOrderGoodsComposites();
                if (CollectionUtils.isNotEmpty(ikeaOrderGoodsComposites)) {
                    ikeaOrderGoodsCompositeList = ikeaOrderGoodsComposites.stream().filter(o -> orderId.equals(o.getOrderIkeaGoods().getOrderId())).collect(Collectors.toList());
                }
            } else {
                if (orderBase.getOrderServeVersion() == 0) {
                    List<OrderGoodsComposite> orderGoodsComposites = orderInfoBatchComposite.getOrderGoodsComposites();
                    if (CollectionUtils.isNotEmpty(orderGoodsComposites)) {
                        orderGoodsCompositeList = orderGoodsComposites.stream().filter(o -> orderId.equals(o.getOrderGoods().getOrderId())).collect(Collectors.toList());
                    }
                } else {
                    List<OrderServiceAttributeInfo> orderServiceAttributeInfos = orderInfoBatchComposite.getOrderServiceAttributeInfos();
                    if (CollectionUtils.isNotEmpty(orderServiceAttributeInfos)) {
                        orderServiceAttributeInfoList = orderServiceAttributeInfos.stream().filter(o -> orderId.equals(o.getOrderId())).collect(Collectors.toList());
                    }
                }
            }


            if(CollectionUtils.isNotEmpty(orderInfoBatchComposite.getOrderAddItemServiceInfoList())){
                orderAddItemServiceInfoList = orderInfoBatchComposite.getOrderAddItemServiceInfoList().stream().filter(o -> orderId.equals(o.getOrderId())).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(orderInfoBatchComposite.getOrderAutoGrabList())) {
                orderAutoGrabList = orderInfoBatchComposite.getOrderAutoGrabList().stream().filter(o -> orderId.equals(o.getOrderId())).collect(Collectors.toList());
            }
            if (Objects.nonNull(orderInfoBatchComposite.getRemainRecommendOfferNumMap())) {
                remainRecommendOfferNum = orderInfoBatchComposite.getRemainRecommendOfferNumMap().get(orderId);
            }
            if (CollectionUtils.isNotEmpty(orderInfoBatchComposite.getOrderPartsList())) {
                orderPartsList = orderInfoBatchComposite.getOrderPartsList().stream().filter(o -> orderId.equals(o.getOrderId())).collect(Collectors.toList());
            }
        }


        waitOfferResp.setOrderBase(orderBase);
        waitOfferResp.setOrderExtraData(orderExtraData);
        waitOfferResp.setOrderLogisticsInfo(orderLogisticsInfo);
        waitOfferResp.setOrderReturnLogistics(orderReturnLogistics);
        waitOfferResp.setOrderInitFee(orderInitFee);
        waitOfferResp.setOrderAward(orderAward);
        waitOfferResp.setOrderEpcGenreData(orderEpcGenre);
        waitOfferResp.setEmergencyOrder(emergencyOrder);
        waitOfferResp.setOrderTagResp(orderTagResp);
        waitOfferResp.setOrderBountyInfo(orderBountyInfo);

        //返回订单支付状态
        if (ObjectUtil.isNotNull(orderGrab) && AppointType.DEFINITE_PRICE.value.equals(orderGrab.getAppointType()) && orderGrab.getOrderPayStatus() == 1) {
            waitOfferResp.setOrderPayStatus(orderGrab.getOrderPayStatus());
        }

        //接单易4.7,好评返现
        if (ObjectUtil.isNotNull(orderRateAward)) {
            waitOfferResp.setRateAwardFee(orderRateAward.getRateAwardFee());
        }

        if (Objects.nonNull(orderBase) && Objects.nonNull(orderGrab)) {
            waitOfferResp.setIsPrePayOrderBeforeAppoint(this.isPrePayOrderBeforeAppoint(orderBase, orderGrab));
        }
        if (Objects.nonNull(orderGrab)) {
            waitOfferResp.setSecondDivisionId(orderGrab.getSecondDivisionId());
        }

        waitOfferResp.setHasVideo(hasVideo);
        waitOfferResp.setImageCount(imageCount);
        waitOfferResp.setIkeaOrderGoodsComposites(ikeaOrderGoodsCompositeList);
        waitOfferResp.setOrderGoodsComposites(orderGoodsCompositeList);
        waitOfferResp.setOrderServiceAttributeInfos(orderServiceAttributeInfoList);
        waitOfferResp.setOrderAddItemServiceInfoList(orderAddItemServiceInfoList);
        waitOfferResp.setOrderAutoGrabList(orderAutoGrabList);
        waitOfferResp.setRemainRecommendOfferNum(remainRecommendOfferNum);
        waitOfferResp.setOrderPartsList(orderPartsList);
        waitOfferResp.setIsAgentOrder(orderTag != null ? 1 : 0);
        Optional.ofNullable(noticeOrderTag).ifPresent(it -> waitOfferResp.setPushNoticeLabel(OrderTagEnum.LESS_CONTEND.name));

        if (Objects.nonNull(orderBase)) {
            //V7.12首次拉取订单,幂等(订单距离表),附近单发送mq
            if (specialOrderGateway.checkSideOrderDistanceConfig(2, orderBase.getCategoryId(), cityId, orderPush.getPushDistance())
                    && ObjectUtil.isNull(super.selectByOrderId(orderId))) {
                waitOfferResp.setIsClearOrderDistance(1);
            }
        }

        return waitOfferResp;
    }


    @Override
    public SimplePageInfo<WaitOfferV2RespBo> waitOfferV2(WaitOfferToV2 waitOfferRqt, Long cityId, Integer pageNumber, Integer pageSize) {
        Long masterId = waitOfferRqt.getMasterId();
        List<Long> provinceNextIds = waitOfferRqt.getProvinceNextIds();

        SimplePageInfo<WaitOfferV2RespBo> respSimplePageInfo;
        List<OrderPush> orderPushList;

        List<String> displayLongTailLv1ServeIdsList = ApolloSwitchUtils.getDisplayLongTailLv1ServeIds();
        if (Objects.nonNull(waitOfferRqt.getPushFlag())
                && waitOfferRqt.getPushFlag() == 0
                && Objects.nonNull(waitOfferRqt.getMasterSourceType())
                && "tob".equals(waitOfferRqt.getMasterSourceType())
                && CollectionUtil.isNotEmpty(displayLongTailLv1ServeIdsList)) {
            log.info("待接单默认列表展示技能不相关订单>>>>>masterId:{},pushFlag:{},masterSourceType:{},displayLongTailLv1ServeIdsList:{}",
                    waitOfferRqt.getMasterId(), waitOfferRqt.getPushFlag(), waitOfferRqt.getMasterSourceType(), JSONUtil.toJsonStr(displayLongTailLv1ServeIdsList));
            //待接单默认列表展示技能不相关订单
            waitOfferRqt.setDisplayLongTailOrderLevel1ServeIds(displayLongTailLv1ServeIdsList.stream().map(Long::valueOf).collect(Collectors.toList()));
        }


        //获取干预配置
        SmartSortPositionConfig smartSortPositionConfig = smartSortPositionConfigService.selectByCityId(provinceNextIds.get(0).intValue());

        if ("smart".equals(waitOfferRqt.getSortQueryType())
                && ApolloSwitchUtils.isOpenExposureSortCitySwitch(String.valueOf(provinceNextIds.get(0)))
                && Objects.nonNull(smartSortPositionConfig)) {
            //智能排序且该城市开启干预位曝光
            orderPushList = this.smartList(waitOfferRqt, smartSortPositionConfig);
            if (CollectionUtil.isEmpty(orderPushList)) {
                return null;
            }
            //内存中排序返回
            List<OrderPush> poList = paginate(orderPushList, pageNumber, pageSize);

            List<WaitOfferV2RespBo> waitOfferRespList = this.getWaitOfficeResp(provinceNextIds, poList, cityId, masterId, pageSize);

            respSimplePageInfo = new SimplePageInfo<>(waitOfferRespList);
            respSimplePageInfo.setPageNum(pageNumber);
            respSimplePageInfo.setPages((int) Math.ceil((double) orderPushList.size() / pageSize));
            respSimplePageInfo.setPageSize(pageSize);
            respSimplePageInfo.setTotal(orderPushList.size());

            orderPushList = poList;

        } else {
            Page<?> page = PageHelper.startPage(pageNumber, pageSize);
            orderPushList = orderPushRepository.selectWaitOfferListV4(waitOfferRqt);
            List<OrderPush> orderPushListNew = new ArrayList<>();
            try {
                //需求 有过滤的订单id入参时  过滤掉
                //表示没有需要过率的数据
                boolean nhv = true;
                if (null != waitOfferRqt.getOrderId()) {
                    for (OrderPush orderPush : orderPushList) {
                        if (waitOfferRqt.getOrderId().equals(orderPush.getOrderId())) {
                            nhv = false;
                        } else {
                            orderPushListNew.add(orderPush);
                        }

                    }
                    if (nhv) {
                        orderPushListNew.remove(orderPushListNew.get(orderPushListNew.size() - 1));
                    }
                    orderPushList = orderPushListNew;
                }
            } catch (Exception e) {
                //不要影响其他
            }

            //获取待报价列表组装数据
            List<WaitOfferV2RespBo> waitOfferRespList = this.getWaitOfficeResp(provinceNextIds, orderPushList, cityId, masterId, pageSize);

            respSimplePageInfo = new SimplePageInfo<>(waitOfferRespList);
            respSimplePageInfo.setPageNum(page.getPageNum());
            respSimplePageInfo.setPages(page.getPages());
            respSimplePageInfo.setPageSize(page.getPageSize());
            respSimplePageInfo.setTotal(page.getTotal());
        }

        List<Long> orderIdList = orderPushList == null ? null : orderPushList.stream().map(OrderPush::getOrderId).collect(Collectors.toList());
        //异步更新师傅拉取待报价列表订单时间
        orderPushManagerGateway.masterPullOrderList(provinceNextIds, masterId, orderIdList);

        return respSimplePageInfo;
    }

    private List<OrderPush> smartList(WaitOfferToV2 waitOfferRqt,  SmartSortPositionConfig smartSortPositionConfig) {
        waitOfferRqt.setTableName("ltb_order_push");

        long startTime = System.currentTimeMillis();
        List<OrderPush> orderPushList = orderPushRepository.selectSmartList(waitOfferRqt);
        long endTime = System.currentTimeMillis();
        log.info("smartList >>> selectDb consume {}ms", endTime - startTime);
        if (CollectionUtil.isEmpty(orderPushList)) {
            return null;
        }
        //干预排序
        List<OrderPush> exposureOrderPushList = orderPushList.stream()
                .sorted(Comparator.comparing(OrderPush::getExposureScore).reversed())
                .collect(Collectors.toList());

        //自然+干预=组合排序
        orderPushList = mergeLists(orderPushList, exposureOrderPushList, smartSortPositionConfig);
        long endTime2 = System.currentTimeMillis();
        log.info("smartList mergeLists >>> mergeLists consume {}ms", endTime2 - endTime);

        return orderPushList;
    }

    private List<OrderPush> paginate(List<OrderPush> orderPushList, int pageNumber, int pageSize) {
        if (orderPushList == null || orderPushList.isEmpty()) {
            return new ArrayList<>();
        }

        int fromIndex = (pageNumber - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, orderPushList.size());

        if (fromIndex >= orderPushList.size()) {
            return new ArrayList<>();
        }

        return orderPushList.subList(fromIndex, toIndex);
    }

    /**
     *
     * @param listSize 列表大小
     * @param pageSize 每页大小
     * @param pageIndex 每页取第？位元素（索引从0开始）
     * @return
     */
    private List<Integer> getIndices(int listSize, int pageSize, int pageIndex) {
        List<Integer> indices = new ArrayList<>();

        // 计算总页数
        int totalPages = (int) Math.ceil((double) listSize / pageSize);

        // 生成索引列表
        for (int page = 0; page < totalPages; page++) {
            int index = page * pageSize + pageIndex;
            if (index < listSize) {
                indices.add(index);
            }
        }

        return indices;
    }

    private List<OrderPush> mergeLists(List<OrderPush> naturalPush, List<OrderPush> exposurePush, SmartSortPositionConfig smartSortPositionConfig) {
        // 结果列表，初始为自然排序
        List<OrderPush> result = new ArrayList<>(naturalPush);
        List<Long> resOrderIds = result.stream().map(OrderPush::getOrderId).collect(Collectors.toList());

        JSONObject jsonObject = JSON.parseObject(smartSortPositionConfig.getPositionConfigRule());
        Integer dynamicEveryFewSize = jsonObject.getInteger("dynamicEveryFewSize");
        Integer dynamicPositionIndex = jsonObject.getInteger("dynamicPositionIndex");
        List<Integer> interventionIndexes = getIndices(naturalPush.size(),dynamicEveryFewSize ,dynamicPositionIndex-1);

        // 当前干预位索引
        int interventionIndex = 0;

        // 遍历干预排序列表
        for (OrderPush value : exposurePush) {
            if (interventionIndex >= interventionIndexes.size()) {
                break; // 所有干预位已填充完毕
            }

            // 获取当前干预位索引
            int targetIndex = interventionIndexes.get(interventionIndex);

            // 检查值是否已存在于干预位之前
            if (!resOrderIds.subList(0, targetIndex).contains(value.getOrderId())) {
                // 插入值到干预位，后续元素自动向后移动
                value.setNote("exposureOrder，orderId:".concat(value.getOrderId().toString()));
                result.add(targetIndex, value);

                // 检查干预位置后面是否存在重复值
                for (int i = targetIndex + 1; i < result.size(); i++) {
                    if (result.get(i).getOrderId().equals(value.getOrderId())) {
                        result.remove(i);
                        break; // 只移除一次，避免影响后续索引
                    }
                }

                // 移动到下一个干预位
                interventionIndex++;
            } else {
                if ("on".equals(exposurePositionFlagSwitch)) {
                    OrderPush indexValue = result.get(targetIndex);
                    indexValue.setNote("exposureOrder,干预位置但非干预订单，自然排序已展示干预订单，orderId:".concat(value.getOrderId().toString()));
                    result.remove(targetIndex);
                    result.add(targetIndex, indexValue);
                }

            }
        }

        return result;
    }

    @Override
    public SimplePageInfo<OrderPush> tmplCityOrderPushList(TmplCityOrderPushRqt rqt) {
        Long masterId = rqt.getMasterId();
        PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        List<OrderPush> orderPushList = orderPushRepository.listTmplCityOrderPush(rqt);
        if (CollectionUtil.isEmpty(orderPushList)) {
            //查出数据为空，清空该师傅在缓存中的家庭样板城市推单标识
            redisHelper.del(RedisKeyConstant.MASTER_TMPL_CITY_FLAG_KEY.concat(masterId.toString()));
        } else {
            //异步更新样板城市推单弹窗读取时间
            CompletableFuture.runAsync(() -> orderPushManagerGateway.updateTmplCityTime(orderPushList), executor);
        }

        return new SimplePageInfo<>(orderPushList);
    }

    @Override
    public List<WaitOfferNoPageRespBo> waitOfferNoPage(WaitOfferNoPageBo waitOfferNoPageBo) {
        List<WaitOfferNoPageRespBo> respList = new ArrayList<>();
        Long masterId = waitOfferNoPageBo.getMasterId();
        //按照待报价订单列表搜索订单id
        List<OrderPush> orderPushList = orderPushRepository.selectWaitOfferNoPageList(waitOfferNoPageBo);
        if (CollectionUtils.isEmpty(orderPushList)) {
            return respList;
        }
        List<Long> orderIds = orderPushList.stream().map(OrderPush::getOrderId).collect(Collectors.toList());

        List<BatchGetOrderInfoExtraResp> batchGetOrderInfoExtraResp = commonOrderOfferService.getBatchGetOrderInfoExtraResp(orderIds, masterId);
        if(CollectionUtils.isEmpty(batchGetOrderInfoExtraResp)){
            return respList;
        }
        for (BatchGetOrderInfoExtraResp getOrderInfoExtraResp : batchGetOrderInfoExtraResp) {
            WaitOfferNoPageRespBo resp = new WaitOfferNoPageRespBo();
            resp.setOrderExtraData(getOrderInfoExtraResp.getOrderExtraData());
            resp.setOrderBase(getOrderInfoExtraResp.getOrderBase());
            resp.setOrderFirstImageAid(getOrderInfoExtraResp.getOrderFirstImageAid());
            respList.add(resp);
        }
        return respList;
    }


    @Override
    public List<OrderPush> selectMasterOrderPushList(WaitOfferNoPageBo waitOfferNoPageBo) {
        Long masterId = waitOfferNoPageBo.getMasterId();
        //按照待报价订单列表搜索订单id
        List<OrderPush> orderPushList = orderPushRepository.selectWaitOfferNoPageList(waitOfferNoPageBo);
        return orderPushList;
    }

    @Override
    public List<OrderPush> selectOrderPushMaxLimit300(List<Long> provinceNextIds, Long masterId, Integer limit) {
        if (Objects.isNull(limit) || limit < 0 || limit > 300) {
            throw new BusException("查询师傅最大推单数量，必须大于0 且小于等于300个");
        }
        return orderPushRepository.selectIocWaitOfferFiler(provinceNextIds, masterId, limit);
    }


    @Override
    public List<OrderPush> getOrderPushForNotice(List<Long> provinceNextIds, Long orderId) {
        List<String> displayLongTailLv1ServeIdsList = ApolloSwitchUtils.getDisplayLongTailLv1ServeIds();
        if (CollectionUtil.isNotEmpty(displayLongTailLv1ServeIdsList)) {
            return orderPushRepository.selectOrderPushForNotice(provinceNextIds, orderId, displayLongTailLv1ServeIdsList.stream().map(Long::valueOf).collect(Collectors.toList()));
        } else {
            return orderPushRepository.selectOrderPushForNotice(provinceNextIds, orderId, new ArrayList<>());
        }
    }

    @Override
    public List<OrderPush> selectOrderPushByOrderId(List<Long> provinceNextId, Long orderId) {
        return orderPushRepository.selectOrderPushByOrderId(provinceNextId, orderId);
    }

    @Override
    public List<OrderPush> selectMasterOrderPushByAccount(List<Long> provinceNextId, Long masterId,Long accountId,String accountType) {
        return orderPushRepository.selectMasterOrderPushByAccount(provinceNextId, masterId,accountId,accountType);
    }

    @Override
    public OrderPush getOrderPushByOrderIdAndOffer(List<Long> provinceNextId, Long orderId, Integer offer) {
        return orderPushRepository.getOrderPushByOrderIdAndOffer(provinceNextId, orderId, offer);
    }

    @Override
    public List<OrderPush> listByMasterDivisionIdForAppletUnLogin(MasterAppletListOrderPushGatewayRqt rqt) {
        return orderPushRepository.listByMasterDivisionIdForAppletUnLogin(rqt);
    }

    /**
     * 判断是否为user订单
     *
     * @param orderBase 订单基础信息
     * @return boolean
     */
    private boolean isUserOrder(OrderBase orderBase) {

        return "user".equals(orderBase.getAccountType());
    }

    /**ps： 指派前判断，指派后这种判断方法是有问题的
     * 是否是企业一口价前置支付订单
     * @param orderBase
     * @return
     */
    private Boolean isPrePayOrderBeforeAppoint(OrderBase orderBase, OrderGrab orderGrab){
        // 总包的忽略，家庭的忽略

        boolean isUserOrder = isUserOrder(orderBase) && BusinessLineIdEnum.ONE.id == orderBase.getBusinessLineId();

        Integer orderPayStatus = Optional.ofNullable(orderGrab).map(OrderGrab::getOrderPayStatus).orElse(0);

        boolean isDefinitePriceOrder = Objects.nonNull(orderGrab) && orderGrab.getAppointType().equals(AppointType.DEFINITE_PRICE.value);
        //是否用户一口价前置支付订单
        return isUserOrder && isDefinitePriceOrder && orderPayStatus == 1;
    }


    @Override
    public List<OrderPush> selectOrderPushList(List<Long> provinceNextId, Long orderId,Integer limitCount) {
        return orderPushRepository.selectOrderPushList(provinceNextId,orderId,limitCount);
    }

    @Override
    public List<OrderPush> selectOrderPushListByPushScore(List<Long> provinceNextId, Long orderId,Integer pageNum,Integer pageSize) {
        Page<?> page = PageHelper.startPage(pageNum, pageSize,false);
        return orderPushRepository.selectOrderPushListByPushScore(provinceNextId,orderId);
    }

    @Override
    public Integer selectFirstPushMasterCount(Long globalOrderId) {
        if (Objects.isNull(globalOrderId)) {
            return null;
        }
        FirstPushMatchMaster firstPushMatchMaster = firstPushMatchMasterRepository.selectFirstPushMasterByGlobalOrderId(globalOrderId);
        if (Objects.isNull(firstPushMatchMaster)) {
            return null;
        }

        return firstPushMatchMaster.getMatchMasterNum();
    }

    /**
     * 1. 查询该订单推单师傅，按推单评分取前100
     * 2. 查询收藏师傅、合作过、服务好、省钱师傅标签师傅，若师傅有多个标签，只展示一个，标签优先级为：
     *          收藏师傅 > 合作过 > 服务好 > 省钱师傅 > 普通(其他无标签师傅)
     * 3. 不同标签师傅列表自身先按指标排序，再进行排序组合分页，每页展示6个师傅，分类排序：收藏师傅 > 合作过 > 服务好 > 省钱师傅，数量比例为1:1:2:2
     * 4. 如果某类师傅没有，则后面分类的师傅往前补充，往前补充的优先级也是收藏师傅 > 合作过 > 服务好 > 省钱师傅，且比例为1:1:2:2
     *
     * @param provinceNextId
     * @param orderId
     * @param thirdDivisionId
     * @param serveIds
     * @param userId
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public SimplePageInfo<SiteOrderDetailMasterListBo> getSiteOrderDetailMasterSortCombinationsList(List<Long> provinceNextId,
                                                                                                    Long orderId,
                                                                                                    Long thirdDivisionId,
                                                                                                    String serveIds,
                                                                                                    Long userId,
                                                                                                    Integer pageNum,
                                                                                                    Integer pageSize) {

        log.info("getSiteOrderDetailMasterSortCombinationsList,orderId:{},thirdDivisionId:{},userId:{},serveIds:{}", orderId, thirdDivisionId, userId, serveIds);
        List<OrderPush> pushMasterList = null;

        Long serveLv2Id = null;
        Long serveLv3Id = null;

        long startTime = System.currentTimeMillis();
        CompletableFuture<List<OrderPush>> pushMasterListFuture = CompletableFuture.supplyAsync(
                () -> orderPushRepository.selectSiteDetailNearbyMasterListAndLimit(provinceNextId, orderId, 100), concurrentProcessExecutor
        );

        CompletableFuture<List<ServeBaseInfoResp>> serveBaseInfoRespListFuture = CompletableFuture.supplyAsync(
                () -> getServeBaseInfoRespList(serveIds), concurrentProcessExecutor
        );

        //等待所有任务完成（设置5秒超时控制）
        try {
            CompletableFuture.allOf(pushMasterListFuture, serveBaseInfoRespListFuture)
                    .get(5, TimeUnit.SECONDS);
            pushMasterList = pushMasterListFuture.get();
            List<ServeBaseInfoResp> serveBaseInfoRespList = serveBaseInfoRespListFuture.get();
            if (CollectionUtil.isNotEmpty(serveBaseInfoRespList)) {
                ServeBaseInfoResp serveBaseInfoResp = serveBaseInfoRespList.get(0);
                serveLv2Id = serveBaseInfoResp.getLevel2Id();
                serveLv3Id = serveBaseInfoResp.getLevel3Id();
            }
            log.info("getSiteOrderDetailMasterSortCombinationsList serveLv2Id:{},serveLv3Id:{}", serveLv2Id, serveLv3Id);
        } catch (Exception e) {
            log.error("getSiteOrderDetailMasterSortCombinationsList  并发查询推单信息和服务数据异常", e);
            return null;
        }
        long endTime = System.currentTimeMillis();
        log.info("getSiteOrderDetailMasterSortCombinationsList  并发查询推单信息耗时:{}", endTime - startTime);
        if (CollectionUtils.isEmpty(pushMasterList)) {
            return null;
        }

        List<Long> masterIds = pushMasterList.stream().map(OrderPush::getMasterId).collect(Collectors.toList());
        List<SiteOrderDetailMasterListBo> masterBoList = pushMasterList.stream().map(pushMaster -> BeanUtil.toBean(pushMaster, SiteOrderDetailMasterListBo.class)).collect(Collectors.toList());

        //并发查询HBase填充师傅指标信息
        long startTime1 = System.currentTimeMillis();
        CompletableFuture<Void> future1 = CompletableFuture.runAsync(
                //填充是否收藏师傅、合作过师傅指标
                () -> fillCollectAndCooperationMasterQuota(userId, masterBoList), concurrentProcessExecutor
        );

        Long finalServeLv2Id = serveLv2Id;
        CompletableFuture<Void> future2 = CompletableFuture.runAsync(
                //填充是否服务好指标
                () -> fillGoodServiceMasterQuota(finalServeLv2Id, masterBoList), concurrentProcessExecutor
        );

        Long finalServeLv3Id = serveLv3Id;
        CompletableFuture<Void> future3 = CompletableFuture.runAsync(
                //填充三级服务完工单量指标
                () -> fillServeLv3FinishOrderCntMasterQuota(finalServeLv3Id, masterBoList), concurrentProcessExecutor
        );

        CompletableFuture<Void> future4 = CompletableFuture.runAsync(
                //填充是否省钱师傅以及常报最低价、常抢一口价等指标
                () -> fillSaveMoneyMasterQuota(masterIds, masterBoList), concurrentProcessExecutor
        );

        Long finalServeLv3Id2 = serveLv3Id;
        CompletableFuture<Void> future5 = CompletableFuture.runAsync(
                //填充订单三级地址三级服务完工单量排名指标
                () -> fillThirdDivisionIdServeLv3FinishOrderCntMasterQuota(thirdDivisionId, finalServeLv3Id2, masterBoList), concurrentProcessExecutor
        );

        //等待所有任务完成（设置5秒超时控制）
        try {
            CompletableFuture.allOf(future1, future2, future3, future4, future5)
                    .get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("getSiteOrderDetailMasterSortCombinationsList 并发查询hbase指标数据异常", e);
            return null;
        }

        long endTime1 = System.currentTimeMillis();
        log.info("getSiteOrderDetailMasterSortCombinationsList  并发查询hbase指标数据耗时:{}", endTime1 - startTime1);
        //按标签优先级优先级组合

        //收藏师傅
        List<SiteOrderDetailMasterListBo> collectMasterList = Lists.newArrayList();
        //合作过师傅
        List<SiteOrderDetailMasterListBo> cooperationMasterList = Lists.newArrayList();
        //服务好师傅
        List<SiteOrderDetailMasterListBo> goodServiceMasterList = Lists.newArrayList();
        //省钱师傅
        List<SiteOrderDetailMasterListBo> saveMoneyMasterList = Lists.newArrayList();
        //普通师傅
        List<SiteOrderDetailMasterListBo> normalMasterList = Lists.newArrayList();

        for (SiteOrderDetailMasterListBo siteOrderDetailMasterListBo : masterBoList) {
            if (Objects.nonNull(siteOrderDetailMasterListBo.getUserIdIsCollectMaster())
                    && siteOrderDetailMasterListBo.getUserIdIsCollectMaster() == 1) {
                //收藏师傅
                collectMasterList.add(siteOrderDetailMasterListBo);
                continue;
            }
            if (Objects.nonNull(siteOrderDetailMasterListBo.getUserIdIsCooperation())
                    && siteOrderDetailMasterListBo.getUserIdIsCooperation() == 1) {
                //合作过师傅
                cooperationMasterList.add(siteOrderDetailMasterListBo);
                continue;
            }
            if (Objects.nonNull(siteOrderDetailMasterListBo.getServeLv2IsGoodService())
                    && siteOrderDetailMasterListBo.getServeLv2IsGoodService() == 1) {
                //服务好师傅
                goodServiceMasterList.add(siteOrderDetailMasterListBo);
                continue;
            }
            if (Objects.nonNull(siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsSaveMoneyMaster())
                    && siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsSaveMoneyMaster() == 1) {
                //省钱师傅
                saveMoneyMasterList.add(siteOrderDetailMasterListBo);
            } else {
                //普通师傅
                normalMasterList.add(siteOrderDetailMasterListBo);
            }
        }

        //各标签师傅列表排序
        long startTime2 = System.currentTimeMillis();

        CompletableFuture<Void> collectMasterListSortFuture = CompletableFuture.runAsync(
                //收藏师傅排序
                () -> sortCollectAndCooperationAndNormalMasterList(collectMasterList), concurrentProcessExecutor
        );

        CompletableFuture<Void> cooperationMasterListSortFuture = CompletableFuture.runAsync(
                //合作过师傅排序
                () -> sortCollectAndCooperationAndNormalMasterList(cooperationMasterList), concurrentProcessExecutor
        );

        CompletableFuture<Void> normalMasterListSortFuture = CompletableFuture.runAsync(
                //普通师傅排序
                () -> sortCollectAndCooperationAndNormalMasterList(normalMasterList), concurrentProcessExecutor
        );

        CompletableFuture<Void> goodServiceMasterListSortFuture = CompletableFuture.runAsync(
                //服务好师傅排序
                () -> sortGoodServiceMasterList(goodServiceMasterList), concurrentProcessExecutor
        );

        CompletableFuture<Void> saveMoneyMasterListSortFuture = CompletableFuture.runAsync(
                //省钱师傅排序
                () -> sortSaveMoneyMasterList(saveMoneyMasterList), concurrentProcessExecutor
        );
        //等待所有任务完成（设置5秒超时控制）
        try {
            CompletableFuture.allOf(collectMasterListSortFuture, cooperationMasterListSortFuture, normalMasterListSortFuture, goodServiceMasterListSortFuture, saveMoneyMasterListSortFuture)
                    .get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("getSiteOrderDetailMasterSortCombinationsList 并发对各标签师傅排序异常", e);
            return null;
        }
        long endTime2 = System.currentTimeMillis();
        log.info("getSiteOrderDetailMasterSortCombinationsList 各标签师傅排序耗时:{}", endTime2 - startTime2);

        List<SiteOrderDetailMasterListBo> newMasterBoList = combineMasters(collectMasterList,
                cooperationMasterList,
                goodServiceMasterList,
                saveMoneyMasterList,
                normalMasterList,
                pageSize);

        long endTime3 = System.currentTimeMillis();
        log.info("getSiteOrderDetailMasterSortCombinationsList 各标签师傅内存组合耗时:{}", endTime3 - endTime2);


        //内存中分页
        List<SiteOrderDetailMasterListBo> resMasterBoList = paginateSiteOrderDetailMasterListBo(newMasterBoList, pageNum, pageSize);
        long endTime4 = System.currentTimeMillis();
        log.info("getSiteOrderDetailMasterSortCombinationsList 各标签师傅内存中分页耗时:{}", endTime4 - endTime3);

        SimplePageInfo<SiteOrderDetailMasterListBo> simplePageInfo = new SimplePageInfo<>();
        simplePageInfo.setTotal(newMasterBoList.size());
        simplePageInfo.setList(resMasterBoList);
        simplePageInfo.setPageNum(pageNum);
        simplePageInfo.setPageSize(pageSize);
        simplePageInfo.setPages((int) Math.ceil((double) newMasterBoList.size() / pageSize));
        return simplePageInfo;
    }

    /**
     * @param collectMasterList
     * @param cooperationMasterList
     * @param goodServiceMasterList
     * @param saveMoneyMasterList
     * @param normalMasterList
     * @return
     */
    public List<SiteOrderDetailMasterListBo> combineMasters(
            List<SiteOrderDetailMasterListBo> collectMasterList,
            List<SiteOrderDetailMasterListBo> cooperationMasterList,
            List<SiteOrderDetailMasterListBo> goodServiceMasterList,
            List<SiteOrderDetailMasterListBo> saveMoneyMasterList,
            List<SiteOrderDetailMasterListBo> normalMasterList
            , int pageSize) {

        // 使用队列以便高效移除元素
        Queue<SiteOrderDetailMasterListBo> collectQueue = new LinkedList<>(collectMasterList);
        Queue<SiteOrderDetailMasterListBo> coopQueue = new LinkedList<>(cooperationMasterList);
        Queue<SiteOrderDetailMasterListBo> serviceQueue = new LinkedList<>(goodServiceMasterList);
        Queue<SiteOrderDetailMasterListBo> moneyQueue = new LinkedList<>(saveMoneyMasterList);
        Queue<SiteOrderDetailMasterListBo> normalQueue = new LinkedList<>(normalMasterList);

        List<SiteOrderDetailMasterListBo> combinedList = new ArrayList<>();

        int totalMasters = collectMasterList.size() + cooperationMasterList.size()
                + goodServiceMasterList.size() + saveMoneyMasterList.size()
                + normalMasterList.size();

        // 持续组合直到所有数据用完
        while (totalMasters > 0) {
            List<SiteOrderDetailMasterListBo> page = new ArrayList<>(pageSize);

            // 步骤1: 按比例分配非普通标签
            // 收藏师傅 (1)
            addFromQueue(collectQueue, page, 1, pageSize);
            // 合作师傅 (1)
            addFromQueue(coopQueue, page, 1, pageSize);
            // 服务好师傅 (2)
            addFromQueue(serviceQueue, page, 2, pageSize);
            // 省钱师傅 (2)
            addFromQueue(moneyQueue, page, 2, pageSize);

            // 步骤2: 优先级补充 (非普通标签)
            while (page.size() < pageSize &&
                    (!collectQueue.isEmpty() || !coopQueue.isEmpty() ||
                            !serviceQueue.isEmpty() || !moneyQueue.isEmpty())) {

                if (!collectQueue.isEmpty()) {
                    page.add(collectQueue.poll());
                } else if (!coopQueue.isEmpty()) {
                    page.add(coopQueue.poll());
                } else if (!serviceQueue.isEmpty()) {
                    page.add(serviceQueue.poll());
                } else if (!moneyQueue.isEmpty()) {
                    page.add(moneyQueue.poll());
                }
            }

            // 步骤3: 用普通标签补足
            while (page.size() < pageSize && !normalQueue.isEmpty()) {
                page.add(normalQueue.poll());
            }

            combinedList.addAll(page);
            totalMasters -= page.size();
        }

        return combinedList;
    }

    // 辅助方法：从队列中添加指定数量的元素
    private void addFromQueue(Queue<SiteOrderDetailMasterListBo> queue,
                                     List<SiteOrderDetailMasterListBo> page,
                                     int count, int pageSize) {
        for (int i = 0; i < count && page.size() < pageSize && !queue.isEmpty(); i++) {
            page.add(queue.poll());
        }
    }

    /**
     * 获取服务信息
     * @param serveIds
     * @return
     */
    private List<ServeBaseInfoResp> getServeBaseInfoRespList(String serveIds) {

        if (!Strings.isNullOrEmpty(serveIds)) {
            String serveId = serveIds.split(",")[0];
            log.info("getSiteOrderDetailMasterSortCombinationsList serveId:{}", serveId);
            HashSet<Long> serveIdSet = new HashSet<>();
            serveIdSet.add(Long.valueOf(serveId));

            ServeIdSetReq serveIdSetReq = new ServeIdSetReq();
            serveIdSetReq.setServeIdSet(serveIdSet);
            return callGateway.catchLog(() -> serveServiceApi.getServeBaseInfo(serveIdSetReq));
        }
        return null;
    }

    /**
     * 收藏、合作过、普通师傅排序
     * @param masterBoList
     */
    private void sortCollectAndCooperationAndNormalMasterList(List<SiteOrderDetailMasterListBo> masterBoList) {
        if (CollectionUtil.isNotEmpty(masterBoList)) {
            //收藏师傅排序
            masterBoList.sort(
                    // 完工量不为null的按完工量倒序，相同再按评分倒序，完工量为null的直接放在最后面，再按评分倒序
                    Comparator.comparing(SiteOrderDetailMasterListBo::getServeLv3FinishOrderCnt,
                                    Comparator.nullsLast(Comparator.reverseOrder())
                            )
                            .thenComparing(
                                    SiteOrderDetailMasterListBo::getPushScore,
                                    Comparator.reverseOrder()
                            )
            );
        }
    }

    /**
     * 服务好师傅排序
     * @param masterBoList
     */
    private void sortGoodServiceMasterList(List<SiteOrderDetailMasterListBo> masterBoList) {
        if (CollectionUtil.isNotEmpty(masterBoList)) {
            //服务好师傅排序
            masterBoList.sort(
                    // 师傅三级地址三级服务完工单量排名不为null的按师傅三级地址三级服务完工单量排名正序，相同再按评分倒序，
                    // 师傅三级地址三级服务完工单量排名为null的直接放在最后面，再按评分倒序
                    Comparator.comparing(SiteOrderDetailMasterListBo::getMasterThirdDivisionIdServeLv3IdRankByFinishOrderCnt,
                                    Comparator.nullsLast(Comparator.naturalOrder())
                            )
                            .thenComparing(
                                    SiteOrderDetailMasterListBo::getPushScore,
                                    Comparator.reverseOrder()
                            )
            );
        }
    }

    /**
     * 省钱师傅排序
     * @param masterBoList
     */
    private void sortSaveMoneyMasterList(List<SiteOrderDetailMasterListBo> masterBoList) {
        if (CollectionUtil.isNotEmpty(masterBoList)) {
            //省钱师傅排序
            masterBoList.sort(
                    // 师傅所在二级地址该师傅最近90天报价最低单量排名不为null的按师傅所在二级地址该师傅最近90天报价最低单量排名正序，相同再按评分倒序，
                    // 师傅所在二级地址该师傅最近90天报价最低单量排名为null的直接放在最后面，再按评分倒序
                    Comparator.comparing(SiteOrderDetailMasterListBo::getMasterSecondDivisionIdRankByLatest90DayLowOfferCnt,
                                    Comparator.nullsLast(Comparator.naturalOrder())
                            )
                            .thenComparing(
                                    SiteOrderDetailMasterListBo::getPushScore,
                                    Comparator.reverseOrder()
                            )
            );
        }

    }


    /**
     * 填充收藏师傅、合作过师傅指标
     * @param userId
     * @param masterBoList
     */
    private void fillCollectAndCooperationMasterQuota(Long userId, List<SiteOrderDetailMasterListBo> masterBoList) {

        List<String> rowKeyList = masterBoList.stream().map(siteOrderDetailMasterListBo -> siteOrderDetailMasterListBo.getMasterId() + "_" + userId.toString()).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("user_id");
        fieldColumnList.add("is_cooperation");
        fieldColumnList.add("is_collect_master");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList, fieldColumnList, "mst_and_usr_stat");
        Map<String, Integer> isCooperationMap = new HashMap<>();
        Map<String, Integer> isCollectMasterMap = new HashMap<>();

        if (Objects.nonNull(resultArray) && resultArray.size() > 0) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String) jsonObject.get("master_id");

                if (jsonObject.containsKey("is_cooperation")) {
                    String isCooperation = (String) jsonObject.get("is_cooperation");
                    if (!Strings.isNullOrEmpty(isCooperation)) {
                        isCooperationMap.put(masterId, Integer.valueOf(isCooperation));
                    }
                }
                if (jsonObject.containsKey("is_collect_master")) {
                    String isCollectMaster = (String) jsonObject.get("is_collect_master");
                    if (!Strings.isNullOrEmpty(isCollectMaster)) {
                        isCollectMasterMap.put(masterId, Integer.valueOf(isCollectMaster));
                    }
                }
            }
            for (SiteOrderDetailMasterListBo masterBo : masterBoList) {

                if (isCollectMasterMap.containsKey(masterBo.getMasterId().toString())) {
                    //填充是否收藏师傅
                    masterBo.setUserIdIsCollectMaster(isCollectMasterMap.get(masterBo.getMasterId().toString()));
                }
                if (isCooperationMap.containsKey(masterBo.getMasterId().toString())) {
                    //填充合作过师傅
                    masterBo.setUserIdIsCooperation(isCooperationMap.get(masterBo.getMasterId().toString()));
                }
            }
        }
    }

    /**
     * 填充服务好指标
     * @param serveLv2Id
     * @param masterBoList
     */
    private void fillGoodServiceMasterQuota(Long serveLv2Id, List<SiteOrderDetailMasterListBo> masterBoList) {
        if (Objects.isNull(serveLv2Id) || serveLv2Id == 0L) {

            return;
        }

        List<String> rowKeyList = masterBoList.stream().map(siteOrderDetailMasterListBo -> siteOrderDetailMasterListBo.getMasterId() + "_" + serveLv2Id.toString()).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("is_good_service");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList, fieldColumnList, "mst_serve_lv2_stat");
        Map<String, Integer> isGoodServiceMap = new HashMap<>();

        if (Objects.nonNull(resultArray) && resultArray.size() > 0) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String) jsonObject.get("master_id");

                if (jsonObject.containsKey("is_good_service")) {
                    String isGoodService = (String) jsonObject.get("is_good_service");
                    if (!Strings.isNullOrEmpty(isGoodService)) {
                        isGoodServiceMap.put(masterId, Integer.valueOf(isGoodService));
                    }
                }
            }
            for (SiteOrderDetailMasterListBo masterBo : masterBoList) {

                if (isGoodServiceMap.containsKey(masterBo.getMasterId().toString())) {
                    //填充是否服务好
                    masterBo.setServeLv2IsGoodService(isGoodServiceMap.get(masterBo.getMasterId().toString()));
                }
            }
        }

    }

    /**
     * 填充三级服务完工单量指标
     * @param serveLv3Id
     * @param masterBoList
     */
    private void fillServeLv3FinishOrderCntMasterQuota(Long serveLv3Id, List<SiteOrderDetailMasterListBo> masterBoList) {
        if (Objects.isNull(serveLv3Id) || serveLv3Id == 0L) {
            return;
        }

        List<String> rowKeyList = masterBoList.stream().map(siteOrderDetailMasterListBo -> siteOrderDetailMasterListBo.getMasterId() + "_" + serveLv3Id.toString()).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("finish_order_cnt");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList, fieldColumnList, "mst_serve_lv3_stat");
        Map<String, Integer> finishOrderCntMap = new HashMap<>();

        if (Objects.nonNull(resultArray) && resultArray.size() > 0) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String) jsonObject.get("master_id");

                if (jsonObject.containsKey("finish_order_cnt")) {
                    String finishOrderCnt = (String) jsonObject.get("finish_order_cnt");
                    if (!Strings.isNullOrEmpty(finishOrderCnt)) {
                        finishOrderCntMap.put(masterId, Integer.valueOf(finishOrderCnt));
                    }
                }
            }
            for (SiteOrderDetailMasterListBo masterBo : masterBoList) {

                if (finishOrderCntMap.containsKey(masterBo.getMasterId().toString())) {
                    //填充三级服务完工单量指标
                    masterBo.setServeLv3FinishOrderCnt(finishOrderCntMap.get(masterBo.getMasterId().toString()));
                }
            }
        }

    }

    /**
     * 填充省钱师傅指标
     *
     * @param masterIds
     * @param masterBoList
     */
    private void fillSaveMoneyMasterQuota(List<Long> masterIds, List<SiteOrderDetailMasterListBo> masterBoList) {
        if (CollectionUtil.isEmpty(masterIds)) {
            return;
        }

        //填充师傅所在地城市id

        Map<Long, GetMasterInfoListByIdsResp> masterInfoMap = null;

        GetMasterInfoListByIdsRqt getMasterInfoListByIdsRqt = new GetMasterInfoListByIdsRqt();
        getMasterInfoListByIdsRqt.setMasterIds(masterIds);
        getMasterInfoListByIdsRqt.setPageSize(masterIds.size());

        List<GetMasterInfoListByIdsResp> getMasterInfoListByIdsRespList = callGateway.catchLog(() -> commonQueryServiceApi.getMasterInfoListByIds(getMasterInfoListByIdsRqt));

        if (CollectionUtil.isNotEmpty(getMasterInfoListByIdsRespList)) {
            masterInfoMap = getMasterInfoListByIdsRespList.stream().collect(Collectors.toMap(GetMasterInfoListByIdsResp::getMasterId, Function.identity()));
        }
        if (Objects.isNull(masterInfoMap)) {
            log.error("getSiteOrderDetailMasterSortCombinationsList fillSaveMoneyMasterQuota 获取师傅信息失败！");
            return;
        }
        for (SiteOrderDetailMasterListBo masterBo : masterBoList) {
            if (masterInfoMap.containsKey(masterBo.getMasterId())
                    && Objects.nonNull(masterInfoMap.get(masterBo.getMasterId()).getCityDivisionId())
                    && masterInfoMap.get(masterBo.getMasterId()).getCityDivisionId() != 0L) {
                masterBo.setMasterSecondDivisionId(masterInfoMap.get(masterBo.getMasterId()).getCityDivisionId());
            }
        }

        //填充省钱师傅指标
        List<SiteOrderDetailMasterListBo> tempMasterBoList = masterBoList.stream().filter(siteOrderDetailMasterListBo -> Objects.nonNull(siteOrderDetailMasterListBo.getMasterSecondDivisionId())
                && siteOrderDetailMasterListBo.getMasterSecondDivisionId() != 0L).collect(Collectors.toList());

        List<String> rowKeyList = tempMasterBoList.stream().map(siteOrderDetailMasterListBo -> siteOrderDetailMasterListBo.getMasterId() + "_" + siteOrderDetailMasterListBo.getMasterSecondDivisionId().toString()).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("is_save_money_master");
        fieldColumnList.add("is_usual_offer_low_price");
        fieldColumnList.add("is_usual_grab_order");
        fieldColumnList.add("latest_90_day_low_offer_cnt");
        fieldColumnList.add("rank_by_latest_90_day_low_offer_cnt");

        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList, fieldColumnList, "mst_second_division_id_stat");
        Map<String, Integer> isSaveMoneyMasterMap = new HashMap<>();

        Map<String, Integer> isUsualOfferLowPriceMap = new HashMap<>();

        Map<String, Integer> isUsualGrabOrderMap = new HashMap<>();

        Map<String, Integer> latest90DayLowOfferCntMap = new HashMap<>();

        Map<String, Integer> rankByLatest90DayLowOfferCntMap = new HashMap<>();

        if (Objects.nonNull(resultArray) && resultArray.size() > 0) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String) jsonObject.get("master_id");

                if (jsonObject.containsKey("is_save_money_master")) {
                    String isSaveMoneyMaster = (String) jsonObject.get("is_save_money_master");
                    if (!Strings.isNullOrEmpty(isSaveMoneyMaster)) {
                        isSaveMoneyMasterMap.put(masterId, Integer.valueOf(isSaveMoneyMaster));
                    }
                }

                if (jsonObject.containsKey("is_usual_offer_low_price")) {
                    String isUsualOfferLowPrice = (String) jsonObject.get("is_usual_offer_low_price");
                    if (!Strings.isNullOrEmpty(isUsualOfferLowPrice)) {
                        isUsualOfferLowPriceMap.put(masterId, Integer.valueOf(isUsualOfferLowPrice));
                    }
                }

                if (jsonObject.containsKey("is_usual_grab_order")) {
                    String isUsualGrabOrder = (String) jsonObject.get("is_usual_grab_order");
                    if (!Strings.isNullOrEmpty(isUsualGrabOrder)) {
                        isUsualGrabOrderMap.put(masterId, Integer.valueOf(isUsualGrabOrder));
                    }
                }

                if (jsonObject.containsKey("latest_90_day_low_offer_cnt")) {
                    String latest90DayLowOfferCnt = (String) jsonObject.get("latest_90_day_low_offer_cnt");
                    if (!Strings.isNullOrEmpty(latest90DayLowOfferCnt)) {
                        latest90DayLowOfferCntMap.put(masterId, Integer.valueOf(latest90DayLowOfferCnt));
                    }
                }

                if (jsonObject.containsKey("rank_by_latest_90_day_low_offer_cnt")) {
                    String rankByLatest90DayLowOfferCnt = (String) jsonObject.get("rank_by_latest_90_day_low_offer_cnt");
                    if (!Strings.isNullOrEmpty(rankByLatest90DayLowOfferCnt)) {
                        rankByLatest90DayLowOfferCntMap.put(masterId, Integer.valueOf(rankByLatest90DayLowOfferCnt));
                    }
                }
            }
        }

        for (SiteOrderDetailMasterListBo masterBo : masterBoList) {

            if (isSaveMoneyMasterMap.containsKey(masterBo.getMasterId().toString())) {
                //填充师傅所在二级地址该师傅是否是省钱师傅指标
                masterBo.setMasterSecondDivisionIdIsSaveMoneyMaster(isSaveMoneyMasterMap.get(masterBo.getMasterId().toString()));
            } else {
                //大数据没有标记成省钱师傅，根据距离再次判断是否10公里内，是则标记成省钱师傅，且距离近
                if (masterBo.getPushDistance() <= 10000L) {
                    masterBo.setMasterSecondDivisionIdIsSaveMoneyMaster(1);
                }
            }

            if (isUsualOfferLowPriceMap.containsKey(masterBo.getMasterId().toString())) {
                //填充师傅所在二级地址该师傅是否常报最低价指标
                masterBo.setMasterSecondDivisionIdIsUsualOfferLowPrice(isUsualOfferLowPriceMap.get(masterBo.getMasterId().toString()));
            }

            if (isUsualGrabOrderMap.containsKey(masterBo.getMasterId().toString())) {
                //填充师傅所在二级地址该师傅是否常抢一口价单指标
                masterBo.setMasterSecondDivisionIdIsUsualGrabOrder(isUsualGrabOrderMap.get(masterBo.getMasterId().toString()));
            }

            if (latest90DayLowOfferCntMap.containsKey(masterBo.getMasterId().toString())) {
                //填充师傅所在二级地址该师傅最近90天报价最低单量指标
                masterBo.setMasterSecondDivisionIdLatest90DayLowOfferCnt(latest90DayLowOfferCntMap.get(masterBo.getMasterId().toString()));
            }

            if (rankByLatest90DayLowOfferCntMap.containsKey(masterBo.getMasterId().toString())) {
                //填充师傅所在二级地址该师傅最近90天报价最低单量排名指标
                masterBo.setMasterSecondDivisionIdRankByLatest90DayLowOfferCnt(rankByLatest90DayLowOfferCntMap.get(masterBo.getMasterId().toString()));
            }
        }

    }


    /**
     * 填充订单三级地址三级服务完工单量排名指标
     *
     * @param thirdDivisionId
     * @param masterBoList
     */
    private void fillThirdDivisionIdServeLv3FinishOrderCntMasterQuota(Long thirdDivisionId, Long serveLv3Id, List<SiteOrderDetailMasterListBo> masterBoList) {
        if (Objects.isNull(thirdDivisionId) || thirdDivisionId == 0L) {
            return;
        }
        if (Objects.isNull(serveLv3Id) || serveLv3Id == 0L) {
            return;
        }

        List<String> rowKeyList = masterBoList.stream().map(siteOrderDetailMasterListBo -> siteOrderDetailMasterListBo.getMasterId() + "_" + thirdDivisionId + "_" + serveLv3Id).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("rank_by_finish_order_cnt");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList, fieldColumnList, "mst_third_division_id_serve_lv3_stat");
        Map<String, Integer> rankByFinishOrderCntMap = new HashMap<>();

        if (Objects.nonNull(resultArray) && resultArray.size() > 0) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String) jsonObject.get("master_id");

                if (jsonObject.containsKey("rank_by_finish_order_cnt")) {
                    String rankByFinishOrderCnt = (String) jsonObject.get("rank_by_finish_order_cnt");
                    if (!Strings.isNullOrEmpty(rankByFinishOrderCnt)) {
                        rankByFinishOrderCntMap.put(masterId, Integer.valueOf(rankByFinishOrderCnt));
                    }
                }
            }
            for (SiteOrderDetailMasterListBo masterBo : masterBoList) {

                if (rankByFinishOrderCntMap.containsKey(masterBo.getMasterId().toString())) {
                    //填充师傅三级地址三级服务完工单量排名指标
                    masterBo.setMasterThirdDivisionIdServeLv3IdRankByFinishOrderCnt(rankByFinishOrderCntMap.get(masterBo.getMasterId().toString()));
                }
            }
        }

    }


    private List<SiteOrderDetailMasterListBo> paginateSiteOrderDetailMasterListBo(List<SiteOrderDetailMasterListBo> masterBoList, int pageNumber, int pageSize) {
        if (masterBoList == null || masterBoList.isEmpty()) {
            return new ArrayList<>();
        }

        int fromIndex = (pageNumber - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, masterBoList.size());

        if (fromIndex >= masterBoList.size()) {
            return new ArrayList<>();
        }

        return masterBoList.subList(fromIndex, toIndex);
    }

    @Override
    public List<OrderPush> selectFamilyOrderPushList(List<Long> provinceNextId, Long masterId,Integer limitCount){
        return orderPushRepository.selectFamilyOrderPushList(provinceNextId,masterId,limitCount);
    }

    @Override
    public List<OrderPush> selectPushMasterList(List<Long> provinceNextId, Long orderId,Integer distance,Integer pageNum,Integer pageSize){
        Page<?> page = PageHelper.startPage(pageNum, pageSize,false);
        return orderPushRepository.selectPushMasterList(provinceNextId,orderId,distance);
    }
}
