package com.wanshifu.infrastructure.gatewayimpl;

import com.wanshifu.domain.agent.gateway.AgentPushDetailManagerGateway;
import com.wanshifu.domain.push.model.AgentPushMaster;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.infrastructure.AgentPushDetail;
import com.wanshifu.infrastructure.provider.MapperProviderSupport;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-09-20 23:05
 * @Description
 * @Version v1
 **/
@Service
public class AgentPushDetailManagerGatewayImpl extends MapperProviderSupport implements AgentPushDetailManagerGateway {


    @Override
    public void addPushRecording(Long orderId, List<AgentPushMaster> agentPushMasterSet, Integer nobodyOfferHour, Date pushTime) {
        if(CollectionUtils.isNotEmpty(agentPushMasterSet)){
            List<AgentPushDetail> insertAgentPushDetailList = new ArrayList<>();
            List<Long> updateAgentPushDetailList = new ArrayList<>();

            //查询订单下代理商推单师傅
            List<AgentPushDetail> agentPushDetails = agentPushDetailRepository.selectListByOrderId(orderId);
            Map<Long, List<AgentPushDetail>> agentPushDetailMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(agentPushDetails)) {
                agentPushDetailMap = agentPushDetails.stream().collect(Collectors.groupingBy(AgentPushDetail::getAgentId));
            }

            for (AgentPushMaster agentPushMaster: agentPushMasterSet) {
                Long agentId = agentPushMaster.getAgentId();
                if (agentId.toString().equals(agentPushMaster.getMasterList())) {
                    //B端团队师傅
                    continue;
                }
                if(StringUtils.isNotBlank(agentPushMaster.getMasterList())){
                    List<Long> masterList = stringToLongList((agentPushMaster.getMasterList()));
                    if(CollectionUtils.isNotEmpty(masterList)){
                        masterList =  masterList.stream().distinct().collect(Collectors.toList());
                         for (Long masterId: masterList) {
                             List<AgentPushDetail> agentPushDetailList = agentPushDetailMap.get(agentId);
                             AgentPushDetail agentPushDetail = CollectionUtils.isNotEmpty(agentPushDetailList) ?
                                     agentPushDetailList.stream().filter(it -> it.getMasterId().equals(masterId)).findFirst().orElse(null) : null;
                             if (Objects.nonNull(agentPushDetail)){
                                 updateAgentPushDetailList.add(agentPushDetail.getId());
                            }else {
                                 AgentPushDetail detail = new AgentPushDetail();
                                 detail.setAgentId(agentId);
                                 detail.setOrderId(orderId);
                                 detail.setMasterId(masterId);
                                 detail.setNobodyOfferHour(nobodyOfferHour);
                                 detail.setPushTime(pushTime);
                                 detail.setCreateTime(new Date());
                                 detail.setUpdateTime(new Date());
                                 insertAgentPushDetailList.add(detail);
                            }
                        }
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(insertAgentPushDetailList)){
                agentPushDetailRepository.insertList(insertAgentPushDetailList);
            }

            if (CollectionUtils.isNotEmpty(updateAgentPushDetailList)) {
                agentPushDetailRepository.branchUpdateAgentPushDetail(updateAgentPushDetailList,
                        nobodyOfferHour,pushTime);
            }
        }
    }

    @Override
    public void viewOrder(Long orderId,Long masterId,Date viewTime){
        //可能存在多条情况
        List<AgentPushDetail> pushDetailList = agentPushDetailRepository.selectByOrderIdAndMasterId(orderId,masterId);
        if (CollectionUtils.isNotEmpty(pushDetailList)) {
            for(AgentPushDetail pushDetail: pushDetailList) {
                if(isAgentPush(pushDetail) && pushDetail.getFirstViewTime() == null){
                    AgentPushDetail agentPushDetail = new AgentPushDetail();
                    agentPushDetail.setId(pushDetail.getId());
                    agentPushDetail.setFirstViewTime(viewTime);
                    agentPushDetailRepository.updateByPrimaryKeySelective(agentPushDetail);
                }
            }
        }
    }

    @Override
    public void offerPrice(Long orderId,Long masterId,Date offerTime){
        setOfferTime(orderId,masterId,offerTime);
    }

    @Override
    public void grabOrder(Long orderId,Long masterId,Date grabTime){
        setOfferTime(orderId,masterId,grabTime);
    }

    @Override
    public void disinterestOrder(Long orderId,Long masterId,Date disinterestTime){
        List<AgentPushDetail> pushDetailList = agentPushDetailRepository.selectByOrderIdAndMasterId( orderId, masterId);
        if (CollectionUtils.isNotEmpty(pushDetailList)) {
            for (AgentPushDetail pushDetail:pushDetailList) {
                if(isAgentPush(pushDetail) && pushDetail.getDisinterestTime() == null){
                    AgentPushDetail agentPushDetail = new AgentPushDetail();
                    agentPushDetail.setId(pushDetail.getId());
                    agentPushDetail.setDisinterestTime(disinterestTime);
                    agentPushDetailRepository.updateByPrimaryKeySelective(agentPushDetail);
                }
            }
        }

    }
    private void setOfferTime(Long orderId,Long masterId,Date offerTime){
        List<AgentPushDetail> pushDetailList = agentPushDetailRepository.selectByOrderIdAndMasterId(orderId,masterId);
        if (CollectionUtils.isNotEmpty(pushDetailList)) {
            for (AgentPushDetail pushDetail:pushDetailList) {
                if(isAgentPush(pushDetail) && pushDetail.getOfferTime() == null){
                    AgentPushDetail agentPushDetail = new AgentPushDetail();
                    agentPushDetail.setId(pushDetail.getId());
                    agentPushDetail.setOfferTime(offerTime);
                    agentPushDetailRepository.updateByPrimaryKeySelective(agentPushDetail);
                }
            }

        }

    }


    private List<Long> stringToLongList(String str) {
        return Arrays.stream(str.split(","))
                .map(s -> Long.parseLong(s.trim()))
                .collect(Collectors.toList());
    }
    private boolean isAgentPush(AgentPushDetail agentPushDetail){
        Date date = DateUtils.addHours(agentPushDetail.getPushTime(),agentPushDetail.getNobodyOfferHour());
        return date.compareTo(new Date()) > 0;
    }
}
