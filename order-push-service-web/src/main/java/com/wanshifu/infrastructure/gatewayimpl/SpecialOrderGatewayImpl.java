package com.wanshifu.infrastructure.gatewayimpl;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.model.InternalProducerTagEnum;
import com.wanshifu.domain.base.tools.TopicHelper;
import com.wanshifu.domain.special.model.ClearMasterOrderDistance;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.MasterOrderDistance;
import com.wanshifu.infrastructure.OrderDistance;
import com.wanshifu.infrastructure.OrderDistanceConditionConfig;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.master.order.domains.enums.ServeStatus;
import com.wanshifu.order.offer.domains.enums.OrderStatus;
import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import com.wanshifu.domain.special.model.SpecialOrderCountReqBo;
import com.wanshifu.domain.special.model.SpecialOrderCountRespBo;
import com.wanshifu.infrastructure.provider.MapperProviderSupport;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.push.enums.PushBusinessCode;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GeodeticCurve;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.wanshifu.domain.base.model.InternalProducerTagEnum.NEARBY_ORDER_DISTANCE;

/**
 * <AUTHOR>
 * @Date 2024-02-19 15:35
 * @Description
 * @Version v1
 **/
@Component
public class SpecialOrderGatewayImpl extends MapperProviderSupport
        implements SpecialOrderGateway {

    @Value("${wanshifu.rocketMQ.side-order-topic}")
    private String sideOrderDistanceTopic;

    @Value("${wanshifu.rocketMQ.nearby-order-topic}")
    private String nearbyOrderTopic;

    /**
     * 订单顺路单距离开关
     */
    @Value("${order.side.distance.switch:1}")
    private Integer sideOrderSwitch;
    /**
     * 清除特殊订单条数
     */
    @Value("${order.special.pageSize}")
    protected int pageSize;

    @Resource
    private AsyncConfigurer asyncConfigurer;

    @Override
    public SpecialOrderCountRespBo getSpecialOrderCount(SpecialOrderCountReqBo specialOrderCountReqBo) {
        Long masterId = specialOrderCountReqBo.getMasterId();
        List<Long> orderIds = specialOrderCountReqBo.getOrderIds();
        Map<Long, OrderBase> orderBaseMap = specialOrderCountReqBo.getOrderBaseMap();
        Map<Long, OrderGrab> orderGrabMapMap = specialOrderCountReqBo.getOrderGrabMapMap();
        Integer orderType = Optional.ofNullable(specialOrderCountReqBo.getOrderType()).orElse(0);
        List<Long> provinceNextId = specialOrderCountReqBo.getProvinceNextId();
        List<Integer> tmplCityFlag = specialOrderCountReqBo.getTmplCityFlag();

        SpecialOrderCountRespBo specialOrderCountRespBo = new SpecialOrderCountRespBo();

        if (CollectionUtils.isEmpty(orderIds)) {
            return specialOrderCountRespBo;
        }
        if (MapUtils.isEmpty(orderBaseMap)) {
            return specialOrderCountRespBo;
        }
        if (MapUtils.isEmpty(orderGrabMapMap)) {
            return specialOrderCountRespBo;
        }
        Map<Long, OrderPush> orderPushMap = orderPushRepository.selectByMasterIdOrderIds(provinceNextId, masterId, orderIds).stream().collect(Collectors.toMap(OrderPush::getOrderId, ob -> ob));
        if (MapUtils.isEmpty(orderPushMap)) {
            return specialOrderCountRespBo;
        }
        //附近单
        List<SpecialOrderCountRespBo.SpecialOrderCount> nearbyOrderList = new ArrayList<>();
        for (Long orderId : orderIds) {
            OrderBase orderBase = orderBaseMap.get(orderId);
            //订单存在且在交易中
            if (ObjectUtil.isNull(orderBase) || !OrderStatus.TRADING.code.equals(orderBase.getOrderStatus())) {
                continue;
            }
            //订单指派记录不存在
            OrderGrab orderGrab = orderGrabMapMap.get(orderId);
            if (ObjectUtil.isNull(orderGrab) || orderGrab.getIsDelete() == 1) {
                continue;
            }
            //订单推送记录不存在
            OrderPush orderPush = orderPushMap.get(orderId);
            if (ObjectUtil.isNull(orderPush) || orderPush.getIsDelete() == 1 || orderPush.getPushDistance() == 0) {
                continue;
            }

            //返回附近单
            List<OrderPush> orderPushList = this.getFilterNearbyOrderPush(provinceNextId, orderId, masterId,
                    orderGrab.getSecondDivisionId(), 1,
                    orderBase.getCategoryId(), orderPush.getPushDistance(),
                    Boolean.FALSE, tmplCityFlag);
            if (CollUtil.isNotEmpty(orderPushList) && orderPushList.size() > 0) {
                SpecialOrderCountRespBo.SpecialOrderCount nearbyOrder = new SpecialOrderCountRespBo.SpecialOrderCount();
                nearbyOrder.setOrderId(orderId);
                nearbyOrder.setNumber(orderPushList.size());
                nearbyOrderList.add(nearbyOrder);
            }

        }
        specialOrderCountRespBo.setNearbyOrderList(nearbyOrderList);
        return specialOrderCountRespBo;
    }


    @Override
    public List<OrderPush> getFilterNearbyOrderPush(List<Long> provinceNextIds, Long officeOrderId, Long masterId,
                                                    Long secondDivisionId, Integer orderType, Integer categoryId,
                                                    Long pushDistance, boolean skipCheckOrderPushDistance, List<Integer> tmplCityFlag) {
        //1,订单距离开关,附近单
        if (!getSpecialOrderSwitch(2)) {
            return null;
        }

        List<OrderDistance> filterOrderDistanceList = new ArrayList<>();
        //2,查询师傅-订单距离配置
        if (!skipCheckOrderPushDistance) {
            OrderDistanceConditionConfig masterConditionConfig = orderDistanceConditionConfigRepository.selectByDistanceTypeAndCategoryAndDivision(2, 0, categoryId, secondDivisionId);
            if (ObjectUtil.isNull(masterConditionConfig) || pushDistance <= masterConditionConfig.getAddressDistance()) {
                return null;
            }
        }

        //3,查询订单距离配置
        List<OrderDistanceConditionConfig> conditionConfigs = orderDistanceConditionConfigRepository.selectByDistanceTypeAndCategoryAndDivisionList(2, Collections.singletonList(1), null, secondDivisionId);
        if (CollUtil.isEmpty(conditionConfigs)) {
            return null;
        }
        //4,查询报价订单附近单
        List<OrderDistance> orderDistances = orderDistanceRepository.selectByOrderIds(officeOrderId);
        if (CollUtil.isEmpty(orderDistances)) {
            return null;
        }
        //5,过滤配置条件
        for (OrderDistance orderDistance : orderDistances) {
            OrderDistanceConditionConfig config = conditionConfigs.stream().filter(e -> orderDistance.getDistanceCategoryId().equals(e.getCategoryId())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(config) && orderDistance.getOrderAddressDistance() < config.getAddressDistance()) {
                filterOrderDistanceList.add(orderDistance);
            }
        }
        if (CollUtil.isEmpty(filterOrderDistanceList)) {
            return null;
        }
        //查询师傅待报价列表订单
        List<Long> distanceOrderIds = filterOrderDistanceList.stream().map(OrderDistance::getDistanceOrderId).collect(Collectors.toList());
        Map<Long, OrderPush> orderPushMap = orderPushRepository.selectByMasterIdAndOrderIdsFiler(provinceNextIds, masterId, distanceOrderIds, tmplCityFlag).stream().collect(Collectors.toMap(OrderPush::getOrderId, Function.identity()));
        List<OrderPush> pushes = new ArrayList<>();
        for (OrderDistance list : filterOrderDistanceList) {
            OrderPush orderPush = orderPushMap.get(list.getDistanceOrderId());
            if (ObjectUtil.isNull(orderPush)) {
                continue;
            }
            //附近相似单,使用订单之间的距离
            if (orderType == 2) {
                orderPush.setPushDistance(list.getOrderAddressDistance());
            }

            pushes.add(orderPush);
            if (pushes.size() >= 5) {
                break;
            }
        }

        return pushes;

    }


    @Override
    public List<MasterOrderDistance> getFilterSideOrderPush(Long officeOrderId, Long masterId, Long secondDivisionId) {
        List<MasterOrderDistance> filterOrderDistanceList = new ArrayList<>();
        //订单距离开关,顺路单/附近单
        if (!getSpecialOrderSwitch(1)) {
            return null;
        }

        //查询配置
        List<OrderDistanceConditionConfig> conditionConfigs = orderDistanceConditionConfigRepository.selectByDistanceTypeAndCategoryAndDivisionList(1, Collections.singletonList(1), null, secondDivisionId);
        if (CollUtil.isEmpty(conditionConfigs)) {
            return filterOrderDistanceList;
        }
        //查询报价订单与服务中订单数
        List<MasterOrderDistance> orderDistances = masterOrderDistanceRepository.selectListByOrderIdAndMasterIdAndCategoryAndDistance(officeOrderId, masterId);
        if (CollUtil.isEmpty(orderDistances)) {
            return filterOrderDistanceList;
        }
        //过滤配置条件
        for (MasterOrderDistance orderDistance : orderDistances) {

            OrderDistanceConditionConfig config = conditionConfigs.stream().filter(e -> orderDistance.getCategoryId().equals(e.getCategoryId())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(config) && orderDistance.getOrderAddressDistance() < config.getAddressDistance()) {
                filterOrderDistanceList.add(orderDistance);
            }
        }
        return filterOrderDistanceList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer clearSideOrder(int fromDayNumber, int queryNumber, int perDeleteNumber) {
        Date date = new Date();
        Date fromDayTime = fromDayNumber > 0 ? DateUtils.addDays(date, -fromDayNumber) : date;
        //批量查询过期订单距离
        List<Long> ids = masterOrderDistanceRepository.selectByDistanceCreateTimeList(fromDayTime, queryNumber);
        int pushSize = ids.size();

        //分批删除数据
        if (CollectionUtils.isNotEmpty(ids)) {
            int pages = ids.size() / perDeleteNumber;
            if (pages == 0) {
                masterOrderDistanceRepository.deleteFromIdList(ids);
            } else {
                for (int i = 0; i < pages; i++) {
                    final int index = i;
                    CompletableFuture.runAsync(() -> masterOrderDistanceRepository.deleteFromIdList(ids.subList(index * pageSize, (index + 1) * pageSize)), asyncConfigurer.getAsyncExecutor());
                }
            }
        }
        return pushSize;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer clearNearbyOrder(int fromDayNumber, int queryNumber, int perDeleteNumber) {
        Date date = new Date();
        Date fromDayTime = fromDayNumber > 0 ? DateUtils.addDays(date, -fromDayNumber) : date;
        //批量查询过期订单距离
        List<Long> ids = orderDistanceRepository.selectByDistanceCreateTimeList(fromDayTime, queryNumber);
        int size = ids.size();

        //分批删除数据
        if (CollectionUtils.isNotEmpty(ids)) {
            int pages = ids.size() / perDeleteNumber;
            if (pages == 0) {
                orderDistanceRepository.deleteByDistanceOrderId(ids);
            } else {
                for (int i = 0; i < pages; i++) {
                    final int index = i;
                    CompletableFuture.runAsync(() -> orderDistanceRepository.deleteByDistanceOrderId(ids.subList(index * pageSize, (index + 1) * pageSize)),asyncConfigurer.getAsyncExecutor());
                }
            }
        }
        return size;
    }


    @Override
    public Boolean checkSideOrderDistanceConfig(Integer orderType, Integer categoryId, Long regionId, Long pushDistance) {
        if ((!this.getSpecialOrderSwitch(orderType)) || pushDistance == 0 || (ObjectUtil.isNull(regionId) || regionId == 0)) {
            return false;
        }
        //判断师傅距离是否满足
        OrderDistanceConditionConfig masterConfig = orderDistanceConditionConfigRepository.selectByDistanceTypeAndCategoryAndDivision(orderType, 0, categoryId, regionId);
        if (ObjectUtil.isNull(masterConfig) || (ObjectUtil.isNotNull(masterConfig) && pushDistance <= masterConfig.getAddressDistance())) {
            return false;
        }
        //判断订单距离配置是否存在
        OrderDistanceConditionConfig conditionConfig = orderDistanceConditionConfigRepository.selectByOrderDistanceTypeAndCategoryAndDivision(orderType, null, regionId);
        if (ObjectUtil.isNotNull(conditionConfig)) {
            return true;
        }
        return false;
    }

    /**
     * 发送附近单距离mq[自产自消]
     *
     * @param orderId
     * @return
     */
    @Override
    public Integer nearbyOrderPushDistance(Long orderId) {
        Map<String, Object> nearbyInfo = new HashMap<>();
        nearbyInfo.put("orderId", JSON.toJSONString(orderId));
        messageSenderService.sendSyncMessage(nearbyOrderTopic, NEARBY_ORDER_DISTANCE.tag, JSON.toJSONString(nearbyInfo));
        return 1;
    }


    @Override
    public OrderDistanceConditionConfig selectByOrderDistanceTypeAndCategoryAndDivision(Integer orderType, Integer categoryId, Long divisionId) {
        return orderDistanceConditionConfigRepository.selectByOrderDistanceTypeAndCategoryAndDivision(orderType, categoryId, divisionId);
    }

    @Override
    public OrderDistance selectOrderDistanceByOrderId(Long orderId) {
        return orderDistanceRepository.selectOrderDistanceByOrderId(orderId);
    }

    @Override
    @Transactional
    public Integer insertNearbyOrderList(OrderBase orderBase, OrderGrab orderGrab, OrderExtraData orderExtraData, List<OrderBase> nearByOrderBaseList, Map<Long, OrderExtraData> nearByOrderExtraDataMap) {
        List<OrderDistance> orderDistanceList = new ArrayList<>();
        List<OrderDistance> nearByOrderDistanceList = new ArrayList<>();

        Long orderId = orderBase.getOrderId();

        //2,循环计算距离
        List<List<OrderBase>> partitionOrderBaseList = Lists.partition(nearByOrderBaseList, 10);
        partitionOrderBaseList.forEach(orderBaseList -> {
            //3,查询附近单拉取记录//存在则表示拉取过附近单,需要补偿数据
            List<Long> collect = orderBaseList.stream().map(OrderBase::getOrderId).collect(Collectors.toList());
            List<OrderDistance> orderDistances = orderDistanceRepository.selectByOrderIdList(collect);

            orderBaseList.forEach(e -> {
                OrderExtraData nearByOrderExtraData = nearByOrderExtraDataMap.get(e.getOrderId());
                if (ObjectUtil.isNotNull(nearByOrderExtraData)) {
                    //4,计算订单之间距离
                    Long distance = this.getStraightLintDistance(orderExtraData.getBuyerAddressLatitude().doubleValue(), orderExtraData.getBuyerAddressLongitude().doubleValue()
                            , nearByOrderExtraData.getBuyerAddressLatitude().doubleValue(), nearByOrderExtraData.getBuyerAddressLongitude().doubleValue());

                    OrderDistance insertOrderDistance = new OrderDistance();
                    insertOrderDistance.setOrderId(orderId);
                    insertOrderDistance.setDistanceOrderId(e.getOrderId());
                    insertOrderDistance.setCategoryId(orderBase.getCategoryId());
                    insertOrderDistance.setDistanceCategoryId(e.getCategoryId());
                    insertOrderDistance.setOrderAddressDistance(distance);
                    insertOrderDistance.setDistanceCreateTime(new Date());
                    insertOrderDistance.setIsDelete(0);
                    insertOrderDistance.setNote("写入附近单距离");
                    insertOrderDistance.setSecondDivisionId(orderGrab.getSecondDivisionId());
                    insertOrderDistance.setThirdDivisionId(orderBase.getThirdDivisionId());
                    insertOrderDistance.setCreateTime(new Date());
                    insertOrderDistance.setUpdateTime(new Date());
                    orderDistanceList.add(insertOrderDistance);

                    //5,补偿数:对应附近单是否已经拉取过:未拉取则不用补充数据
                    List<OrderDistance> filterNearbyOrderDistanceList = orderDistances.stream().filter(l -> l.getOrderId().equals(e.getOrderId())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(filterNearbyOrderDistanceList)) {
                        //6,对应附近单已经拉取过:但本次这个订单,需要补偿数据
                        OrderDistance orElse = filterNearbyOrderDistanceList.stream().filter(f -> f.getDistanceOrderId().equals(orderId)).findFirst().orElse(null);
                        if (ObjectUtil.isNull(orElse)) {

                            OrderDistance nearbyOrderDistance = new OrderDistance();
                            nearbyOrderDistance.setOrderId(e.getOrderId());
                            nearbyOrderDistance.setDistanceOrderId(orderId);
                            nearbyOrderDistance.setCategoryId(e.getCategoryId());
                            nearbyOrderDistance.setDistanceCategoryId(orderBase.getCategoryId());
                            nearbyOrderDistance.setOrderAddressDistance(distance);
                            nearbyOrderDistance.setDistanceCreateTime(new Date());
                            nearbyOrderDistance.setIsDelete(0);
                            nearbyOrderDistance.setNote("补偿写入附近单距离");
                            nearbyOrderDistance.setSecondDivisionId(orderGrab.getSecondDivisionId());
                            nearbyOrderDistance.setThirdDivisionId(orderBase.getThirdDivisionId());
                            nearbyOrderDistance.setCreateTime(new Date());
                            nearbyOrderDistance.setUpdateTime(new Date());
                            nearByOrderDistanceList.add(nearbyOrderDistance);
                        }
                    }
                }
            });
        });

        //写入拉取订单的附近单
        Integer insert = 0;
        if (CollUtil.isNotEmpty(orderDistanceList) && orderDistanceList.size() > 0) {
            try {
                int insertList = orderDistanceRepository.insertList(CollUtil.distinct(orderDistanceList));
                insert = insertList + insert;
            }catch (Exception e) {

            }

        }
        //写入附近单补偿数据
        if (CollUtil.isNotEmpty(nearByOrderDistanceList) && nearByOrderDistanceList.size() > 0) {
            try {
                int insertList = orderDistanceRepository.insertList(CollUtil.distinct(nearByOrderDistanceList));
                insert = insertList + insert;
            }catch (Exception e) {

            }


        }

        return insert;
    }

    /**
     * 获取直线距离
     *
     * @param sourceLatitude
     * @param sourceLongitude
     * @param targetLatitude
     * @param targetLongitude
     * @return
     */
    public Long getStraightLintDistance(double sourceLatitude, double sourceLongitude, double targetLatitude, double targetLongitude) {
        GlobalCoordinates source = new GlobalCoordinates(sourceLatitude, sourceLongitude);
        GlobalCoordinates target = new GlobalCoordinates(targetLatitude, targetLongitude);

        GeodeticCurve curve = new GeodeticCalculator().calculateGeodeticCurve(Ellipsoid.Sphere, source, target);

        return new Double(curve.getEllipsoidalDistance()).longValue();
    }

    @Override
    public Integer batchInsertOrderDistance(List<Long> officeOrderIds, Map<Long, OrderBase> officeOrderBaseMap, Map<Long, OrderExtraData> officeOrderExtraDataMap, Map<Long, OrderBase> servingOrderBaseMap, Map<Long, OrderExtraData> servingOrderExtraDataMap, Map<Long, Long> orderServeInfoMap, Long masterId, List<Long> servingOrderIds, Long cityId) {
        List<MasterOrderDistance> orderDistances = masterOrderDistanceRepository.selectListByOrderIdsAndMasterId(officeOrderIds, masterId, servingOrderIds);

        //1,报价订单循环处理
        List<MasterOrderDistance> distanceList = new ArrayList<>();
        officeOrderIds.forEach(orderId -> {
            OrderBase orderBase = officeOrderBaseMap.get(orderId);
            if (OrderStatus.TRADING.code.equals(orderBase.getOrderStatus())) {

                Integer categoryId = orderBase.getCategoryId();
                //2,满足顺路单条件
                List<OrderDistanceConditionConfig> conditionConfigs = orderDistanceConditionConfigRepository.selectByDistanceTypeAndCategoryAndDivisionList(1, Collections.singletonList(1), null, cityId);

                if (CollUtil.isNotEmpty(conditionConfigs)) {
                    List<Long> orderIds = new ArrayList<>();
                    orderIds.addAll(servingOrderIds);
                    OrderExtraData orderExtraData = officeOrderExtraDataMap.get(orderId);
                    //3,查询已经存在得订单距离数据:更新距离

                    List<MasterOrderDistance> collect = orderDistances.stream().filter(orderDistance -> orderId.equals(orderDistance.getOrderId())).distinct().collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)) {
                        collect.forEach(e -> {
                            OrderBase servingOrderBase = servingOrderBaseMap.get(e.getDistanceOrderId());
                            OrderExtraData servingOrderExtraData = servingOrderExtraDataMap.get(e.getDistanceOrderId());
                            //计算订单之间得距离
                            Long distance = this.getStraightLintDistance(orderExtraData.getBuyerAddressLatitude().doubleValue(), orderExtraData.getBuyerAddressLongitude().doubleValue()
                                    , servingOrderExtraData.getBuyerAddressLatitude().doubleValue(), servingOrderExtraData.getBuyerAddressLongitude().doubleValue());
                            //更新订单距离
                            if (!distance.equals(e.getOrderAddressDistance())) {
                                masterOrderDistanceRepository.updateByPrimaryKeyAndDistance(e.getOrderDistanceId(), distance, e.getNote().concat(DateUtils.getDateTime() + ":更新订单距离 "), servingOrderBase.getCategoryId());
                            }

                            orderIds.remove(e.getDistanceOrderId());
                        });
                    }

                    //4,批量插入订单距离
                    if (CollUtil.isNotEmpty(orderIds)) {
                        orderIds.forEach(servingOrderId -> {
                            OrderBase servingOrderBase = servingOrderBaseMap.get(servingOrderId);
                            OrderExtraData servingOrderExtraData = servingOrderExtraDataMap.get(servingOrderId);
                            Long distance = this.getStraightLintDistance(orderExtraData.getBuyerAddressLatitude().doubleValue(), orderExtraData.getBuyerAddressLongitude().doubleValue()
                                    , servingOrderExtraData.getBuyerAddressLatitude().doubleValue(), servingOrderExtraData.getBuyerAddressLongitude().doubleValue());
                            //获取类目的配置
                            OrderDistanceConditionConfig conditionConfig = conditionConfigs.stream().filter(config -> servingOrderBase.getCategoryId().equals(config.getCategoryId())).findFirst().orElse(null);

                            if (ObjectUtil.isNotNull(conditionConfig) && conditionConfig.getAddressDistance() >= distance && ObjectUtil.isNotNull(orderServeInfoMap.get(servingOrderId))) {
                                MasterOrderDistance orderDistance = new MasterOrderDistance();
                                orderDistance.setOrderId(orderId);
                                orderDistance.setDistanceOrderId(servingOrderId);
                                orderDistance.setOrderAddressDistance(distance);
                                orderDistance.setCategoryId(servingOrderBase.getCategoryId());
                                orderDistance.setOrderServeId(orderServeInfoMap.get(servingOrderId));
                                orderDistance.setDistanceCreateTime(new Date());
                                orderDistance.setCreateTime(new Date());
                                orderDistance.setUpdateTime(new Date());
                                orderDistance.setIsDelete(0);
                                orderDistance.setNote(DateUtils.getDateTime() + ":新写入距离 ");
                                orderDistance.setMasterId(masterId);
                                orderDistance.setDistanceOrderServeStatus(1);
                                distanceList.add(orderDistance);
                            }

                        });
                    }
                }
            }
        });
        //保存
        if (CollUtil.isNotEmpty(distanceList) && distanceList.size() > 0) {
            return masterOrderDistanceRepository.insertList(distanceList);
        }
        return 0;
    }

    /**
     * 特殊单开关,顺路单:2,附近单:3,全开:1,0:全关
     *
     * @param orderType 1:顺路单,2:附近单,0:全部
     * @return
     */
    @Override
    public Boolean getSpecialOrderSwitch(Integer orderType) {
        if (sideOrderSwitch == 1) {
            return true;
        } else if (orderType == 1 && sideOrderSwitch == 2) {//顺路单
            return true;
        } else if (orderType == 2 && sideOrderSwitch == 3) {//附近单
            return true;
        }
        return false;
    }

    @Override
    public Integer clearNearbyOrderList(Long orderId) {
        OrderDistance orderDistance = super.selectByOrderId(orderId);
        if (ObjectUtil.isNull(orderDistance)) {
            throw new BusException(PushBusinessCode.MQ_IGNORE_ERROR.code,"订单没有附近单,无需清除,orderId:" + orderId);
        }
        //物理删除订单距离信息[附近单:A-B,B-A]
        return this.softDeleteOrderDistance(orderId);
    }

    @Override
    public Integer softDeleteOrderDistance(Long orderId) {
        int count = 0;
        while (true) {
            int updateCount = orderDistanceRepository.softDeleteOrderDistanceByOrderId(orderId, 100);
            count = count + updateCount;
            if (updateCount < 100) {
                break;
            }
        }
        while (true) {
            int updateCount = orderDistanceRepository.softDeleteOrderDistanceByDistanceOrderId(orderId, 100);
            count = count + updateCount;
            if (updateCount < 100) {
                break;
            }
        }
        return count;
    }

    @Override
    public Boolean checkNearbyOrderDistanceConfig(Integer orderType, Long regionId) {
        if ((!this.getSpecialOrderSwitch(orderType)) || (ObjectUtil.isNull(regionId) || regionId == 0)) {
            return false;
        }
        //判断订单距离配置是否存在
        OrderDistanceConditionConfig conditionConfig = orderDistanceConditionConfigRepository.selectByOrderDistanceTypeAndCategoryAndDivision(orderType, null, regionId);
        if (ObjectUtil.isNotNull(conditionConfig)) {
            return true;
        }
        return false;
    }
}
