package com.wanshifu.infrastructure.gatewayimpl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.model.DataSourceType;
import com.wanshifu.domain.push.gateway.OrderPushResourceGateway;
import com.wanshifu.domain.push.model.BatchGetMasterViewNumberV2RqtBo;
import com.wanshifu.domain.push.model.GetMasterOrderCategoryRateBo;
import com.wanshifu.domain.push.model.WaitOfferCountGateway;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.persistence.annotation.DataSource;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.provider.MapperProviderSupport;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderStatus;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.push.domains.dto.OrderPushDTO;
import com.wanshifu.order.push.request.GetTodayUserHireOrderRqt;
import com.wanshifu.order.push.request.GetWaitOfferMasterIdsByOrderIdRqt;
import com.wanshifu.order.push.request.push.NoOfferByOrderIdRqt;
import com.wanshifu.order.push.response.GetMasterViewNumberResp;
import com.wanshifu.order.push.response.GetTodayUserHireOrderResp;
import com.wanshifu.order.push.response.GetUnreadResp;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 23:17
 */
@Component
public class OrderPushResourceGatewayImpl extends MapperProviderSupport implements OrderPushResourceGateway {

    @Override
    public List<GetMasterOrderCategoryRateBo> getMasterOrderCategoryRate(List<Long> provinceNextId, List<Long> masterIdList) {
        return orderPushRepository.selectMasterOrderCategoryNum(provinceNextId, masterIdList);
    }

    @Override
    public List<Long> getWaitOfferMasterIdsByOrderId(List<Long> provinceNextId, GetWaitOfferMasterIdsByOrderIdRqt rqt) {
        Long orderId = rqt.getOrderId();
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        PageHelper.startPage(pageNum, pageSize, false);
        return orderPushRepository.selectWaitOfferMasterIdsByOrderId(provinceNextId, orderId);
    }

    @Override
    public List<Long> selectMasterCategorySelector(List<Long> provinceNextId, Long masterId, DateTime currentDateTime) {

        return orderPushRepository.selectMasterCategorySelector(provinceNextId, masterId, DateUtil.date());
    }

    @Override
    public List<Long> getUnread(List<Long> provinceNextId, OrderBase orderBase, OrderGrab orderGrab) {
        GetUnreadResp resp = new GetUnreadResp();
        Assert.isTrue(AppointType.OPEN.value.equals(orderGrab.getAppointType()), "该订单不是公开报价订单");
        if (!OrderStatus.TRADING.code.equals(orderBase.getOrderStatus())) {
            return null;
        }
        if (orderGrab.getOfferNumber() > 0 || orderGrab.getHireMasterId() > 0 ||
                (Objects.nonNull(orderGrab.getEndDate()) && orderGrab.getEndDate().compareTo(new Date()) < 0)) {
            return null;
        }
        List<OrderPush> orderPushes = orderPushRepository.selectByOrderId(provinceNextId, orderBase.getOrderId());
        Assert.isTrue(!CollectionUtils.isEmpty(orderPushes), "订单推送信息不存在");
        List<Long> masterIds = orderPushes.stream().filter(orderPush -> orderPush.getFirstViewTime() == null).map(OrderPush::getMasterId).collect(Collectors.toList());
        return masterIds;
    }

    @Override
    public GetMasterViewNumberResp batchGetMasterViewNumberV2(BatchGetMasterViewNumberV2RqtBo rqtBo, OrderBase orderBase) {
        Long globalOrderTraceId = rqtBo.getGlobalOrderTraceId();
        List<Long> provinceNextId = rqtBo.getProvinceNextId();
        int masterViewNumber = 0;
        if (ObjectUtil.isNotNull(orderBase)) {
            masterViewNumber = orderPushRepository.selectMasterViewNumber(provinceNextId, orderBase.getOrderId());
        }
        return new GetMasterViewNumberResp(globalOrderTraceId, masterViewNumber);
    }

    @DataSource(DataSourceType.ANALYTIC_DATASOURCE)
    @Override
    public Integer analyticWaitOfferCount(WaitOfferCountGateway waitOfferCountGateway) {
        return orderPushRepository.analyticWaitOfferCount(waitOfferCountGateway);
    }


    @Override
    public Integer analyticWaitOfferCountV2(WaitOfferCountGateway waitOfferCountGateway) {
        return orderPushRepository.analyticWaitOfferCount(waitOfferCountGateway);
    }

    @Override
    public GetTodayUserHireOrderResp getTodayUserHireOrder(List<Long> provinceNextId, GetTodayUserHireOrderRqt getTodayUserHireOrderRqt) {
        Long masterId = getTodayUserHireOrderRqt.getMasterId();
        Date nowTime = getTodayUserHireOrderRqt.getNowTime();

        List<OrderPush> orderPushList = orderPushRepository.selectUserHireOrder(provinceNextId, masterId, DateUtils.toDateStart(nowTime), DateUtils.toDateEnd(nowTime));
        GetTodayUserHireOrderResp getTodayUserHireOrderResp = new GetTodayUserHireOrderResp();
        if (CollectionUtils.isNotEmpty(orderPushList)) {
            getTodayUserHireOrderResp.setOrderId(orderPushList.get(0).getOrderId());
        } else {
            getTodayUserHireOrderResp.setOrderId(null);
        }
        return getTodayUserHireOrderResp;
    }

    @Override
    public List<OrderPush> getMasterNormalOrderPushList(List<Long> provinceNextId, Long masterId) {
        return orderPushRepository.getMasterNormalList(provinceNextId, masterId);
    }

    @Override
    public List<OrderPush> batchGetOrderPushByOrderIdsAndMasterId(List<Long> provinceNextId, List<Long> orderIds, Long masterId) {
        return orderPushRepository.selectByMasterIdOrderIds(provinceNextId, masterId, orderIds);
    }

    @Override
    public OrderPush getOrderPush(List<Long> provinceNextId, Long orderId, Long masterId) {
        return orderPushRepository.selectByOrderIdAndMasterId(provinceNextId, orderId, masterId);
    }


    @Override
    public List<OrderPush> getMasterOrderPush(List<Long> provinceNextId, Long masterId,Integer appointType,Integer limitCount) {
        return orderPushRepository.selectByMasterIdAndAppointType(provinceNextId,masterId, appointType,limitCount);
    }

    @Override
    public Integer getOrderShowNumOfPeople(List<Long> provinceNextId, Long orderId) {
        return orderPushRepository.getOrderShowNumOfPeople(provinceNextId, orderId);
    }

    @Override
    public List<OrderPush> getOrderPushByOrderId(List<Long> provinceNextId, Long orderId) {
        return orderPushRepository.selectByOrderId(provinceNextId, orderId);
    }

    @Override
    public List<OrderPush> getOrderPushNoOfferByOrderId(List<Long> provinceNextId, Long orderId) {
        return orderPushRepository.selectOrderPushNoOfferByOrderId(provinceNextId, orderId);
    }

    @Override
    public SimplePageInfo<OrderPush> getOrderPushNoOfferByOrderIdV2(NoOfferByOrderIdRqt rqt) {
        PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        return new SimplePageInfo<>(orderPushRepository.selectOrderPushNoOfferByOrderIdV2(rqt));
    }


    @Override
    public Integer orderPushCount(List<Long> provinceNextId, Long orderId,Integer mode){
        return orderPushRepository.orderPushCount(provinceNextId, orderId,mode);
    }

}
