package com.wanshifu.infrastructure.gatewayimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.domain.base.model.InternalProducerTagEnum;
import com.wanshifu.domain.base.model.RedisKeyConstant;
import com.wanshifu.domain.base.tools.TopicHelper;
import com.wanshifu.domain.base.tools.FeiShuUtils;
import com.wanshifu.domain.push.ability.AsyncDeleteOrderPushExecutor;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.model.enums.BusinessLineIdEnum;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.sdk.offer.gateway.OrderOfferGateway;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DataUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.NoOfferFilterMaster;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.provider.MapperProviderSupport;
import com.wanshifu.master.order.domains.enums.OfferPriceEndReasonType;
import com.wanshifu.order.offer.domains.api.request.infoorder.BatchInsertInfoMasterOrderCloseRqt;
import com.wanshifu.order.offer.domains.api.request.offer.ClearPriceEndRqt;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.offer.PushingOrderCompositeResp;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderOfferPriceEnd;
import com.wanshifu.order.offer.domains.po.OrderOfferPriceFailReason;
import com.wanshifu.order.offer.domains.po.*;
import com.wanshifu.order.push.enums.OrderPushTypeEnum;
import com.wanshifu.order.push.request.push.MasterAddressInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 15:51
 */
@Component
@Slf4j
public class OrderPushManagerGatewayImpl extends MapperProviderSupport implements OrderPushManagerGateway {

    @Value("${order.orderPush.softDelete.limitCount}")
    private int limitCount;
    @Value("${order.orderPush.direct.delete.limitCount:200}")
    private int directDeleteLimitCount;
    @Value("${order.orderPush.delete.delay:5}")
    private int deleteDelay;
    @Value("${order.sideOrder.distance.limitCount:20}")
    private int sideOrderLimitCount;
    /**
     * 推单数据插入分布式锁开关
     */
    @Value("${order.orderPush.distributeLockInsertOrderPushSwitchV2:on}")
    private String distributeLockInsertOrderPushSwitchV2;

    /**
     * 推单数据插入分布式锁超时时间,单位：毫秒
     */
    @Value("${order.orderPush.distributeLockInsertOrderPushExpireTime:6000}")
    private int distributeLockInsertOrderPushExpireTime;

    /**
     * 推单数据插入分布式锁超时时间,单位：毫秒
     */
    @Value("${order.orderPush.distributeLockInsertOrderPushExpireTimeV2:6000}")
    private int distributeLockInsertOrderPushExpireTimeV2;

    @Value("${order.orderPush.lastOfferPriceDeleteSwitch:on}")
    private String lastOfferPriceDeleteSwitch;

    @Value("${order.orderPush.siteInviteMasterOfferSwitch:on}")
    private String siteInviteMasterOfferSwitch;

    /**
     * 推单数据插入批次大小
     */
    @Value("${order.orderPush.insertOrderPushBatchSize:50}")
    private int insertOrderPushBatchSize;

    @Autowired
    protected AsyncConfigurer asyncConfigurer;

    @Resource
    private MessageSenderService messageSenderService;
    @Resource
    private OrderOfferGateway orderOfferGateway;


    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    @Resource
    private AsyncDeleteOrderPushExecutor deleteOrderPushExecutor;

    @Autowired
    private RedisHelper redisHelper;


    @Override
    public List<OrderPush> selectByMasterIdAndOrderIds(List<Long> provinceNextIds, Long masterId, List<Long> orderIdIdList) {
        if (Objects.isNull(masterId) || CollectionUtils.isEmpty(orderIdIdList)) {
            return Collections.EMPTY_LIST;
        }
        return orderPushRepository.selectByMasterIdAndOrderIds(provinceNextIds, masterId, orderIdIdList);
    }

    @Override
    public List<Long> selectInviteMasterByOrderId(List<Long> provinceNextId, Long orderId) {
        if (Objects.isNull(orderId) || orderId <= 0) {
            return Collections.EMPTY_LIST;
        }
        if ("on".equals(siteInviteMasterOfferSwitch)) {
            //new
            List<NoOfferFilterMaster> noOfferFilterMasterList = noOfferFilterMasterRepository.selectByOrderId(orderId);
            if (CollectionUtil.isEmpty(noOfferFilterMasterList)) {
                log.error("selectInviteMasterByOrderId >>> orderId:{},该订单无合适师傅！", orderId);
                return Lists.newArrayList();
            }

            // 去重
            noOfferFilterMasterList = noOfferFilterMasterList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    NoOfferFilterMaster::getMasterId,
                                    e -> e,
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));

            List<Long> masterIds = noOfferFilterMasterList.stream().map(NoOfferFilterMaster::getMasterId).collect(Collectors.toList());
            List<OrderPush> orderPushList = orderPushRepository.selectByOrderIdsAndMasterId(provinceNextId, orderId, masterIds);
            if (CollectionUtil.isEmpty(orderPushList)) {
                log.error("selectInviteMasterByOrderId >>> orderId:{},该订单合适师傅的待报价列表已无该订单！", orderId);
                return Lists.newArrayList();
            }
            List<Long> existsMasterIds = orderPushList.stream().map(OrderPush::getMasterId).collect(Collectors.toList());

            noOfferFilterMasterList = noOfferFilterMasterList.stream().filter(e -> existsMasterIds.contains(e.getMasterId())).collect(Collectors.toList());
            //按师傅评分从高到低截取前10个
            if (noOfferFilterMasterList.size() > 10) {
                noOfferFilterMasterList = noOfferFilterMasterList.stream()
                        .sorted(Comparator.comparing(NoOfferFilterMaster::getScore).reversed())
                        .limit(10)
                        .collect(Collectors.toList());
            }

            return noOfferFilterMasterList.stream().map(NoOfferFilterMaster::getMasterId).collect(Collectors.toList());

        } else {
            //old
            return orderPushRepository.selectInviteMasterByOrderId(provinceNextId, orderId);
        }
    }

    @Override
    public List<OrderPush> selectPushOrderByMasterId(List<Long> provinceNextId, Long masterId) {
        if (Objects.isNull(masterId) || masterId <= 0) {
            return Collections.EMPTY_LIST;
        }
        return orderPushRepository.selectByMasterId(provinceNextId, masterId);
    }

    @Override
    public List<OrderPush> queryOrderToMasterIdsPushInfo(List<Long> provinceNextId, Long orderId, List<Long> masterIds) {
        if (Objects.isNull(orderId)) {
            throw new BusException("query orderId not null");
        }
        return orderPushRepository.selectByOrderIdsAndMasterId(provinceNextId, orderId, masterIds);
    }

    /**
     * 师傅拉取待报价订单列表
     *
     * @param masterId
     * @param orderIdList
     */
    @Override
    @Async
    public void masterPullOrderList(List<Long> provinceNextIds, Long masterId, List<Long> orderIdList) {
        if (CollectionUtils.isNotEmpty(orderIdList)) {
            List<OrderPush> orderPushList = orderPushRepository.selectByMasterIdAndOrderIds(provinceNextIds, masterId, orderIdList);
            if (!CollectionUtils.isEmpty(orderPushList)) {
                List<Long> orderIds = Lists.newArrayList();
                for (OrderPush orderPush : orderPushList) {
                    if (orderPush.getFirstPullTime() != null) {
                        continue;
                    }
                    orderIds.add(orderPush.getOrderId());
                }
                if (CollectionUtil.isNotEmpty(orderIds)) {
                    orderPushRepository.updateFirstPullTime(provinceNextIds, orderIds, masterId);
                }
            }
        }
    }

    /**
     * 更新样板城市推单弹窗读取时间
     * @param orderPushList 样板城市推单
     */
    @Override
    public void updateTmplCityTime(List<OrderPush> orderPushList) {
        if (CollectionUtil.isEmpty(orderPushList)) {
            return;
        }
        orderPushList.forEach(orderPush -> orderPushRepository.updateTmplCityTimeByOrderPush(orderPush, new Date()));
    }

    @Override
    public List<Long> modifyMasterServeRegions(List<Long> provinceNextId, Long masterId,
                                               List<Long> removeServeThirdDivisionIdList,
                                               List<Long> removeServeFourthDivisionIdList,
                                               List<OrderPush> orderPushList,
                                               Map<Long, OrderBase> orderBaseMap) {


        //需要删除order_push 记录的orderId集合
        List<Long> orderIdListTarget = new ArrayList<>();
        List<Long> orderIdList = orderPushList.stream().map(OrderPush::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(removeServeThirdDivisionIdList)) {
            orderBaseMap.forEach((k, v) -> {
                OrderBase orderBase = v;
                if (removeServeThirdDivisionIdList.contains(orderBase.getThirdDivisionId()) && !orderIdListTarget.contains(orderBase.getOrderId())) {
                    orderIdListTarget.add(orderBase.getOrderId());
                }
            });
        }
        orderIdList.removeAll(orderIdListTarget);
        if (CollectionUtils.isNotEmpty(orderIdList) &&
                CollectionUtils.isNotEmpty(removeServeFourthDivisionIdList)) {
            Map<Long, Integer> pushDivisionLevelMap = new HashMap<>();
            orderPushList.forEach(orderPush -> pushDivisionLevelMap.put(orderPush.getOrderId(), orderPush.getPushDivisionLevel()));
            //剩余的订单
            List<OrderBase> residueOrderBases = orderIdList.stream()
                    .map(it -> orderBaseMap.get(it)).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(residueOrderBases)) {
                residueOrderBases.forEach(orderBase -> {
                    if (removeServeFourthDivisionIdList.contains(orderBase.getFourthDivisionId()) && pushDivisionLevelMap.get(orderBase.getOrderId()) == 4 && !orderIdListTarget.contains(orderBase.getOrderId())) {
                        orderIdListTarget.add(orderBase.getOrderId());
                    }
                });
            }
        }

        clearPush(provinceNextId, masterId,
                orderIdListTarget, "(师傅修改服务地区) ", OfferPriceEndReasonType.MODIFY_ORDER);
        return orderIdListTarget;
    }

    @Override
    public List<Long> removeTechnologys(List<Long> provinceNextId, Long masterId,
                                        List<Long> removeTechnologyIds,
                                        Map<Long, OrderBase> orderBaseMap,
                                        List<OrderPush> orderPushList,
                                        String selectedTechnologyIds) {
        //需要删除order_push 记录的orderId集合
        List<Long> orderIdListTarget = new ArrayList<>();
        if (CollectionUtils.isEmpty(removeTechnologyIds)) {
            return orderIdListTarget;
        }

        List<Long> selectedTechnologyIdList = StringUtils.isNotBlank(selectedTechnologyIds) ? StringUtils.splitCommaToList(selectedTechnologyIds, Long::valueOf) : new ArrayList<>();
        orderBaseMap.forEach((k, v) -> {
            String orderTechnologyIds = v.getBindingTechnologyIds().replace("|", ",");
            List<Long> orderTechnologyIdList = StringUtils.splitCommaToList(orderTechnologyIds, Long::valueOf);
            if (CollectionUtils.isNotEmpty(CollectionUtils.intersection(removeTechnologyIds, orderTechnologyIdList))) {
                List<String> technologyIdsList = Arrays.asList(v.getBindingTechnologyIds().split("\\|"));
                boolean offerPrice = technologyIdsList.stream().filter(technologyIds -> selectedTechnologyIdList.containsAll(StringUtils.splitCommaToList(technologyIds, Long::valueOf))).findAny().isPresent();
                if (!offerPrice) {
                    orderIdListTarget.add(v.getOrderId());
                }
            }
        });

        clearPush(provinceNextId, masterId,
                orderIdListTarget, "(师傅移除技能) ", OfferPriceEndReasonType.REMOVE_TECHNOLOGY);
        return orderIdListTarget;
    }

    private void clearPush(List<Long> provinceNextId, Long masterId,
                           List<Long> orderIdListTarget, String note, OfferPriceEndReasonType offerPriceEndReasonType) {
        if (CollectionUtils.isNotEmpty(orderIdListTarget)) {
            this.clearOrderPushNewV2(provinceNextId, orderIdListTarget, masterId, offerPriceEndReasonType);
        }
    }

    public void clearOrderPushNewV2(List<Long> provinceNextId, List<Long> orderIds, Long masterId, OfferPriceEndReasonType offerPriceEndReasonType) {

        List<OrderPush> orderPushes = orderPushRepository.selectByMasterIdOrderIds(provinceNextId, masterId, orderIds);

        List<OrderOfferPriceEnd> orderOfferPriceEndList = new ArrayList<>();
        List<OrderOfferPriceFailReason> orderOfferPriceFailReasonList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderPushes)) {
            orderPushes.forEach(orderPush -> {
                OrderOfferPriceEnd orderOfferPriceEnd = new OrderOfferPriceEnd();
                orderOfferPriceEnd.setMasterId(orderPush.getMasterId());
                orderOfferPriceEnd.setOrderId(orderPush.getOrderId());
                orderOfferPriceEnd.setPushTime(orderPush.getPushTime());
                orderOfferPriceEnd.setReasonType(offerPriceEndReasonType.code);
                orderOfferPriceEndList.add(orderOfferPriceEnd);

                if (AppointType.NORMAL.value.equals(orderPush.getAppointType())) {
                    OrderOfferPriceFailReason failReason = new OrderOfferPriceFailReason();
                    failReason.setOrderId(orderPush.getOrderId());
                    failReason.setReasonType(offerPriceEndReasonType.code);
                    orderOfferPriceFailReasonList.add(failReason);
                }
            });

            orderPushRepository.deleteByMasterIdAndOrderIds(provinceNextId, masterId, orderIds);
        }

        if (CollectionUtils.isNotEmpty(orderOfferPriceEndList) && CollectionUtils.isNotEmpty(orderOfferPriceFailReasonList)) {
            ClearPriceEndRqt clearPriceEndRqt = new ClearPriceEndRqt();
            clearPriceEndRqt.setOrderOfferPriceEndList(orderOfferPriceEndList);
            clearPriceEndRqt.setOrderOfferPriceFailReasonList(orderOfferPriceFailReasonList);
            commonOrderOfferService.clearOrderOfferPriceEndList(clearPriceEndRqt);
        }
    }

    @Override
    @Async
    public void clearOrderPush(Long provinceNextId, Long orderId, Long masterId) {
        Map<String, Long> orderPushClearMap = new HashMap<>();
        orderPushClearMap.put("orderId", orderId);
        orderPushClearMap.put("provinceNextId", provinceNextId);
        if (masterId != null && masterId > 0L) {
            orderPushClearMap.put("masterId", masterId);
        }
        messageSenderService.sendDelayMessage(TopicHelper.ORDER_PUSH_INTERNAL_NORMAL_BUSINESS_TOPIC, InternalProducerTagEnum.ORDER_PUSH_CLEAR.tag, JSON.toJSONString(orderPushClearMap), deleteDelay * 1000L);

    }

    /**
     * 分批软删除推单数据 - 订单
     *
     * @param orderId
     * @param appendNote
     * @return
     */
    @Override
    public int softDeleteOrderPush(List<Long> provinceNextId, Long orderId, String appendNote) {
        int count = 0;
        while (true) {
            int updateCount = orderPushRepository.softDeleteOrderPush(provinceNextId, orderId, appendNote, limitCount);
            count = count + updateCount;
            if (updateCount < limitCount) {
                break;
            }
        }
        return count;
    }

    @Override
    public int deleteOrderPushBatch(List<Long> provinceNextId, Long orderId) {
        int count = 0;
        while (true) {
            int deleteCount = orderPushRepository.deleteByOrderId(provinceNextId, orderId, directDeleteLimitCount);
            count = count + deleteCount;
            if (deleteCount < directDeleteLimitCount) {
                break;
            }
        }
        return count;
    }


    @Override
    public int deleteOrderPush(List<Long> provinceNextId, Long masterId,Long categoryId) {
        int count = 0;
        while (true) {
            int deleteCount = orderPushRepository.deleteByMasterIdAndCategoryId(provinceNextId, masterId,categoryId, directDeleteLimitCount);
            count = count + deleteCount;
            if (deleteCount < directDeleteLimitCount) {
                break;
            }
        }
        return count;
    }


    public int batchUpdateFullTimeExclusiveOrderFlag(List<Long> provinceNextId, Long masterId) {
        int count = 0;
        while (true) {
            int deleteCount = orderPushRepository.updateFullTimeExclusiveOrderFlag(provinceNextId, masterId, directDeleteLimitCount);
            count = count + deleteCount;
            if (deleteCount < directDeleteLimitCount) {
                break;
            }
        }
        return count;
    }

    @Override
    public int deleteOrderPushByOrderIdAndMasterIds(List<Long> provinceNextId, Long orderId, Set<Long> masterIds) {
        orderPushRepository.deleteByOrderIdAndMasterIds(provinceNextId, orderId, Lists.newArrayList(masterIds));
        return 1;
    }


    @Async
    @Override
    public void pushAsync(List<Long> provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, List<MasterAddressInfo> masterAddressInfoList,
                          Date pushTime, Integer pushDivisionLevel, Integer pushFrom,
                          String techniqueTypeIds, Integer agentOrderFlag,
                          Integer pushFlag, Integer accordingDistancePushFlag,
                          Integer exclusiveFlag, String masterSourceType) {
        handlerInsertOrderPush(provinceNextId, pushMold, pushOrderComposite, masterAddressInfoList, pushTime,
                pushDivisionLevel, pushFrom, techniqueTypeIds, agentOrderFlag, pushFlag,
                accordingDistancePushFlag, exclusiveFlag, masterSourceType);
    }


    @Override
    public void pushSync(List<Long> provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, List<MasterAddressInfo> masterAddressInfoList, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag, Integer pushFlag,
                         Integer accordingDistancePushFlag, Integer exclusiveFlag, String masterSourceType) {
        handlerInsertOrderPush(provinceNextId, pushMold, pushOrderComposite, masterAddressInfoList, pushTime,
                pushDivisionLevel, pushFrom, techniqueTypeIds, agentOrderFlag, pushFlag,
                accordingDistancePushFlag, exclusiveFlag, masterSourceType);
    }

    @Override
    public void pushOrderHandle(List<Long> provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, List<MasterAddressInfo> masterAddressInfoList, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag, Integer pushFlag,
                                Integer accordingDistancePushFlag, Integer exclusiveFlag, String masterSourceType) {

        Long orderId = pushOrderComposite.getOrderBaseComposite().getOrderBase().getOrderId();
        String lockKey = "pushOrderHandle:".concat(orderId.toString());
        long start = System.currentTimeMillis();
        String requestId = UUID.randomUUID().toString();
        boolean lockAcquired = false;
        while (!lockAcquired) {
            lockAcquired = redisHelper.tryGetDistributedLock(lockKey, requestId, distributeLockInsertOrderPushExpireTime);
            if (!lockAcquired) {
                log.error(String.format("pushOrderHandle acquired lock fail! order:%d", orderId));
                // 没获取到锁,等待一段时间后重试
                long currentTime = System.currentTimeMillis();
                if (currentTime - start > distributeLockInsertOrderPushExpireTime) {
                    log.error(String.format("pushOrderHandle acquired lock expire! order:%d", orderId));
                    //发飞书告警
                    FeiShuUtils.sendTempMsg("pushOrderHandle", "orderId:".concat(orderId.toString()), "pushOrderHandle acquired lock expire!");
                    // 获取锁超时
                    throw new RuntimeException("acquired lock expire!");
                }

                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
        // 获取锁成功
        try {
            //分批次写push表
            //分批次进行推单
            int size = masterAddressInfoList.size();
            int baseNum = insertOrderPushBatchSize;
            List<MasterAddressInfo> newMasterList;
            if (size > baseNum) {
                int mod = size % baseNum;
                int quotient = size / baseNum;
                if (mod > 0) {
                    quotient += 1;
                }
                for (int i = 0; i < quotient; i++) {
                    int formIndex = i * baseNum;
                    if (i == quotient - 1) {
                        int toIndex = 0;
                        if (mod == 0) {
                            toIndex = size;
                        } else {
                            toIndex = formIndex + mod;
                        }
                        newMasterList = masterAddressInfoList.subList(formIndex, toIndex);
                    } else {
                        newMasterList = masterAddressInfoList.subList(formIndex, formIndex + baseNum);
                    }
                    handlerInsertOrderPush(provinceNextId, pushMold, pushOrderComposite, newMasterList, pushTime,
                            pushDivisionLevel, pushFrom, techniqueTypeIds, agentOrderFlag, pushFlag,
                            accordingDistancePushFlag, exclusiveFlag, masterSourceType);
                }
            } else if (size > 0) {
                handlerInsertOrderPush(provinceNextId, pushMold, pushOrderComposite, masterAddressInfoList, pushTime,
                        pushDivisionLevel, pushFrom, techniqueTypeIds, agentOrderFlag, pushFlag,
                        accordingDistancePushFlag, exclusiveFlag, masterSourceType);
            }
        } finally {
            // 释放分布式锁
            redisHelper.releaseDistributedLock(lockKey, requestId);
        }


    }


    private void handlerInsertOrderPush(List<Long> provinceNextId, String pushMold,
                                        PushingOrderCompositeResp pushOrderComposite,
                                        List<MasterAddressInfo> masterAddressInfoList, Date pushTime,
                                        Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds,
                                        Integer agentOrderFlag, Integer pushFlag,
                                        Integer accordingDistancePushFlag, Integer exclusiveFlag,
                                        String masterSourceType) {
        if (!masterAddressInfoList.isEmpty()) {
            try {
                int efficaciousPushMasterNumber = this.insertOrderPushBatch(provinceNextId, pushMold, pushOrderComposite, masterAddressInfoList, pushTime,
                        pushDivisionLevel, pushFrom, techniqueTypeIds, agentOrderFlag, pushFlag, accordingDistancePushFlag, exclusiveFlag, masterSourceType);
                //更新次数
                OrderBase orderBase = pushOrderComposite.getOrderBaseComposite().getOrderBase();
                CompletableFuture.runAsync(() -> orderOfferGateway.updateOrderPushNumber(orderBase.getOrderId(), efficaciousPushMasterNumber), asyncConfigurer.getAsyncExecutor());
            } catch (BusException busException) {
                throw busException;
            } catch (Exception e) {
                CompletableFuture.runAsync(() -> retryInsertOrderPushBatch(provinceNextId, pushMold, pushOrderComposite, masterAddressInfoList,
                        pushTime, pushDivisionLevel, pushFrom, techniqueTypeIds, agentOrderFlag, pushFlag, accordingDistancePushFlag, exclusiveFlag, masterSourceType), asyncConfigurer.getAsyncExecutor());

            }
        }
    }


    /**
     * 新增或修改推单数据
     *
     * @param pushOrderComposite
     * @param masterAddressInfoList
     * @param pushTime
     * @param pushDivisionLevel
     * @param pushFrom
     * @param techniqueTypeIds
     * @param agentOrderFlag
     * @param pushFlag
     * @param accordingDistancePushFlag
     * @param exclusiveFlag
     * @return
     */
    private int insertOrderPushBatch(List<Long> provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite,
                                     List<MasterAddressInfo> masterAddressInfoList, Date pushTime, Integer pushDivisionLevel,
                                     Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag,
                                     Integer pushFlag, Integer accordingDistancePushFlag, Integer exclusiveFlag,
                                     String masterSourceType) {

        //调整师傅的经纬度数据结构(<masterId,MasterAddressInfo>)
        Map<Long, MasterAddressInfo> masterAddressInfoMap = masterAddressInfoList.stream()
                .collect(Collectors.toMap(MasterAddressInfo::getMasterId, masterAddressInfo -> DataUtils.copyObject(masterAddressInfo, MasterAddressInfo.class)));

        OrderBaseComposite orderBaseComposite = pushOrderComposite.getOrderBaseComposite();
        OrderBase orderBase = orderBaseComposite.getOrderBase();
        OrderLogisticsInfo orderLogisticsInfo = orderBaseComposite.getOrderLogisticsInfo();
        OrderExtraData orderExtraData = orderBaseComposite.getOrderExtraData();
        OrderGrab orderGrab = orderBaseComposite.getOrderGrab();
        List<OrderExclusiveTagResp> orderExclusiveTagRespList = pushOrderComposite.getOrderExclusiveTagRespList();
        int limitOffer = pushOrderComposite.getLimitOffer();
        Long orderId = orderBase.getOrderId();
        Long thirdDivisionId = orderBase.getThirdDivisionId();
        Long level1ServeId = Strings.isNullOrEmpty(orderBase.getServeLevel1Ids()) ? 0L : Long.parseLong(Arrays.asList(orderBase.getServeLevel1Ids().split(",")).get(0));
        List<OrderPush> orderPushList = orderPushRepository.selectByOrderIdsAndMasterId(provinceNextId, orderId, masterAddressInfoMap.keySet());
        boolean isMerchantInviteTag = false;
        boolean isExclusiveGoodOrder = false;
        if (CollectionUtils.isNotEmpty(orderExclusiveTagRespList)) {
            OrderExclusiveTagResp orderExclusiveTagResp = orderExclusiveTagRespList.stream().filter(it -> it.getTagType().equals("merchant_invite")).findFirst().orElse(null);
            isMerchantInviteTag = Objects.nonNull(orderExclusiveTagResp);

            OrderExclusiveTagResp exlusiveGoodOrderTagResp = orderExclusiveTagRespList.stream().filter(it -> it.getTagType().equals("exclusive_good_order")).findFirst().orElse(null);
            isExclusiveGoodOrder = Objects.nonNull(exlusiveGoodOrderTagResp);
        }

        boolean finalIsExclusiveGoodOrderTag = isExclusiveGoodOrder;
        Integer businessLineId = orderBase.getBusinessLineId();

        Integer offerNum = orderGrab.getOfferNumber();
        if (CollectionUtils.isNotEmpty(orderPushList)) {
            boolean finalIsMerchantInviteTag = isMerchantInviteTag;
            orderPushList.forEach(orderPush -> {
                OrderPush updateOrderPush = new OrderPush();
                updateOrderPush.setOrderId(orderPush.getOrderId());
                updateOrderPush.setMasterId(orderPush.getMasterId());
                //如果出现note 长度超长1000时，则默认截前
                String note = buildPushNode(orderPush.getNote(), pushTime);
                updateOrderPush.setNote(note);
                updateOrderPush.setPushTime(pushTime);
                updateOrderPush.setStopOfferTime(orderGrab.getEndDate());
                updateOrderPush.setPushDivisionLevel(pushDivisionLevel);
                updateOrderPush.setOrderDivisionId(thirdDivisionId);
                updateOrderPush.setIsDelete(0);
                updateOrderPush.setOfferNumber(0);
                updateOrderPush.setPushFrom(pushFrom);
                updateOrderPush.setAppointType(orderGrab.getAppointType());
                updateOrderPush.setAccountType(orderBase.getAccountType());
                updateOrderPush.setExclusiveFlag(exclusiveFlag);
                int emergencyOrderFlag = orderExtraData != null ? orderExtraData.getEmergencyOrderFlag() : 0;
                if (emergencyOrderFlag == 2) {
                    emergencyOrderFlag = 0;
                }
                updateOrderPush.setEmergencyOrderFlag(emergencyOrderFlag);
                updateOrderPush.setTechniqueTypeIds(techniqueTypeIds);
                updateOrderPush.setCategoryId(orderBase.getCategoryId());
                //推送距离(重新推送订单)
                MasterAddressInfo masterAddressInfoObj = masterAddressInfoMap.get(orderPush.getMasterId());
                if (Objects.nonNull(masterAddressInfoObj) && Objects.nonNull(masterAddressInfoObj.getPushDistance())
                        && !Objects.equals(masterAddressInfoObj.getPushDistanceType(), 0)) {
                    updateOrderPush.setPushDistance(masterAddressInfoObj.getPushDistance());
                    updateOrderPush.setPushDistanceType(masterAddressInfoObj.getPushDistanceType());
                } else {
                    updateOrderPush.setPushDistance(orderPush.getPushDistance());
                    updateOrderPush.setPushDistanceType(orderPush.getPushDistanceType());
                }
                updateOrderPush.setIsPullOrderDistance(0);
                updateOrderPush.setPushFlag(pushFlag);
                // 20231213迭代需求：重新推单需要回写订单标签
                if (orderPush.getMenuCategory() != 2 && orderPush.getMenuCategory() != 5 && orderPush.getMenuCategory() != 6 && orderPush.getMenuCategory() != 8) {
                    updateOrderPush.setMenuCategory(finalIsMerchantInviteTag && orderPush.getMenuCategory() == 1 ? 1 : super.isAppointCategory(orderPush, pushMold));
                }

                if(Objects.nonNull(masterAddressInfoObj) && Objects.nonNull(masterAddressInfoObj.getShuntFlag())
                        && masterAddressInfoObj.getShuntFlag() == 1 && orderPush.getMenuCategory() != 5){
                    updateOrderPush.setMenuCategory(8);
                }

                if("gold_medal_master".equals(pushMold)){
                    updateOrderPush.setMenuCategory(5);
                }


                if("technique_verify_master".equals(pushMold) || "technique_verify_master_dispatch".equals(pushMold)){
                    updateOrderPush.setMenuCategory(6);
                }


                if(finalIsExclusiveGoodOrderTag && Objects.nonNull(masterAddressInfoObj) && Objects.nonNull(masterAddressInfoObj.getMasterTimeType())
                        && masterAddressInfoObj.getMasterTimeType() == 1 && (Objects.isNull(pushFlag) || pushFlag == 0)){
                    orderPush.setExclusiveGoodOrderFlag(1);
                }else{
                    orderPush.setExclusiveGoodOrderFlag(0);
                }

                updateOrderPush.setAccordingDistancePushFlag(accordingDistancePushFlag);
                updateOrderPush.setLessContendFlag(0);
                //是否按照技能推单
                updateOrderPush.setAccordingTechnologyPushFlag(Optional.ofNullable(masterAddressInfoObj).map(MasterAddressInfo::getAccordingTechnologyPushFlag).orElse(1));
//                if ("afresh_new_model_single".equals(pushMold)) {
//                    //家庭样板城市推单推主力师傅
//                    updateOrderPush.setTmplCityFlag(3);
//                }
                this.setDistanceAndArriveStatus(updateOrderPush, orderLogisticsInfo);
                updateOrderPush.setMustOrderFlag((Objects.nonNull(masterAddressInfoObj) && Objects.nonNull(masterAddressInfoObj.getMustOrderFlag())) ? masterAddressInfoObj.getMustOrderFlag() : 0);
                updateOrderPush.setPushScore((Objects.nonNull(masterAddressInfoObj) && Objects.nonNull(masterAddressInfoObj.getScore())) ? masterAddressInfoObj.getScore() : BigDecimal.ZERO);
                updateOrderPush.setIsPullView(0);
                orderPushRepository.orderPushAnew(orderPush.getProvinceNextId(), updateOrderPush);
                //移除当前存在师傅id
                masterAddressInfoMap.remove(orderPush.getMasterId());
            });
        }

        if (!masterAddressInfoMap.isEmpty()) {
            //下单区域是否在限制报价区域
            List<OrderPush> orderPushListTarget = new ArrayList<>();
            //tips: order_push 表增加字段需要注意设置字段属性值
            int finalLimitOffer = limitOffer;
            masterAddressInfoMap.forEach((masterId, masterAddressInfo) -> {

                MasterAddressInfo masterAddressInfoObj = masterAddressInfoMap.get(masterId);
                OrderPush orderPush = new OrderPush();
                if (Objects.nonNull(masterAddressInfoObj)
                        && Objects.nonNull(masterAddressInfoObj.getCrossCityPush())
                        && masterAddressInfoObj.getCrossCityPush() == 1) {
                    //跨城推单
                    orderPush.setProvinceNextId(99999L);
                } else {
                    orderPush.setProvinceNextId(provinceNextId.get(0));
                }
                orderPush.setOrderId(orderBase.getOrderId());
                orderPush.setMasterId(masterId);
                orderPush.setAppointType(orderGrab.getAppointType());
                orderPush.setAccountType(orderBase.getAccountType());
                orderPush.setFromAccount(orderBase.getAccountId());
                orderPush.setOrderFrom(orderBase.getOrderFrom());
                orderPush.setLevel1ServeId(level1ServeId);
                orderPush.setOrderLabel(Objects.isNull(orderBase.getOrderLabel()) ? "" : orderBase.getOrderLabel());
                orderPush.setPushTime(pushTime);
                orderPush.setLimitOffer(finalLimitOffer);
                orderPush.setNote(DateUtil.format(pushTime, "yyyy-MM-dd HH:mm:ss") + ":添加新订单 ");
                orderPush.setPushDivisionLevel(pushDivisionLevel);
                orderPush.setAgentOrderFlag(agentOrderFlag);
                orderPush.setIsDelete(0);
                orderPush.setOfferNumber(0);
                orderPush.setEmergencyOrderFlag(0);
                orderPush.setStopOfferTime(orderGrab.getEndDate());
                orderPush.setOrderDivisionId(thirdDivisionId);
                orderPush.setIsIntention(0);
                orderPush.setMasterSourceType(masterSourceType);

                if (businessLineId != null && businessLineId == BusinessLineIdEnum.TWO.id) {
                    setOrderPushType(pushMold, orderPush);
                }

                if (!Strings.isNullOrEmpty(pushMold)) {
                    //家庭样板城市推单
                    if ("new_model_single".equals(pushMold) || "new_model_dispatch".equals(pushMold) || "afresh_new_model_single".equals(pushMold)) {
                        //家庭样板城市推单推主力师傅
                        orderPush.setTmplCityFlag(3);
                    } else if ("new_model_transfor_normal".equals(pushMold)) {
                        //家庭样板城市订单转推普通师傅
                        orderPush.setTmplCityFlag(2);
                    } else if ("new_model".equals(pushMold)) {
                        //家庭样板城市推单
                        orderPush.setTmplCityFlag(1);
                    } else {
                        orderPush.setTmplCityFlag(0);
                    }
                } else {
                    //非样板城市订单
                    orderPush.setTmplCityFlag(0);
                }

                if (Objects.nonNull(masterAddressInfoObj) && "reserve_master".equals(masterAddressInfoObj.getPushTag())) {
                    //家庭样板城市推单对储备师傅在首页会有弹窗列表，在推单时在缓存中给该师傅塞入样板城市标识，减少弹窗列表接口/orderPush/list/tmplCityOrderPushList的并发度
                    //当弹窗列表查出数据为空时会清空家庭样板城市推单标识
                    redisHelper.set(RedisKeyConstant.MASTER_TMPL_CITY_FLAG_KEY.concat(masterId.toString()), "1", 0);
                }

                if (Objects.nonNull(masterAddressInfoObj) && Objects.nonNull(masterAddressInfoObj.getPushDistance())
                        && !Objects.equals(masterAddressInfoObj.getPushDistanceType(), 0)) {
                    orderPush.setPushDistance(masterAddressInfoObj.getPushDistance());
                    orderPush.setPushDistanceType(masterAddressInfoObj.getPushDistanceType());
                } else {
                    orderPush.setPushDistance(0L);
                    orderPush.setPushDistanceType(0);
                }
                if (Objects.nonNull(masterAddressInfoObj.getMasterLongitude()) && Objects.nonNull(masterAddressInfoObj.getMasterLatitude())) {
                    orderPush.setMasterLongitude(masterAddressInfoObj.getMasterLongitude());
                    orderPush.setMasterLatitude(masterAddressInfoObj.getMasterLatitude());
                } else {
                    orderPush.setMasterLatitude(new BigDecimal(0));
                    orderPush.setMasterLongitude(new BigDecimal(0));
                }
                orderPush.setCreateTime(new Date());
                orderPush.setUpdateTime(new Date());
                orderPush.setPushFrom(pushFrom);
                orderPush.setExclusiveFlag(exclusiveFlag);
                int emergencyOrderFlag = orderExtraData != null ? orderExtraData.getEmergencyOrderFlag() : 0;
                if (emergencyOrderFlag == 2) {
                    emergencyOrderFlag = 0;
                }
                orderPush.setEmergencyOrderFlag(emergencyOrderFlag);
                orderPush.setTechniqueTypeIds(techniqueTypeIds);
                orderPush.setCategoryId(orderBase.getCategoryId());
                orderPush.setIsPullOrderDistance(0);
                orderPush.setPushFlag(pushFlag);
                if("gold_medal_master".equals(pushMold)){
                    orderPush.setMenuCategory(5);
                }else if("technique_verify_master".equals(pushMold) || "technique_verify_master_dispatch".equals(pushMold)){
                    orderPush.setMenuCategory(6);
                }else{
                    orderPush.setMenuCategory(super.isAppointCategory(orderPush, pushMold));
                }

                if(finalIsExclusiveGoodOrderTag && Objects.nonNull(masterAddressInfoObj) && Objects.nonNull(masterAddressInfoObj.getMasterTimeType()) &&
                        masterAddressInfoObj.getMasterTimeType() == 1 && (Objects.isNull(pushFlag) || pushFlag == 0)){
                    orderPush.setExclusiveGoodOrderFlag(1);
                }else{
                    orderPush.setExclusiveGoodOrderFlag(0);
                }

                orderPush.setAccordingDistancePushFlag(accordingDistancePushFlag);
                //是否按照技能推单
                orderPush.setAccordingTechnologyPushFlag(Optional.ofNullable(masterAddressInfoObj).map(MasterAddressInfo::getAccordingTechnologyPushFlag).orElse(1));
                orderPush.setLessContendFlag(0);
                orderPush.setScore(new BigDecimal(0));
                orderPush.setExposureScore(new BigDecimal(0));
                this.setDistanceAndArriveStatus(orderPush, orderLogisticsInfo);
                orderPush.setMustOrderFlag(Objects.nonNull(masterAddressInfo.getMustOrderFlag()) ? masterAddressInfo.getMustOrderFlag() : 0);
                orderPush.setPushScore(Objects.nonNull(masterAddressInfo.getScore()) ? masterAddressInfo.getScore() : BigDecimal.ZERO);
                orderPush.setIsPullView(0);
                if (Objects.nonNull(offerNum) && offerNum > 0) {
                    orderPush.setOffer(1);
                } else {
                    orderPush.setOffer(0);
                }

                if(Objects.nonNull(masterAddressInfoObj) && Objects.nonNull(masterAddressInfoObj.getShuntFlag()) && masterAddressInfoObj.getShuntFlag() == 1){
                    orderPush.setMenuCategory(8);
                }

                orderPushListTarget.add(orderPush);
            });
            orderPushRepository.insertOrderPushList(orderPushListTarget);
        }
        //返回实际推单师傅数
        return masterAddressInfoMap.size();
    }

    private void setOrderPushType(String pushMold, OrderPush orderPush) {
        String orderPushType = orderPush.getOrderPushType();
        switch (pushMold) {
            case "new_model_single":
            case "new_model_dispatch":
            case "afresh_new_model_single":
                if (StringUtils.isBlank(orderPushType)) {
                    orderPush.setOrderPushType(OrderPushTypeEnum.NEW_MODEL_CORE_MASTER_PUSH.getPushType());
                } else {
                    orderPush.setOrderPushType(orderPushType + "," + OrderPushTypeEnum.NEW_MODEL_CORE_MASTER_PUSH.getPushType());
                }
                break;
            case "new_model":
                orderPush.setOrderPushType(OrderPushTypeEnum.NEW_MODEL_RESERVE_MASTER_PUSH.getPushType());
                break;
            case "family_agreement_master":
                orderPush.setOrderPushType(OrderPushTypeEnum.FAMILY_AGREEMENT_MASTER_PUSH.getPushType());
                break;
            case "agent_master":
                orderPush.setOrderPushType(OrderPushTypeEnum.AGENT_PUSH.getPushType());
                break;
            default:
                orderPush.setOrderPushType(OrderPushTypeEnum.NORMAL_PUSH.getPushType());
                break;
        }
    }


    public void setDistanceAndArriveStatus(OrderPush updateOrderPush, OrderLogisticsInfo orderLogisticsInfo) {
        //订单的到货状态
        if (ObjectUtil.isNotNull(orderLogisticsInfo)) {
            if (ObjectUtil.isNotNull(orderLogisticsInfo.getArriveTime())) {
                updateOrderPush.setIsArrived(1);
            } else {
                updateOrderPush.setIsArrived(2);
            }
        } else {
            updateOrderPush.setIsArrived(1);
        }
    }

    private String buildPushNode(String note, Date pushTime) {
        if (note.length() <= 1500) {
            return note.concat(DateUtil.format(pushTime,"yyyy-MM-dd HH:mm:ss") + ":重新推送订单 ");
        }
        //固定覆盖前500字符串。
        String substring = note.substring(500);
        return "..." + substring.concat(DateUtil.format(pushTime, "yyyy-MM-dd HH:mm:ss") + ":重新推送订单 ");
    }


    private void retryInsertOrderPushBatch(List<Long> provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite,
                                           List<MasterAddressInfo> masterAddressInfoList, Date pushTime,
                                           Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag,
                                           Integer pushFlag, Integer accordingDistancePushFlag, Integer exclusiveFlag,
                                           String masterSourceType) {

        int retryNumber = 3;
        int alreadyNumber = 1;
        Exception exception = null;
        while (alreadyNumber <= retryNumber) {
            try {
                Thread.sleep(200 * retryNumber);
                Integer efficaciousPushMasterNumber = this.insertOrderPushBatch(provinceNextId, pushMold, pushOrderComposite, masterAddressInfoList, pushTime, pushDivisionLevel, pushFrom, techniqueTypeIds, agentOrderFlag,
                        pushFlag, accordingDistancePushFlag, exclusiveFlag, masterSourceType);
                //更新次数
                OrderBase orderBase = pushOrderComposite.getOrderBaseComposite().getOrderBase();
                orderOfferGateway.updateOrderPushNumber(orderBase.getOrderId(), efficaciousPushMasterNumber);
            } catch (BusException busException) {
                exception = busException;
                break;
            } catch (Exception e) {
                alreadyNumber++;
                exception = e;
                continue;
            }
            break;
        }
        OrderBaseComposite orderBaseComposite = pushOrderComposite.getOrderBaseComposite();
        if (alreadyNumber > retryNumber && exception != null) {
            FeiShuUtils.sendTempMsg("推单写push表操作失败(已重试 " + retryNumber + "次)", orderBaseComposite.getOrderBase(), exception);
            //抛出异常，还是由上游业务继续重试
            throw new BusException(exception.getMessage());
        }

    }

    @Override
    public void clearOrderPushByOrderIdV2(List<Long> provinceNextId, Long orderId, String appendNote, Boolean isNeedAsyncDelete) {
        //先删order_push
        deleteOrderPushExecutor.executeWithRetry(orderId, () -> this.deleteOrderPushBatch(provinceNextId, orderId));
    }

    @Override
    public void clearOrderPushByOrderIdForGrabDefinite(List<Long> provinceNextId, Long orderId) {
        //先删order_push
        deleteOrderPushExecutor.executeWithRetry(orderId, () -> this.deleteOrderPushBatch(provinceNextId, orderId));
    }



    @Override
    public void clearTechniqueVerifyOrderForGrabDefinite(List<Long> provinceNextId, Long masterId,Long categoryId) {
        //先删order_push
        //TODO
        deleteOrderPushExecutor.executeWithRetry(masterId, () -> this.deleteOrderPush(provinceNextId, masterId,categoryId));
    }



    @Override
    public void updateFullTimeExclusiveOrderFlag(List<Long> provinceNextId, Long masterId) {
        //先删order_push
        //TODO
        deleteOrderPushExecutor.executeWithRetry(masterId, () -> this.batchUpdateFullTimeExclusiveOrderFlag(provinceNextId, masterId));
    }

    @Override
    public void offerPrice(List<Long> provinceNextId, Long orderId, Long masterId, Date offerTime, String appendNote) {
        OrderPush orderPushCur = orderPushRepository.selectByOrderIdAndMasterId(provinceNextId, orderId, masterId);
        if (Objects.nonNull(orderPushCur)) {
            orderPushRepository.updateOfferTimeAndNote(provinceNextId, orderId, masterId, offerTime, orderPushCur.getNote() + DateUtils.getDateTime() + ":师傅报价 ");
        }
    }

    @Override
    public void cancelOfferPrice(List<Long> provinceNextId, Long orderId, Long masterId) {
        orderPushRepository.deleteByMasterIdAndOrderId(provinceNextId, masterId, orderId);
    }

    @Override
    public void lastOfferPrice(List<Long> provinceNextId, Long orderId, String appendNote) {
        if ("on".equals(lastOfferPriceDeleteSwitch)) {
            //先删order_push
            deleteOrderPushExecutor.executeWithRetry(orderId, () -> this.deleteOrderPushBatch(provinceNextId, orderId));
        } else {
            deleteOrderPushExecutor.executeWithRetry(orderId, () -> this.softDeleteOrderPush(provinceNextId, orderId, appendNote));
        }
    }

    @Override
    public void deleteInfoOrderBaseOrderPush(List<Long> provinceNextId, Long orderId, Long masterId) {
        orderPushRepository.deleteByMasterIdAndOrderId(provinceNextId, orderId, masterId);
    }

    @Override
    public void deleteInfoOrderPush(List<Long> provinceNextId, Long orderId, Date closeTime) {
        if (Objects.isNull(orderId)) {
            return;
        }
        List<OrderPush> orderPushes = orderPushRepository.selectByOrderId(provinceNextId, orderId);

        if (CollectionUtils.isEmpty(orderPushes)) {
            return;
        }
        List<Long> masterIds = orderPushes.stream().map(OrderPush::getMasterId).collect(Collectors.toList());

        //物理删除推单记录
        //先删order_push
        deleteOrderPushExecutor.executeWithRetry(orderId, () -> this.deleteOrderPushBatch(provinceNextId, orderId));


        List<InfoMasterOrderClose> infoMasterOrderCloseList = new ArrayList<>();
        InfoMasterOrderClose masterOrderClose = new InfoMasterOrderClose();
        masterOrderClose.setOrderId(orderId);
        masterOrderClose.setCloseTime(closeTime);

        masterIds.forEach(masterId -> {
                    //保存师傅订单关闭记录
                    masterOrderClose.setMasterId(masterId);
                    infoMasterOrderCloseList.add(masterOrderClose);
                }
        );

        if (CollectionUtils.isNotEmpty(infoMasterOrderCloseList)) {
            BatchInsertInfoMasterOrderCloseRqt batchInsertInfoMasterOrderCloseRqt = new BatchInsertInfoMasterOrderCloseRqt();
            batchInsertInfoMasterOrderCloseRqt.setInfoMasterOrderCloseList(infoMasterOrderCloseList);
            CompletableFuture.runAsync(() -> commonOrderOfferService.batchInsertInfoMasterOrderCloseInfo(batchInsertInfoMasterOrderCloseRqt), asyncConfigurer.getAsyncExecutor());
        }
    }


    @Override
    public void masterDisinterestOrder(List<Long> provinceNextId, Long orderId, Long masterId) {
        //先删除order_push
        orderPushRepository.deleteByMasterIdAndOrderId(provinceNextId, masterId, orderId);

    }
}
