package com.wanshifu.infrastructure;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/25 17:08
 */
@Data
public class SmartSortPositionConfig {

    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 城市id, 0:全国
     */
    @Column(name = "city_id")
    private Integer cityId;

    /**
     * 干预位置类型，1：固定位置，2：动态位置
     */
    @Column(name = "position_type")
    private Integer positionType;

    /**
     * 位置配置规则，json格式
     */
    @Column(name = "position_config_rule")
    private String positionConfigRule;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}
