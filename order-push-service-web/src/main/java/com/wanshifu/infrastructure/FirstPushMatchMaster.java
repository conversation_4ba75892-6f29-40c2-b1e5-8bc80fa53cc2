package com.wanshifu.infrastructure;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/8 20:35
 */
@Data
@Table(name = "first_push_match_master")
public class FirstPushMatchMaster {

    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 全局订单id
     */
    @Column(name = "global_order_id")
    private Long globalOrderId;

    /**
     * 匹配的师傅数量
     */
    @Column(name = "match_master_num")
    private Integer matchMasterNum;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
