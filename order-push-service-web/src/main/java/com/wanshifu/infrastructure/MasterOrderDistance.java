package com.wanshifu.infrastructure;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 订单距离表
 */
@Data
@ToString
@Table(name = "master_order_distance")
public class MasterOrderDistance {

    /**
     * PK,订单距离记录ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "order_distance_id")
    private Long orderDistanceId;

    /**
     * 订单ID
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 距离的订单ID
     */
    @Column(name = "distance_order_id")
    private Long distanceOrderId;

    /**
     * 师傅ID
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 订单服务ID(order_serve_info.order_serve_id)
     */
    @Column(name = "order_serve_id")
    private Long orderServeId;

    /**
     * 订单类目id
     */
    @Column(name = "category_id")
    private Integer categoryId;

    /**
     * 距离订单的服务状态,0:未服务,1:服务中,2:服务终止,3:服务完成
     */
    @Column(name = "distance_order_serve_status")
    private Integer distanceOrderServeStatus;

    /**
     * 订单地址距离(订单之间的距离),单位米
     */
    @Column(name = "order_address_distance")
    private Long orderAddressDistance;

    /**
     * 删除状态,1:删除,0:未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @Column(name = "note")
    private String note;

    /**
     * 距离记录创建时间
     */
    @Column(name = "distance_create_time")
    private Date distanceCreateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}