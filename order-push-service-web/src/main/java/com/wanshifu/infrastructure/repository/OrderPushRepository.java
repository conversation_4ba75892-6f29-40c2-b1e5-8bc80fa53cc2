package com.wanshifu.infrastructure.repository;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.wanshifu.domain.push.model.*;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.gatewayreq.push.MasterAppletListOrderPushGatewayRqt;
import com.wanshifu.infrastructure.mapper.OrderPushMapper;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.push.request.push.NoOfferByOrderIdRqt;
import com.wanshifu.order.push.request.push.TmplCityOrderPushRqt;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class OrderPushRepository extends BaseRepository<OrderPush> {

    @Resource
    private OrderPushMapper orderPushMapper;

    public Integer deleteByMasterIdAndOrderId(List<Long> provinceNextId, Long masterId, Long orderId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId).andEqualTo("masterId", masterId);
        criteria.andIn("provinceNextId", provinceNextId);
        return this.deleteByCondition(condition);
    }

    public void deleteByMasterIdAndOrderIds(List<Long> provinceNextId, Long masterId, List<Long> orderIds) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderId", orderIds)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        this.deleteByCondition(condition);
    }

    public void deleteByOrderIdAndMasterIds(List<Long> provinceNextId, Long orderId, List<Long> masterIds) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIds)
                .andIn("provinceNextId", provinceNextId);

        this.deleteByCondition(condition);
    }

    public int deleteByOrderIdAndLimit(List<Long> provinceNextId, Long orderId, Integer isDelete, Integer limit) {
        return orderPushMapper.deleteByOrderIdAndLimit(provinceNextId, orderId, isDelete, limit);
    }

    public int deleteByOrderId(List<Long> provinceNextId, Long orderId, Integer limit) {
        return orderPushMapper.deleteByOrderId(provinceNextId, orderId, limit);
    }


    public int deleteByMasterIdAndCategoryId(List<Long> provinceNextId, Long masterId,Long categoryId, Integer limit) {
        return orderPushMapper.deleteByMasterIdAndCategoryId(provinceNextId, masterId,categoryId, limit);
    }


    public int updateFullTimeExclusiveOrderFlag(List<Long> provinceNextId, Long masterId, Integer limit) {
        return orderPushMapper.updateFullTimeExclusiveOrderFlag(provinceNextId, masterId, limit);
    }

    public List<OrderPush> selectByOrderId(List<Long> provinceNextId, Long orderId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("isDelete", 0)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }

    /**
     * 更新推单记录的offerTime
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     * @param date 报价时间
     * @return
     */
    public Integer updateOfferTime(List<Long> provinceNextId, Long orderId, Long masterId, Date date) {
        OrderPush orderPush = new OrderPush();
        orderPush.setOfferTime(date);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId).andEqualTo("masterId", masterId);
        criteria.andIn("provinceNextId", provinceNextId);

        return this.updateByExampleSelective(orderPush, example);
    }

    public void updateOfferTimeAndNote(List<Long> provinceNextId, Long orderId, Long masterId, Date offerTime, String note) {
        OrderPush orderPush = new OrderPush();
        orderPush.setOfferTime(offerTime);
        orderPush.setIsDelete(1);
        orderPush.setNote(note);

        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        this.updateByConditionSelective(orderPush, condition);
    }

    public int updateIsArrivedByOrderId(List<Long> provinceNextId, Long orderId, int limit) {
        return orderPushMapper.updateIsArrivedByOrderId(provinceNextId, orderId, limit);
    }

    public int updateNotArrivedByOrderId(List<Long> provinceNextId, Long orderId, int limit) {
        return orderPushMapper.updateNotArrivedByOrderId(provinceNextId, orderId, limit);
    }

    /**
     * 更新推单记录的first_view_time
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     * @param date 当前时间
     * @return
     */
    public Integer updateFirstViewTimeByMasterOrderId(List<Long> provinceNextId, Long orderId, Long masterId, Date date) {
        OrderPush orderPush = new OrderPush();
        orderPush.setFirstViewTime(date);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId).andEqualTo("masterId", masterId).andIsNull("firstViewTime");
        criteria.andIn("provinceNextId", provinceNextId);

        return this.updateByExampleSelective(orderPush, example);
    }

    public Integer updatePullOrderDistanceByMasterOrderId(List<Long> provinceNextId, Long orderId, Long masterId, Integer isPullOrderDistance) {
        OrderPush orderPush = new OrderPush();
        orderPush.setIsPullOrderDistance(isPullOrderDistance);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        return this.updateByExampleSelective(orderPush, example);
    }


    public Integer updateByOrderIdAndMasters(List<Long> provinceNextId, Long orderId, List<Long> masterIdList, Date pushTime) {
        OrderPush infoOrderPush = new OrderPush();
        infoOrderPush.setPushTime(pushTime);
        infoOrderPush.setIsDelete(1);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIdList)
                .andIn("provinceNextId", provinceNextId);

        return this.updateByExampleSelective(infoOrderPush, example);
    }

    public Integer updateMasterAddressByOrderId(List<Long> provinceNextId, Long masterId, Long orderId,
                                                BigDecimal masterLongitude, BigDecimal masterLatitude,
                                                Long pushDistance, Integer pushDistanceType) {
        OrderPush orderPush = new OrderPush();
        orderPush.setMasterLongitude(masterLongitude);
        orderPush.setMasterLatitude(masterLatitude);
        orderPush.setPushDistance(pushDistance);
        if (Objects.nonNull(pushDistanceType)) {
            orderPush.setPushDistanceType(pushDistanceType);
        }
        orderPush.setUpdateTime(DateUtil.date());
        Example example = new Example(OrderPush.class);

        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        return this.updateByExampleSelective(orderPush, example);
    }

    public int softDeleteOrderPush(List<Long> provinceNextId, Long orderId, String appendNote, int limitCount) {
        return orderPushMapper.softDeleteOrderPush(provinceNextId, orderId, appendNote, limitCount);
    }

    public OrderPush selectByOrderIdAndMasterId(List<Long> provinceNextIds, Long orderId, Long masterId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextIds);

        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    public List<OrderPush> selectByMasterIdAndAppointType(List<Long> provinceNextIds, Long masterId, Integer appointType,Integer limitCount) {
        Condition condition = new Condition(OrderPush.class);
        condition.selectProperties("orderId");
        condition.selectProperties("pushDistance");

        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("masterId", masterId)
                .andEqualTo("appointType", appointType)
                .andIn("provinceNextId", provinceNextIds)
                .andIsNull("offerTime")
                .andGreaterThan("stopOfferTime", new Date())
                .andGreaterThan("pushDistance", 0L)
                .andEqualTo("isDelete", 0);

        condition.setOrderByClause("push_id DESC limit " + limitCount);


        return this.selectByCondition(condition);
    }

    public List<OrderPush> selectByMasterIdOrderIds(List<Long> provinceNextId, Long masterId, List<Long> orderIdIdList) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderId", orderIdIdList)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }


    public List<OrderPush> selectByOrderIdsAndMasterId(List<Long> provinceNextId, Long orderId, Set<Long> masterIdSet) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIdSet)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }

    public void orderPushAnew(Long provinceNextId, OrderPush orderPush) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderPush.getOrderId())
                .andEqualTo("masterId", orderPush.getMasterId())
                .andEqualTo("provinceNextId", provinceNextId);
        //写分表
        this.updateByConditionSelective(orderPush, condition);
    }

    public List<GetMasterOrderCategoryRateBo> selectMasterOrderCategoryNum(List<Long> provinceNextId, List<Long> masterIdList) {
        return orderPushMapper.selectMasterOrderCategoryNum(provinceNextId, masterIdList);
    }

    /**
     * 待报价列表v4(订单id查询)
     *
     * @param provinceNextIds 省下级地址id
     * @param masterId
     * @param orderIdIdList
     * @param tmplCityFlag 样板城市订单标识
     * @return
     */
    public List<OrderPush> selectByMasterIdAndOrderIdsFiler(List<Long> provinceNextIds, Long masterId,
                                                            List<Long> orderIdIdList, List<Integer> tmplCityFlag) {
        return orderPushMapper.selectByMasterIdAndOrderIdsFiler(provinceNextIds, masterId, orderIdIdList, new Date(), tmplCityFlag);
    }

    /**
     * 批量插入推单记录
     * @param orderPushList 推单记录
     */
    public void insertOrderPushList(List<OrderPush> orderPushList) {
        this.insertList(orderPushList);
    }

    /**
     * 更新推单记录菜单类别
     * @param provinceNextId
     * @param orderId
     * @param masterId
     * @param menuCategory
     * @return
     */
    public int updateMenuCategory(List<Long> provinceNextId, Long orderId, Long masterId, Integer menuCategory) {
        OrderPush orderPush = new OrderPush();
        orderPush.setMenuCategory(menuCategory);

        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextId);
        //写分表
        return this.updateByConditionSelective(orderPush, condition);
    }

    /**
     * 更新推单记录菜单类别
     * @param provinceNextId
     * @param orderId
     * @param masterIds
     * @param menuCategory
     * @return
     */
    public int updateMenuCategory(List<Long> provinceNextId, Long orderId, List<Long> masterIds, Integer menuCategory) {
        OrderPush orderPush = new OrderPush();
        orderPush.setMenuCategory(menuCategory);

        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIds)
                .andIn("provinceNextId", provinceNextId);
        //写分表
        return this.updateByConditionSelective(orderPush, condition);
    }

    public List<OrderPush> selectWaitOfferListV4(WaitOfferToV2 waitOfferTo) {
        if ("ltb_order_push_score".equals(waitOfferTo.getTableName())) {
            waitOfferTo.setTableName("ltb_order_push");
        }
        return orderPushMapper.selectWaitOfferListV4(waitOfferTo);

    }

    public List<OrderPush> selectSmartList(WaitOfferToV2 waitOfferTo) {
        return orderPushMapper.selectSmartList(waitOfferTo);

    }

    public List<OrderPush> listTmplCityOrderPush(TmplCityOrderPushRqt tmplCityOrderPushRqt) {
        return orderPushMapper.listTmplCityOrderPush(tmplCityOrderPushRqt);
    }

    public List<OrderPush> selectByMasterIdAndOrderIds(List<Long> provinceNextIds, Long masterId, List<Long> orderIdIdList) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderId", orderIdIdList)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextIds);
        return this.selectByCondition(condition);

    }

    public List<OrderPush> selectWaitOfferNoPageList(WaitOfferNoPageBo waitOfferNoPageBo) {
        return orderPushMapper.selectWaitOfferNoPageList(waitOfferNoPageBo);
    }

    /**
     * IOC(智能运营)活动待报价-筛选数据
     *
     * @param provinceNextIds 省下级地址id
     * @param masterId 师傅id
     * @param queryNumber 查询条数限制
     * @return
     */
    public List<OrderPush> selectIocWaitOfferFiler(List<Long> provinceNextIds, Long masterId, Integer queryNumber) {
        return orderPushMapper.selectIocWaitOfferFilter(provinceNextIds, masterId, queryNumber, new Date());
    }

    public List<Long> selectWaitOfferMasterIdsByOrderId(List<Long> provinceNextId, Long orderId) {
        return orderPushMapper.selectWaitOfferMasterIdsByOrderId(provinceNextId, orderId);
    }

    public List<Long> selectInviteMasterByOrderId(List<Long> provinceNextId, Long orderId) {
        return orderPushMapper.selectInviteMasterByOrderId(provinceNextId, orderId, new Date());
    }

    /**
     * 查询师傅待报价列表可筛选订单类目id集合
     *
     * @param provinceNextId   省下级地址id
     * @param masterId        师傅id
     * @param currentDateTime 当前时间
     * @return 师傅待报价列表可筛选订单类目id集合
     */
    public List<Long> selectMasterCategorySelector(List<Long> provinceNextId, Long masterId, DateTime currentDateTime) {
        List<Long> categoryIds = orderPushMapper.selectMasterCategorySelector(provinceNextId, masterId, currentDateTime);

        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }
        //新门-6,旧门-16,把16替换为6,然后去重,兼容家庭小程序下单还是旧门（16）的情况
        Collections.replaceAll(categoryIds, 16L, 6L);
        return categoryIds.stream().distinct().collect(Collectors.toList());
    }

    public List<OrderPush> selectByMasterId(List<Long> provinceNextId, Long masterId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("masterId", masterId)
                .andEqualTo("isDelete", 0)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }

    public List<OrderPush> selectByOrderIdsAndMasterId(List<Long> provinceNextId, Long orderId, List<Long> masterIdList) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId);
        criteria.andEqualTo("isDelete", 0);
        if (CollectionUtils.isNotEmpty(masterIdList)) {
            criteria.andIn("masterId", masterIdList);
        }
        criteria.andIn("provinceNextId", provinceNextId);
        return this.selectByCondition(condition);
    }

    /**
     * 查询订单已经查看师傅数量
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return 订单已经查看师傅数量
     */
    public Integer selectMasterViewNumber(List<Long> provinceNextId, Long orderId) {
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("isDelete", 0)
                .andIsNotNull("firstViewTime")
                .andIn("provinceNextId", provinceNextId);
        return this.selectCountByExample(example);
    }


    /**
     * 修改专属订单标签
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    public Integer updateOrderExclusiveFlagByOrderId(List<Long> provinceNextId, Long orderId, Integer exclusiveFlag) {
        OrderPush infoOrderPush = new OrderPush();
        infoOrderPush.setExclusiveFlag(exclusiveFlag);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId);
        criteria.andIn("provinceNextId", provinceNextId);
        //写分表
        return this.updateByExampleSelective(infoOrderPush, example);
    }

    public List<OrderPush> selectUserHireOrder(List<Long> provinceNextId, Long masterId, Date startTime, Date endTime) {
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("masterId", masterId);
        criteria.andBetween("pushTime", startTime, endTime);
        criteria.andEqualTo("appointType", 3);
        criteria.andEqualTo("orderFrom", OrderFrom.SITE.toString());
        criteria.andIsNull("offerTime");
        criteria.andEqualTo("isDelete", 0);
        criteria.andIn("provinceNextId", provinceNextId);
        return this.selectByExample(example);
    }

    /**
     * 更新竞争少标识
     */
    public int updateLssContendFlagByOrderIdAndMasterIdList(List<Long> provinceNextId, Long orderId, List<Long> masterIdList) {
        OrderPush updatePo = new OrderPush();
        updatePo.setLessContendFlag(1);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIdList)
                .andEqualTo("lessContendFlag", 0)
                .andIn("provinceNextId", provinceNextId);

        //写分表
        return this.updateByExampleSelective(updatePo, example);
    }

    /**
     * 更新样板城市推单弹窗读取时间
     * @param orderPush 推单数据
     * @param tmplCityTipTime 弹窗读取时间
     * @return
     */
    public void updateTmplCityTimeByOrderPush(OrderPush orderPush, Date tmplCityTipTime) {
        Long provinceNextId = orderPush.getProvinceNextId();
        OrderPush updatePo = new OrderPush();
        updatePo.setTmplCityTipTime(tmplCityTipTime);
        updatePo.setUpdateTime(tmplCityTipTime);

        Example example = new Example(OrderPush.class);
        example.createCriteria().andEqualTo("orderId", orderPush.getOrderId())
                .andEqualTo("masterId", orderPush.getMasterId())
                .andEqualTo("provinceNextId", provinceNextId);
        this.updateByExampleSelective(updatePo, example);
    }


    /**
     * TODO 目前这个接口走分析型数据库查询，不能采用默认数据源，否则会存在性能问题
     * @param analyticWaitOfferCountGateway
     * @return
     */
    public Integer analyticWaitOfferCount (WaitOfferCountGateway analyticWaitOfferCountGateway){
        return orderPushMapper.analyticWaitOfferCount(analyticWaitOfferCountGateway);
    }

    /**
     * 查询分表过期的推单记录
     *
     * @param tableName 分表表名
     * @param stopOfferTime 截止报价时间
     * @return
     */
    public List<Long> selectExpiredOrderPush(String tableName, Date stopOfferTime, int limit) {
        return orderPushMapper.selectExpiredOrderPush(tableName, stopOfferTime, limit);
    }

    /**
     * 删除分表过期的推单记录
     * @param tableName 分表表名
     * @param pushIds 分表主键id
     */
    public void deleteExpiredOrderPushByPushIds(String tableName, List<Long> pushIds) {
        orderPushMapper.deleteExpiredOrderPushByPushIds(tableName,pushIds);
    }


    /**
     * 获取师傅推单列表。特殊场景才使用，不然存在性能问题
     * @param provinceNextId 省下级地址id
     * @param masterId 师傅id
     * @return
     */
    public List<OrderPush> getMasterNormalList(List<Long> provinceNextId, Long masterId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        condition.selectProperties("orderId");
        condition.selectProperties("pushDivisionLevel");
        condition.setDistinct(true);

        criteria.andEqualTo("masterId", masterId)
                .andEqualTo("isDelete", 0)
                .andIn("provinceNextId", provinceNextId);

        return this.selectByCondition(condition);
    }

    public List<OrderPush> selectOrderPushForNotice(List<Long> provinceNextIds, Long orderId, List<Long> displayLongTailOrderLevel1ServeIds) {
        return orderPushMapper.selectOrderPushForNotice(orderId, provinceNextIds, displayLongTailOrderLevel1ServeIds);
    }


    public List<OrderPush> selectOrderPushByOrderId(List<Long> provinceNextId, Long orderId) {
        return orderPushMapper.selectOrderPushByOrderId(provinceNextId, orderId, new Date());

    }


    public List<OrderPush> selectMasterOrderPushByAccount(List<Long> provinceNextId, Long masterId,Long accountId,String accountType) {
        return orderPushMapper.selectMasterOrderPushByAccount(provinceNextId, masterId,accountId,accountType,DateUtil.date());

    }

    public OrderPush getOrderPushByOrderIdAndOffer(List<Long> provinceNextId, Long orderId, Integer offer) {

        return orderPushMapper.getOrderPushByOrderIdAndOffer(provinceNextId, orderId, offer);

    }

    public List<OrderPush> listByMasterDivisionIdForAppletUnLogin(MasterAppletListOrderPushGatewayRqt rqt) {
        String orderFrom = "tob".equals(rqt.getMasterSourceType()) ? OrderFrom.SITE.valueEn : "toc".equals(rqt.getMasterSourceType()) ? OrderFrom.APPLET.valueEn : null;
        return orderPushMapper.listByMasterDivisionIdForAppletUnLogin(rqt.getProvinceNextId(), rqt.getMasterDivisionId(), new Date(), DateUtil.offsetHour(new Date(), -6),orderFrom);
    }


    public Integer getOrderShowNumOfPeople(List<Long> provinceNextId, Long orderId) {
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("orderId", orderId);
        criteria.andIsNull("firstViewTime");
        criteria.andIn("provinceNextId", provinceNextId);

        return this.selectCountByExample(example);
    }

    public List<OrderPush> selectOrderPushNoOfferByOrderIdV2(NoOfferByOrderIdRqt rqt) {
        rqt.setCurrentDateTime(new Date());
        return orderPushMapper.selectOrderPushNoOfferByOrderId(rqt);
    }

    public List<OrderPush> selectOrderPushNoOfferByOrderId(List<Long> provinceNextId, Long orderId) {
        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andIsNull("offerTime")
                .andGreaterThan("stopOfferTime", new Date())
                .andEqualTo("isDelete", 0)
                .andIn("provinceNextId", provinceNextId);
        return this.selectByCondition(condition);
    }


    public Integer orderPushCount(List<Long> provinceNextId, Long orderId,Integer mode){
        return orderPushMapper.orderPushCount(provinceNextId,orderId,mode);
    }

    /**
     * 更新师傅拉取待报价列表订单时间
     * @param provinceNextIds 省下级地址id
     * @param orderIds 订单ids
     * @param masterId 师傅id
     */
    public void updateFirstPullTime(List<Long> provinceNextIds, List<Long> orderIds, Long masterId) {
        OrderPush orderPush = new OrderPush();
        orderPush.setFirstPullTime(new Date());

        Condition condition = new Condition(OrderPush.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andIn("orderId", orderIds)
                .andEqualTo("masterId", masterId)
                .andIn("provinceNextId", provinceNextIds);
        this.updateByConditionSelective(orderPush, condition);
    }

    public int updateMenuCategoryLimit(List<Long> provinceNextIds, Long orderId, int menuCategory, int limit) {

        return orderPushMapper.updateMenuCategoryByOrderIdAndLimit(provinceNextIds, orderId, menuCategory, limit, new Date());
    }

    public int updateMenuCategoryLimitV2(List<Long> provinceNextIds, Long orderId, int limit) {

        return orderPushMapper.updateMenuCategoryByOrderIdAndLimitV2(provinceNextIds, orderId, 0, limit, new Date());
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateOfferLimit(List<Long> provinceNextIds, Long orderId, int limit) {

        return orderPushMapper.updateOfferByOrderIdAndLimit(provinceNextIds, orderId, limit);
    }


    /**
     * 更新推单记录的first_view_time
     *
     * @param provinceNextId 省下级地址id
     * @param masterId 师傅id
     * @param date 当前时间
     * @return
     */
    public Integer updateIsPullViewByMasterOrderId(List<Long> provinceNextId, List<Long> orderIdList, Long masterId, Date date) {
        OrderPush orderPush = new OrderPush();
        orderPush.setIsPullView(1);
        Example example = new Example(OrderPush.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("orderId", orderIdList).andEqualTo("masterId", masterId).andEqualTo("isPullView",0);
        criteria.andIn("provinceNextId", provinceNextId);

        return this.updateByExampleSelective(orderPush, example);
    }

    public List<OrderPush> selectOrderPushList(List<Long> provinceNextId, Long orderId, Integer limitCount) {
        return orderPushMapper.selectOrderPushList(provinceNextId, orderId, limitCount);
    }


    public List<OrderPush> selectOrderPushListByPushScore(List<Long> provinceNextId, Long orderId) {
        return orderPushMapper.selectOrderPushListByPushScore(provinceNextId, orderId);
    }



    public List<OrderPush> selectFamilyOrderPushList(List<Long> provinceNextId, Long masterId, Integer limitCount) {
        return orderPushMapper.selectFamilyOrderPushList(provinceNextId, masterId, limitCount);
    }


    public List<OrderPush> selectPushMasterList(List<Long> provinceNextId, Long orderId,Integer distance) {
        return orderPushMapper.selectPushMasterList(provinceNextId, orderId,distance);
    }

    public List<OrderPush> selectSiteDetailNearbyMasterListAndLimit(List<Long> provinceNextId, Long orderId,Integer limitCount) {
        return orderPushMapper.selectSiteDetailNearbyMasterListAndLimit(provinceNextId, orderId, limitCount);
    }
}
