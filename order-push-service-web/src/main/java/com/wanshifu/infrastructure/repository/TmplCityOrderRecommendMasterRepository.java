package com.wanshifu.infrastructure.repository;

import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBo;
import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBoV2;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.TmplCityOrderRecommendMaster;
import com.wanshifu.infrastructure.mapper.TmplCityOrderRecommendMasterMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 14:55
 */
@Repository
public class TmplCityOrderRecommendMasterRepository extends BaseRepository<TmplCityOrderRecommendMaster> {

    @Resource
    private TmplCityOrderRecommendMasterMapper tmplCityOrderRecommendMasterMapper;

    /**
     * 家庭样板城市订单后台指派推荐师傅列表
     * @param listTmplCityOrderRecommendMasterRqtBo ListTmplCityOrderRecommendMasterRqtBo
     * @return List<TmplCityOrderRecommendMaster>
     */
    public List<TmplCityOrderRecommendMaster> listTmplCityOrderRecommendMaster(ListTmplCityOrderRecommendMasterRqtBo listTmplCityOrderRecommendMasterRqtBo) {
        return tmplCityOrderRecommendMasterMapper.listTmplCityOrderRecommendMaster(listTmplCityOrderRecommendMasterRqtBo);
    }

    /**
     * 家庭样板城市订单后台指派推荐师傅列表
     * @param listTmplCityOrderRecommendMasterRqtBo ListTmplCityOrderRecommendMasterRqtBoV2
     * @return List<TmplCityOrderRecommendMaster>
     */
    public List<TmplCityOrderRecommendMaster> listTmplCityOrderRecommendMasterV2(ListTmplCityOrderRecommendMasterRqtBoV2 listTmplCityOrderRecommendMasterRqtBo) {
        return tmplCityOrderRecommendMasterMapper.listTmplCityOrderRecommendMasterV2(listTmplCityOrderRecommendMasterRqtBo);
    }

    /**
     * 根据全局订单id删除推荐师傅数据
     * @param globalOrderTraceId 全局订单id
     */
    public void deleteByGlobalOrderTraceId(Long globalOrderTraceId) {
        Condition condition = new Condition(TmplCityOrderRecommendMaster.class);
        condition.createCriteria().andEqualTo("globalOrderTraceId", globalOrderTraceId);
        this.deleteByCondition(condition);
    }


    /**
     * 根据全局订单id删除推荐师傅数据
     * @param globalOrderTraceId 全局订单id
     */
    public void updateIsPush(Long globalOrderTraceId, List<Long> masterIdList) {
        TmplCityOrderRecommendMaster tmplCityOrderRecommendMaster = new TmplCityOrderRecommendMaster();
        tmplCityOrderRecommendMaster.setIsPush(1);
        Condition condition = new Condition(TmplCityOrderRecommendMaster.class);
        condition.createCriteria().andEqualTo("globalOrderTraceId", globalOrderTraceId).andIn("masterId",masterIdList);
        this.updateByConditionSelective(tmplCityOrderRecommendMaster,condition);
    }

}
