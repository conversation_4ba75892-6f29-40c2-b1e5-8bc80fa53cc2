package com.wanshifu.infrastructure.repository;

import cn.hutool.core.util.ObjectUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.OrderDistance;
import com.wanshifu.infrastructure.mapper.OrderDistanceMapper;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Repository
public class OrderDistanceRepository extends BaseRepository<OrderDistance> {

    @Resource
    private OrderDistanceMapper orderDistanceMapper;

    /**
     * 获取一条附近单记录
     * 验证存在
     *
     * @param orderId
     * @return
     */
    public OrderDistance selectOrderDistanceByOrderId(Long orderId) {
        Condition condition = new Condition(OrderDistance.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByExampleAndRowBounds(condition, new RowBounds(0, 1)));
    }


    /**
     * 根据订单id物理删除订单距离
     *
     * @param orderId
     * @param limitCount
     * @return
     */
    public Integer softDeleteOrderDistanceByOrderId(Long orderId, Integer limitCount) {
        if (ObjectUtil.isNotNull(orderId)) {
            Condition condition = new Condition(OrderDistance.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andEqualTo("orderId", orderId);
            condition.setOrderByClause("distance_id DESC limit " + limitCount);
            return this.deleteByCondition(condition);
        }
        return 0;
    }


    /**
     * 根据距离订单id物理删除订单距离
     *
     * @param distanceOrderId
     * @param limitCount
     * @return
     */
    public Integer softDeleteOrderDistanceByDistanceOrderId(Long distanceOrderId, Integer limitCount) {
        if (ObjectUtil.isNotNull(distanceOrderId)) {
            Condition condition = new Condition(OrderDistance.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andEqualTo("distanceOrderId", distanceOrderId);
            condition.setOrderByClause("distance_id DESC limit " + limitCount);
            return this.deleteByCondition(condition);
        }
        return 0;
    }

    /**
     * 批量查询附近单记录
     *
     * @param orderId
     * @return
     */
    public List<OrderDistance> selectByOrderIds(Long orderId) {
        Condition condition = new Condition(OrderDistance.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("isDelete", 0);
        condition.setOrderByClause("order_address_distance ASC");
        return this.selectByCondition(condition);
    }


    /**
     * 批量查询附近单记录
     *
     * @param orderIds
     * @return
     */
    public List<OrderDistance> selectByOrderIdList(List<Long> orderIds) {
        Condition condition = new Condition(OrderDistance.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("orderId", orderIds)
                .andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

    /**
     * 查询固定时间之前的附近单数据
     *
     * @param distanceCreateTime
     * @param limitCount
     * @return
     */
    public List<Long> selectByDistanceCreateTimeList(Date distanceCreateTime, Integer limitCount) {
        return orderDistanceMapper.selectByDistanceCreateTimeList(distanceCreateTime, limitCount);
    }

    /**
     * 根据主键id批量物理删除订单距离(附近单)
     *
     * @param distanceIdList
     * @return
     */
    public void deleteByDistanceOrderId(List<Long> distanceIdList) {
        if (CollectionUtils.isNotEmpty(distanceIdList)) {
            Condition condition = new Condition(OrderDistance.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andIn("distanceId", distanceIdList);
            this.deleteByCondition(condition);
        }
    }
}