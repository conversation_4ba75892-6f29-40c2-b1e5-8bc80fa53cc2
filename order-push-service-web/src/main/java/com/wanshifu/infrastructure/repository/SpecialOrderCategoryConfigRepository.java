package com.wanshifu.infrastructure.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.OrderDistanceConfig;
import com.wanshifu.infrastructure.SpecialOrderCategoryConfig;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class SpecialOrderCategoryConfigRepository extends BaseRepository<SpecialOrderCategoryConfig> {

    /**
     * 根据条件查询配置
     *
     * @param orderType
     * @param divisionId
     * @return
     */
    public SpecialOrderCategoryConfig selectByOrderTypeAndDivision(Integer orderType, Long divisionId) {
        Example example = new Example(SpecialOrderCategoryConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderType", orderType)
                .andEqualTo("secondDivisionId", divisionId)
                .andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }


}