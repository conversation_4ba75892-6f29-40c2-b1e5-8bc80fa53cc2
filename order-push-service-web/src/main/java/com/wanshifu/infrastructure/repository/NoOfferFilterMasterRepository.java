package com.wanshifu.infrastructure.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.infrastructure.NoOfferFilterMaster;
import com.wanshifu.infrastructure.mapper.NoOfferFilterMasterMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 14:02
 */
@Repository
public class NoOfferFilterMasterRepository extends BaseRepository<NoOfferFilterMaster> {

    @Resource
    private NoOfferFilterMasterMapper noOfferFilterMasterMapper;

    public void deleteByOrderId(Long orderId) {
        if (Objects.isNull(orderId) || orderId == 0L) {
            return;
        }
        Condition condition = new Condition(NoOfferFilterMaster.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId);
        this.deleteByCondition(condition);
    }

    public void deleteByOrderIdAndPutTagFlag(Long orderId, Integer putTagFlag) {
        if (Objects.isNull(orderId) || orderId == 0L) {
            return;
        }
        Condition condition = new Condition(NoOfferFilterMaster.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("putTagFlag", putTagFlag);
        this.deleteByCondition(condition);
    }

    public void deleteByGlobalOrderId(Long globalOrderId) {
        if (Objects.isNull(globalOrderId) || globalOrderId == 0L) {
            return;
        }
        Condition condition = new Condition(NoOfferFilterMaster.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("globalOrderId", globalOrderId);
        this.deleteByCondition(condition);
    }

    public void deleteByGlobalOrderIds(List<Long> globalOrderIds) {
        if (CollectionUtil.isEmpty(globalOrderIds)) {
            return;
        }
        Condition condition = new Condition(NoOfferFilterMaster.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andIn("globalOrderId", globalOrderIds);
        this.deleteByCondition(condition);
    }

    public void insertNoOfferFilterMasterList(List<NoOfferFilterMaster> masterList) {
        if (CollectionUtil.isEmpty(masterList)) {
            return;
        }
        this.insertList(masterList);
    }

    public List<NoOfferFilterMaster> selectByOrderId(Long orderId) {
        if (Objects.isNull(orderId) || orderId == 0L) {
            return null;
        }
        Condition condition = new Condition(NoOfferFilterMaster.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId);
        return this.selectByCondition(condition);
    }

    public List<NoOfferFilterMaster> selectByOrderIdAndPutFlagTag(Long orderId, Integer putFlagTag) {
        if (Objects.isNull(orderId) || orderId == 0L) {
            return null;
        }
        Condition condition = new Condition(NoOfferFilterMaster.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("putTagFlag", putFlagTag);
        return this.selectByCondition(condition);
    }

    public void updatePutFlagTagByOrderIdAndMasterIds(Long orderId, List<Long> masterIds, Integer putFlagTag) {
        if (Objects.isNull(orderId) || orderId == 0L) {
            return;
        }
        NoOfferFilterMaster noOfferFilterMaster = new NoOfferFilterMaster();
        noOfferFilterMaster.setUpdateTime(new Date());
        noOfferFilterMaster.setPutTagFlag(putFlagTag);

        Condition condition = new Condition(NoOfferFilterMaster.class);
        Example.Criteria criteria = condition.createCriteria();

        criteria.andEqualTo("orderId", orderId)
                .andIn("masterId", masterIds);

        updateByConditionSelective(noOfferFilterMaster, condition);
    }
}

