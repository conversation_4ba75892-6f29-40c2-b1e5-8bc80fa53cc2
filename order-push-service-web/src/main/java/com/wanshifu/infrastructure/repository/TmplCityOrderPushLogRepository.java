package com.wanshifu.infrastructure.repository;

import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderPushLogRqtBo;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.infrastructure.TmplCityOrderPushLog;
import com.wanshifu.infrastructure.mapper.TmplCityOrderPushLogMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/29 18:12
 */
@Repository
public class TmplCityOrderPushLogRepository extends BaseRepository<TmplCityOrderPushLog> {

    @Resource
    private TmplCityOrderPushLogMapper tmplCityOrderPushLogMapper;

    /**
     * 家庭样板城市订单后台指派推荐师傅列表
     * @param listTmplCityOrderPushLogRqtBo listTmplCityOrderPushLogRqtBo
     * @return List<TmplCityOrderPushLog>
     */
    public List<TmplCityOrderPushLog> listTmplCityOrderPushLog(ListTmplCityOrderPushLogRqtBo listTmplCityOrderPushLogRqtBo) {
        return tmplCityOrderPushLogMapper.listTmplCityOrderPushLog(listTmplCityOrderPushLogRqtBo);
    }
}
