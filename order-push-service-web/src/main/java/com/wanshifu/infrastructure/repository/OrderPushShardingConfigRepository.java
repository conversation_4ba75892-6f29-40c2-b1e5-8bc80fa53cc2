package com.wanshifu.infrastructure.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.infrastructure.OrderPushShardingConfig;
import com.wanshifu.infrastructure.mapper.OrderPushShardingConfigMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/1 17:24
 */
@Repository
public class OrderPushShardingConfigRepository extends BaseRepository<OrderPushShardingConfig> {

    @Resource
    private OrderPushShardingConfigMapper orderPushShardingConfigMapper;

    @Override
    public List<OrderPushShardingConfig> selectAll() {
        return orderPushShardingConfigMapper.selectAllConfigs();
    }
}
