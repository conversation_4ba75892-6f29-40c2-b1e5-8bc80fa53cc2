package com.wanshifu.infrastructure.repository;

import cn.hutool.core.util.ObjectUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.OrderDistanceConditionConfig;
import com.wanshifu.infrastructure.OrderDistanceConfig;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class OrderDistanceConfigRepository extends BaseRepository<OrderDistanceConfig> {

    /**
     * 根据条件查询配置
     *
     * @param orderType
     * @param divisionId
     * @return
     */
    public List<OrderDistanceConfig> selectByOrderTypeAndDivision(Integer orderType, Long divisionId) {
        Example example = new Example(OrderDistanceConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderType", orderType)
                .andEqualTo("secondDivisionId", divisionId)
                .andEqualTo("isDelete", 0);
        return this.selectByExample(example);
    }


}