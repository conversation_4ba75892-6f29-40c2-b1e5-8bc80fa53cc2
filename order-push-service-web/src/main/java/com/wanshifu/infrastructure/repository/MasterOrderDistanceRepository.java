package com.wanshifu.infrastructure.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.MasterOrderDistance;
import com.wanshifu.infrastructure.mapper.MasterOrderDistanceMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class MasterOrderDistanceRepository extends BaseRepository<MasterOrderDistance> {

    @Resource
    private MasterOrderDistanceMapper masterOrderDistanceMapper;

    /**
     * 统计报价订单与服务中订单符合距离要求数量
     *
     * @param orderId
     * @param masterId
     * @param categoryId
     * @return
     */
    public Integer selectCountByOrderIdAndMasterIdAndCategoryAndDistance(Long orderId, Long masterId, Integer categoryId, Long addressDistance) {
        Example example = new Example(MasterOrderDistance.class);
        Example.Criteria criteria = example.createCriteria();
        example.selectProperties("orderId");
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andEqualTo("categoryId", categoryId)
                .andEqualTo("distanceOrderServeStatus", 1)
                .andLessThan("orderAddressDistance", addressDistance)
                .andEqualTo("isDelete", 0);
        example.setOrderByClause("distance_create_time desc limit 10");
        return this.selectCountByExample(example);
    }

    /**
     * 统计报价订单与服务中订单符合距离要求记录
     *
     * @param orderId
     * @param masterId
     * @return
     */
    public List<MasterOrderDistance> selectListByOrderIdAndMasterIdAndCategoryAndDistance(Long orderId, Long masterId) {
        Example example = new Example(MasterOrderDistance.class);
        Example.Criteria criteria = example.createCriteria();
        example.selectProperties("orderId,distanceOrderId,orderServeId,orderAddressDistance,categoryId");
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("masterId", masterId)
                .andEqualTo("distanceOrderServeStatus", 1)
                .andEqualTo("isDelete", 0);
        example.setOrderByClause("order_distance_id desc limit 10");
        return this.selectByExample(example);
    }

    /**
     * 批量查询订单距离
     *
     * @param orderIds
     * @param masterId
     * @param distanceOrderIds
     * @return
     */
    public List<MasterOrderDistance> selectListByOrderIdsAndMasterId(List<Long> orderIds, Long masterId, List<Long> distanceOrderIds) {
        Example example = new Example(MasterOrderDistance.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("orderId", orderIds)
                .andIn("distanceOrderId", distanceOrderIds)
                .andEqualTo("masterId", masterId)
                .andEqualTo("distanceOrderServeStatus", 1);
        example.selectProperties("orderDistanceId,orderId,distanceOrderId,note,categoryId");
        return this.selectByExample(example);
    }

    /**
     * 更新订单距离信息
     *
     * @param orderDistanceId
     * @param distance
     */
    public void updateByPrimaryKeyAndDistance(Long orderDistanceId, Long distance, String note, Integer categoryId) {
        MasterOrderDistance orderDistance = new MasterOrderDistance();
        orderDistance.setOrderDistanceId(orderDistanceId);
        orderDistance.setOrderAddressDistance(distance);
        orderDistance.setCategoryId(categoryId);
        orderDistance.setIsDelete(0);
        orderDistance.setNote(note);
        orderDistance.setDistanceOrderServeStatus(1);
        this.updateByPrimaryKeySelective(orderDistance);
    }


    /**
     * 批量主键物理删除距离
     *
     * @param distanceIdList
     */
    public void deleteFromIdList(List<Long> distanceIdList) {
        if (CollectionUtils.isNotEmpty(distanceIdList)) {
            Condition condition = new Condition(MasterOrderDistance.class);
            Example.Criteria criteria = condition.createCriteria();
            criteria.andIn("orderDistanceId", distanceIdList);
            this.deleteByCondition(condition);
        }

    }

    /**
     * 查询固定时间之前的距离数据
     *
     * @param distanceCreateTime
     * @param limitCount
     * @return
     */
    public List<Long> selectByDistanceCreateTimeList(Date distanceCreateTime, Integer limitCount) {
        return masterOrderDistanceMapper.selectByDistanceCreateTimeList(distanceCreateTime, limitCount);
    }
}