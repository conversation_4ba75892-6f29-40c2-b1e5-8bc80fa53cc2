package com.wanshifu.infrastructure.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBo;
import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBoV2;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.infrastructure.InterfereOrderPush;
import com.wanshifu.infrastructure.NoOfferFilterMaster;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.TmplCityOrderRecommendMaster;
import com.wanshifu.infrastructure.mapper.InterfereOrderPushMapper;
import com.wanshifu.infrastructure.mapper.TmplCityOrderRecommendMasterMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 14:55
 */
@Repository
public class InterfereOrderPushRepository extends BaseRepository<InterfereOrderPush> {

    @Resource
    private InterfereOrderPushMapper interfereOrderPushMapper;


    public List<InterfereOrderPush> selectByGlobalOrderTraceId(Long globalOrderTraceId, Integer limitCount) {
        Condition condition = new Condition(InterfereOrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("globalOrderTraceId", globalOrderTraceId);
        condition.setOrderByClause("score DESC limit " + limitCount);
        return this.selectByCondition(condition);
    }


    public void deleteByGlobalOrderTraceId(Long globalOrderTraceId) {
        Condition condition = new Condition(InterfereOrderPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("globalOrderTraceId", globalOrderTraceId);
        this.deleteByCondition(condition);
    }


    public int deleteByGlobalOrderTraceIdBatch(Long globalOrderTraceId,Integer limitCount) {
        int count = 0;
        while (true) {
            int deleteCount = interfereOrderPushMapper.deleteByGlobalOrderTraceId(globalOrderTraceId,limitCount);
            count = count + deleteCount;
            if (deleteCount < limitCount) {
                break;
            }
        }
        return count;
    }

}
