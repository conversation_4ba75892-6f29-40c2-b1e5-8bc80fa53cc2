package com.wanshifu.infrastructure.repository;


import com.wanshifu.domain.base.tools.EnvUtil;
import com.wanshifu.domain.corn.model.MaxAndMinPrimaryIdBo;
import com.wanshifu.domain.log.model.QueryMessageReq;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.mapper.OrderMqLogMapper;
import com.wanshifu.infrastructure.OrderMqLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class OrderMqLogRepository extends BaseRepository<OrderMqLog> {

    @Autowired
    private OrderMqLogMapper orderMqLogMapper;



    /**
     * 查询主库
     * @param messageId
     * @param sourceType
     * @return
     */
    public List<OrderMqLog> selectMasterByMessageId (String messageId , String sourceType){
        if (StringUtils.isEmpty(messageId) || StringUtils.isEmpty(sourceType)) {
            return null;
        }
        return orderMqLogMapper.selectMasterByMessageId(messageId,sourceType);
    }



    /**
     * 分页查询消息
     * @param queryMessageReq
     * @return
     */
    public List<OrderMqLog> pageQueryMessage(QueryMessageReq queryMessageReq){
        return orderMqLogMapper.pageQueryMessage(queryMessageReq);
    }


    /**
     * 更新回放状态
     * @param offsetId
     * @param messageId
     * @param status
     */
    public void updatePlaybackStatus (Long offsetId,String messageId,String status) {
        OrderMqLog orderMqLog = new OrderMqLog();
        orderMqLog.setLogOffsetId(offsetId);
        String envName = EnvUtil.getEnvName();
        orderMqLog.setRemark(String.format("重发时间:【%s】环境:【%s】消息回放状态:【%s】 messageId:【%s】", DateUtils.formatDateTime(new Date()),envName,status,messageId));
        this.updateByPrimaryKeySelective(orderMqLog);
    }

    /**
     * 查询 最大id和最小id,以及最大条数
     * @return
     */
    public MaxAndMinPrimaryIdBo selectMaxAndMinPrimaryId (){
        return orderMqLogMapper.selectMaxAndMinPrimaryId();
    }

    public void deleteByStartAndEndOfferId(Long startOffsetId,Long endOffsetId){
        orderMqLogMapper.deleteByStartAndEndOfferId(startOffsetId,endOffsetId);
    }
}