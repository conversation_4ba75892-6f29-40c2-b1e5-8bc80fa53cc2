package com.wanshifu.infrastructure.repository;

import cn.hutool.core.util.ObjectUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.OrderDistanceConditionConfig;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class OrderDistanceConditionConfigRepository extends BaseRepository<OrderDistanceConditionConfig> {

    /**
     * 根据条件查询配置
     *
     * @param distanceType
     * @param categoryId
     * @param divisionId
     * @return
     */
    public OrderDistanceConditionConfig selectByDistanceTypeAndCategoryAndDivision(Integer orderType, Integer distanceType, Integer categoryId, Long divisionId) {
        Example example = new Example(OrderDistanceConditionConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("distanceType", distanceType)
                .andEqualTo("orderType", orderType)
                .andEqualTo("categoryId", categoryId)
                .andEqualTo("secondDivisionId", divisionId)
                .andEqualTo("isDelete", 0);
        return CollectionUtils.getFirstSafety(this.selectByExampleAndRowBounds(example, new RowBounds(0, 1)));
    }

    /**
     * 根据条件查询配置列表
     *
     * @param distanceTypes
     * @param categoryId
     * @param divisionId
     * @return
     */
    public List<OrderDistanceConditionConfig> selectByDistanceTypeAndCategoryAndDivisionList(Integer orderType, List<Integer> distanceTypes, Integer categoryId, Long divisionId) {
        Example example = new Example(OrderDistanceConditionConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("distanceType", distanceTypes)
                .andEqualTo("orderType", orderType)
                .andEqualTo("secondDivisionId", divisionId)
                .andEqualTo("isDelete", 0);

        if (ObjectUtil.isNotNull(categoryId)) {
            criteria.andEqualTo("categoryId", categoryId);
        }
        return this.selectByExample(example);
    }

    /**
     * 查询是否存在订单配置
     *
     * @param orderType
     * @param categoryId
     * @param divisionId
     * @return
     */
    public OrderDistanceConditionConfig selectByOrderDistanceTypeAndCategoryAndDivision(Integer orderType, Integer categoryId, Long divisionId) {
        Example example = new Example(OrderDistanceConditionConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("distanceType", 1)
                .andEqualTo("orderType", orderType)
                .andEqualTo("secondDivisionId", divisionId)
                .andEqualTo("isDelete", 0);

        if (ObjectUtil.isNotNull(categoryId)) {
            criteria.andEqualTo("categoryId", categoryId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExampleAndRowBounds(example, new RowBounds(0, 1)));
    }

    public List<OrderDistanceConditionConfig> selectConditionConfigByList(Integer orderType, Integer distanceType, List<Integer> categoryIds, Long divisionId) {
        Example example = new Example(OrderDistanceConditionConfig.class);
        Example.Criteria criteria = example.createCriteria();
        example.selectProperties("orderType,distanceType,categoryId,addressDistance");
        criteria.andEqualTo("orderType", orderType)
                .andEqualTo("secondDivisionId", divisionId)
                .andEqualTo("isDelete", 0);

        if (ObjectUtil.isNotNull(categoryIds)) {
            criteria.andIn("categoryId", categoryIds);
        }
        if (ObjectUtil.isNotNull(distanceType)) {
            criteria.andEqualTo("distanceType", distanceType);
        }
        return this.selectByExample(example);
    }

}