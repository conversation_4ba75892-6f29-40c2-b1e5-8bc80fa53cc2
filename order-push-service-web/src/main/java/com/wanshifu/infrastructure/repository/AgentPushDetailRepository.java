package com.wanshifu.infrastructure.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.AgentPushDetail;
import com.wanshifu.infrastructure.mapper.AgentPushDetailMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public class AgentPushDetailRepository extends BaseRepository<AgentPushDetail> {

    @Resource
    private AgentPushDetailMapper agentPushDetailMapper;

    public void addAgentPushDetail(Long agentId, Long orderId, Set<Long> masterIdList, Integer nobodyOfferHour, Date pushTime) {
        List<AgentPushDetail> detailList = new ArrayList<>();
        masterIdList.forEach(masterId -> {
            AgentPushDetail detail = new AgentPushDetail();
            detail.setAgentId(agentId);
            detail.setOrderId(orderId);
            detail.setMasterId(masterId);
            detail.setNobodyOfferHour(nobodyOfferHour);
            detail.setPushTime(pushTime);
            detail.setCreateTime(new Date());
            detail.setUpdateTime(new Date());
            detailList.add(detail);
        });
        agentPushDetailMapper.insertList(detailList);


    }

    public List<AgentPushDetail> selectByOrderIdAndMasterId(Long orderId,Long masterId) {
        Example example = new Example(AgentPushDetail.class);
        example.createCriteria()
                .andEqualTo("orderId",orderId)
                .andEqualTo("masterId",masterId);
        return selectByExample(example);
    }

    public List<AgentPushDetail> selectListByOrderId(Long orderId){
        Example example = new Example(AgentPushDetail.class);
        example.createCriteria().andEqualTo("orderId",orderId);
        return selectByExample(example);
    }

    public int updateAbandonTimeByOrderId(Long orderId,Date abandonTime){
        return agentPushDetailMapper.updateAbandonTimeByOrderId(orderId,abandonTime);
    }


    public int updateSecondPushTimeByOrderId(Long orderId,Date secondPushTime){
        return agentPushDetailMapper.updateSecondPushTimeByOrderId(orderId,secondPushTime);
    }

    public int branchUpdateAgentPushDetail(List<Long> ids,
                                           Integer nobodyOfferHour,
                                           Date pushTime){
        if (CollectionUtils.isEmpty(ids)) {
            return 0 ;
        }

        Example example = new Example(AgentPushDetail.class);
        example.createCriteria().andIn("id",ids);

        AgentPushDetail agentPushDetail = new AgentPushDetail();
        agentPushDetail.setPushTime(pushTime);
        agentPushDetail.setNobodyOfferHour(nobodyOfferHour);
        agentPushDetail.setUpdateTime(new Date());
        return updateByExampleSelective(agentPushDetail,example);
    }


}