package com.wanshifu.infrastructure.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.FirstPushMatchMaster;
import com.wanshifu.infrastructure.mapper.FirstPushMatchMasterMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/8 20:46
 */
@Repository
public class FirstPushMatchMasterRepository extends BaseRepository<FirstPushMatchMaster>  {

    @Resource
    private FirstPushMatchMasterMapper firstPushMatchMasterMapper;

    public void insertFirstPushMatchMaster(FirstPushMatchMaster firstPushMatchMaster) {
        this.firstPushMatchMasterMapper.insert(firstPushMatchMaster);
    }

    public FirstPushMatchMaster selectFirstPushMasterByGlobalOrderId(Long globalOrderId) {
        Condition condition = new Condition(FirstPushMatchMaster.class);
        condition.createCriteria().andEqualTo("globalOrderId", globalOrderId);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));

    }
}
