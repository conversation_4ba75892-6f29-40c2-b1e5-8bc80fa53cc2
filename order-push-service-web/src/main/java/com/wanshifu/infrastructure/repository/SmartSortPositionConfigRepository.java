package com.wanshifu.infrastructure.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.infrastructure.SmartSortPositionConfig;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/25 18:22
 */
@Repository
public class SmartSortPositionConfigRepository extends BaseRepository<SmartSortPositionConfig> {


    public void deleteByCityIds(List<Integer> cityIds) {
        if (CollectionUtil.isEmpty(cityIds)) {
            return;
        }
        Condition condition = new Condition(SmartSortPositionConfig.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("cityId", cityIds);
        this.deleteByCondition(condition);
    }

    public void insertSmartSortPositionConfigList(List<SmartSortPositionConfig> configList) {
        if (CollectionUtil.isEmpty(configList)) {
            return;
        }
        this.insertList(configList);
    }

    public SmartSortPositionConfig selectByCityId(Integer cityId) {
        SmartSortPositionConfig config = new SmartSortPositionConfig();
        config.setCityId(cityId);

        return this.selectOne(config);
    }

}
