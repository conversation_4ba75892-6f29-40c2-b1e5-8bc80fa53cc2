package com.wanshifu.infrastructure.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.infrastructure.AgentPushDetail;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface AgentPushDetailMapper extends IBaseCommMapper<AgentPushDetail> {

    int updateAbandonTimeByOrderId(@Param("orderId")Long orderId,@Param("abandonTime")Date abandonTime);

    int updateSecondPushTimeByOrderId(@Param("orderId")Long orderId,@Param("secondPushTime")Date secondPushTime);

}