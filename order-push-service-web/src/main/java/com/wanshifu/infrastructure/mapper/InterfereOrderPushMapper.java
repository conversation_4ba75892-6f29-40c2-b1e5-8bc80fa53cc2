package com.wanshifu.infrastructure.mapper;

import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBo;
import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBoV2;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.infrastructure.InterfereOrderPush;
import com.wanshifu.infrastructure.TmplCityOrderRecommendMaster;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 14:52
 */
public interface InterfereOrderPushMapper extends IBaseCommMapper<InterfereOrderPush> {


    int deleteByGlobalOrderTraceId( @Param(value = "globalOrderTraceId") Long globalOrderTraceId, @Param(value = "limit") int limit);

}
