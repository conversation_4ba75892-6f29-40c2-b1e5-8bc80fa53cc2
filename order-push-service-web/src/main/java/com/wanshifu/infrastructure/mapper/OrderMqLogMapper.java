package com.wanshifu.infrastructure.mapper;

import com.wanshifu.domain.corn.model.MaxAndMinPrimaryIdBo;
import com.wanshifu.domain.log.model.QueryMessageReq;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.infrastructure.OrderMqLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderMqLogMapper extends IBaseCommMapper<OrderMqLog> {


    /**
     * 查询主库通过消息id
     * @param messageId
     * @param sourceType
     * @return
     */
    List<OrderMqLog> selectMasterByMessageId ( @Param(value = "messageId") String messageId ,
                                               @Param(value = "sourceType") String sourceType);


    /**
     * 分页查询消息
     * @param queryMessageReq
     * @return
     */
    public List<OrderMqLog> pageQueryMessage( @Param(value = "param") QueryMessageReq queryMessageReq);

    /**
     * 查询最大和最小主键id
     * @return
     */
    MaxAndMinPrimaryIdBo selectMaxAndMinPrimaryId();


    void deleteByStartAndEndOfferId(@Param(value = "startOffsetId") Long startOffsetId,
                                    @Param(value = "endOffsetId") Long endOffsetId);
}