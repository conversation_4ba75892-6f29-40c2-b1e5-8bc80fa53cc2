package com.wanshifu.infrastructure.mapper;

import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBo;
import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBoV2;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.infrastructure.TmplCityOrderRecommendMaster;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 14:52
 */
public interface TmplCityOrderRecommendMasterMapper extends IBaseCommMapper<TmplCityOrderRecommendMaster> {

    /**
     * 家庭样板城市订单后台指派推荐师傅列表
     * @param listTmplCityOrderRecommendMasterRqtBo ListTmplCityOrderRecommendMasterRqtBo
     * @return List<TmplCityOrderRecommendMaster>
     */
    List<TmplCityOrderRecommendMaster> listTmplCityOrderRecommendMaster(ListTmplCityOrderRecommendMasterRqtBo listTmplCityOrderRecommendMasterRqtBo);

    /**
     * 家庭样板城市订单后台指派推荐师傅列表
     * @param listTmplCityOrderRecommendMasterRqtBo ListTmplCityOrderRecommendMasterRqtBoV2
     * @return List<TmplCityOrderRecommendMaster>
     */
    List<TmplCityOrderRecommendMaster> listTmplCityOrderRecommendMasterV2(ListTmplCityOrderRecommendMasterRqtBoV2 listTmplCityOrderRecommendMasterRqtBo);

}
