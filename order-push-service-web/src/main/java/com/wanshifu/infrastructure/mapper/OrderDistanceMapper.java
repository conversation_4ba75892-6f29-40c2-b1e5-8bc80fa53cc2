package com.wanshifu.infrastructure.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.infrastructure.OrderDistance;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OrderDistanceMapper extends IBaseCommMapper<OrderDistance> {

    List<Long> selectByDistanceCreateTimeList(@Param("distanceCreateTime") Date distanceCreateTime, @Param("limitCount") Integer limitCount);
}