package com.wanshifu.infrastructure.mapper;

import com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderPushLogRqtBo;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.infrastructure.TmplCityOrderPushLog;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29 18:04
 */
public interface TmplCityOrderPushLogMapper extends IBaseCommMapper<TmplCityOrderPushLog> {

    /**
     * 家庭样板城市订单后台指派推荐师傅列表
     * @param listTmplCityOrderPushLogRqtBo listTmplCityOrderPushLogRqtBo
     * @return List<TmplCityOrderPushLog>
     */
    List<TmplCityOrderPushLog> listTmplCityOrderPushLog(ListTmplCityOrderPushLogRqtBo listTmplCityOrderPushLogRqtBo);
}
