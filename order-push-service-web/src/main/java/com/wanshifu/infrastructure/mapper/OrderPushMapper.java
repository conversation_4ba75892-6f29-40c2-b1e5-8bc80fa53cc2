package com.wanshifu.infrastructure.mapper;

import cn.hutool.core.date.DateTime;
import com.wanshifu.domain.push.model.GetMasterOrderCategoryRateBo;
import com.wanshifu.domain.push.model.WaitOfferCountGateway;
import com.wanshifu.domain.push.model.WaitOfferNoPageBo;
import com.wanshifu.domain.push.model.WaitOfferToV2;
import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.order.push.request.push.NoOfferByOrderIdRqt;
import com.wanshifu.order.push.request.push.TmplCityOrderPushRqt;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OrderPushMapper extends IBaseCommMapper<OrderPush> {
    int softDeleteOrderPush(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId, @Param(value = "appendNote") String appendNote, @Param(value = "limitCount") int limitCount);
    List<GetMasterOrderCategoryRateBo> selectMasterOrderCategoryNum(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("masterIdList") List<Long> masterIdList);

    List<OrderPush> selectByMasterIdAndOrderIdsFiler(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("masterId") Long masterId, @Param("orderIdIdList") List<Long> orderIdIdList, @Param("currentDate") Date currentDate, @Param("tmplCityFlag") List<Integer> tmplCityFlag);
    List<OrderPush> selectWaitOfferListV4(WaitOfferToV2 waitOfferTo);
    List<OrderPush> selectSmartList(WaitOfferToV2 waitOfferTo);

    /**
     * 家庭样板城市推单列表
     * @param tmplCityOrderPushRqt
     * @return
     */
    List<OrderPush> listTmplCityOrderPush(TmplCityOrderPushRqt tmplCityOrderPushRqt);

    List<OrderPush> selectOrderPushNoOfferByOrderId(NoOfferByOrderIdRqt noOfferByOrderIdRqt);
    //todo 只支持abd查询
    int analyticWaitOfferCount(WaitOfferCountGateway waitOfferCountGateway);

    List<Long> selectExpiredOrderPush(@Param(value = "tableName") String tableName, @Param(value = "stopOfferTime") Date stopOfferTime, @Param(value = "limit") int limit);
    List<OrderPush> selectWaitOfferNoPageList(WaitOfferNoPageBo waitOfferNoPageBo);
    List<OrderPush> selectIocWaitOfferFilter(@Param(value = "provinceNextId") List<Long> provinceNextIds, @Param("masterId") Long masterId, @Param(value = "queryNumber") int queryNumber, @Param("currentDate") Date currentDate);
    List<Long> selectWaitOfferMasterIdsByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("orderId") Long orderId);

    List<Long> selectInviteMasterByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("orderId") Long orderId, @Param(value = "currentDateTime") Date currentDateTime);
    List<Long> selectMasterCategorySelector(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("masterId") Long masterId, @Param("currentDateTime") DateTime currentDateTime);

    List<OrderPush> selectOrderPushForNotice(@Param("orderId") Long orderId, @Param("provinceNextId") List<Long> provinceNextIds, @Param("displayLongTailOrderLevel1ServeIds") List<Long> displayLongTailOrderLevel1ServeIds);

    List<OrderPush> selectOrderPushByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("orderId") Long orderId, @Param(value = "currentDateTime") Date currentDateTime);


    List<OrderPush> selectMasterOrderPushByAccount(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("masterId") Long masterId,
                                                   @Param(value = "accountId") Long accountId, @Param(value = "accountType") String accountType,
                                                   @Param("currentDateTime") DateTime currentDateTime);

    OrderPush getOrderPushByOrderIdAndOffer(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param("orderId") Long orderId,
                                            @Param(value = "offer") Integer offer);

    /**
     * 师傅小程序游客模式获取推单数据
     *
     * @param masterDivisionId masterDivisionId
     * @param stopOfferTime    stopOfferTime
     * @param pushTime         pushTime
     * @return List<OrderPush>
     */
    List<OrderPush> listByMasterDivisionIdForAppletUnLogin(@Param(value = "provinceNextId") List<Long> provinceNextId,

                                                           @Param("masterDivisionId") Long masterDivisionId, @Param("stopOfferTime") Date stopOfferTime,
                                                           @Param("pushTime") Date pushTime,@Param("orderFrom")String orderFrom);

    int deleteByOrderIdAndLimit(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId, @Param(value = "isDelete") Integer isDelete, @Param(value = "limit") Integer limit);

    int deleteByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId, @Param(value = "limit") int limit);

    int deleteByMasterIdAndCategoryId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "masterId") Long masterId,@Param(value = "categoryId") Long categoryId, @Param(value = "limit") int limit);

    void deleteExpiredOrderPushByPushIds(@Param(value = "tableName") String tableName, @Param(value = "pushIds") List<Long> pushIds);

    /**
     * 分批更新menuCategory
     * @param provinceNextId
     * @param menuCategory
     * @param orderId
     * @param limit
     * @param currentDateTime
     * @return
     */
    int updateMenuCategoryByOrderIdAndLimit(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId,
                                            @Param(value = "menuCategory") int menuCategory, @Param(value = "limit") int limit, @Param(value = "currentDateTime") Date currentDateTime);

    /**
     * 分批更新menuCategory
     * @param provinceNextId
     * @param menuCategory
     * @param orderId
     * @param limit
     * @param currentDateTime
     * @return
     */
    int updateMenuCategoryByOrderIdAndLimitV2(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId,
                                            @Param(value = "menuCategory") int menuCategory, @Param(value = "limit") int limit, @Param(value = "currentDateTime") Date currentDateTime);


    /**
     * 分批更新已到货
     * @param provinceNextId
     * @param orderId
     * @param limit
     * @return
     */
    int updateIsArrivedByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId,
                                              @Param(value = "limit") int limit);

    /**
     * 分批更新已到货
     * @param provinceNextId
     * @param orderId
     * @param limit
     * @return
     */
    int updateNotArrivedByOrderId(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId,
                                 @Param(value = "limit") int limit);

    /**
     * 分批更新offer
     * @param provinceNextId
     * @param orderId
     * @param offer
     * @param limit
     * @return
     */
    int updateOfferByOrderIdAndLimit(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "orderId") Long orderId, @Param(value = "limit") int limit);


    List<OrderPush> selectOrderPushList(@Param(value = "provinceNextId") List<Long> provinceNextId,@Param(value = "orderId") Long orderId,@Param(value = "limitCount")Integer limitCount);



    List<OrderPush> selectOrderPushListByPushScore(@Param(value = "provinceNextId") List<Long> provinceNextId,@Param(value = "orderId") Long orderId);


    List<OrderPush> selectFamilyOrderPushList(@Param(value = "provinceNextId") List<Long> provinceNextId,@Param(value = "masterId") Long masterId,@Param(value = "limitCount")Integer limitCount);

    List<OrderPush> selectPushMasterList(@Param(value = "provinceNextId") List<Long> provinceNextId,@Param(value = "orderId")  Long orderId,@Param(value = "distance")  Integer distance);

    Integer orderPushCount(@Param(value = "provinceNextId")List<Long> provinceNextId,@Param(value = "orderId") Long orderId,@Param(value = "mode")Integer mode);


    int updateFullTimeExclusiveOrderFlag(@Param(value = "provinceNextId") List<Long> provinceNextId, @Param(value = "masterId") Long masterId, @Param(value = "limit") int limit);

    /**
     * 企业网站查询附近师傅
     * @param provinceNextId
     * @param orderId
     * @param limitCount
     * @return
     */
    List<OrderPush> selectSiteDetailNearbyMasterListAndLimit(@Param(value = "provinceNextId") List<Long> provinceNextId,
                                                             @Param(value = "orderId") Long orderId,
                                                             @Param(value = "limitCount") Integer limitCount);

}