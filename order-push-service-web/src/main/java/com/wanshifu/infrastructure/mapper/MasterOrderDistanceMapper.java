package com.wanshifu.infrastructure.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.infrastructure.MasterOrderDistance;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MasterOrderDistanceMapper extends IBaseCommMapper<MasterOrderDistance> {
    List<Long> selectByDistanceCreateTimeList(@Param("distanceCreateTime") Date distanceCreateTime, @Param("limitCount") Integer limitCount);

}