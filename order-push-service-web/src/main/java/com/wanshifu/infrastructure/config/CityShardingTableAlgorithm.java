package com.wanshifu.infrastructure.config;

import com.google.common.base.Strings;
import com.wanshifu.domain.base.tools.FeiShuTokenHelper;
import com.wanshifu.domain.base.tools.FeiShuUtils;
import com.wanshifu.framework.core.BusException;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/1 17:21
 */
@Slf4j
public class CityShardingTableAlgorithm implements PreciseShardingAlgorithm<Comparable<Long>> {

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Comparable<Long>> shardingValue) {
        String actualTableName = "order_push";

        Comparable<Long> value = shardingValue.getValue();
        if (Objects.isNull(value) || value.equals(0L)) {
            String msg = String.format("分片失败！城市地址参数错误！\nparam:%s,\ntable:%s", value.toString(), actualTableName);
            FeiShuUtils.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_PUSH_DATA_UPGRADE_PROVINCE_NEXT_ID_VALID_URL, msg);
            log.error("shardingStrategy error!shardingKey error!,shardingValue:{}", value);
            throw new BusException("分表分片策略异常！分片字段错误！");
        }
        if (value.equals(99999L)) {
            //跨城分表
            return actualTableName.concat("_other");
        }
        String suffixShardingTableName = OrderPushShardingConfigService.getShardingSuffixTableName(value.toString());
        if (Strings.isNullOrEmpty(suffixShardingTableName)) {
            //分表配置未配置该城市，进入兜底表，（当前只会在地址库有新地址更新，而分表配置未更新时会进入兜底表）
            String msg = String.format("分片失败！分表配置未包含该城市！进入兜底表！\ncityDivisionId:%s,\ntable:%s", value, actualTableName);
            FeiShuUtils.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_PUSH_DATA_UPGRADE_PROVINCE_NEXT_ID_VALID_URL, msg);
            return actualTableName.concat("_other");
        } else {
            return actualTableName.concat("_").concat(suffixShardingTableName);
        }

    }
}
