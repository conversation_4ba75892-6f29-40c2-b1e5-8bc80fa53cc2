package com.wanshifu.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/23 15:07
 */
@Slf4j
@Configuration
public class ElasticSearchClientConfig {

    @Value("${elasticsearch.host}")
    private String esHost;

    @Value("${elasticsearch.port}")
    private int esPort;

    @Value("${aliyun.es.username:}")
    private String aliyunUserName;

    @Value("${aliyun.es.password:}")
    private String aliyunPassWord;

    private final static String schema = "http";
    private RestClientBuilder builder;

    private final static boolean openConnectTime = false;
    private final static boolean openMutiConnect = false;
    private static int CONNECT_TIMEOUT_MILLIS = 10000;
    private static int SOCKET_TIMEOUT_MILLIS = 30000;
    private static int CONNECTION_REQUEST_TIMEOUT_MILLIS = 500;
    private static int MAX_CONN_PER_ROUTE = 500;
    private static int MAX_CONN_TOTAL = 500;

    @Bean(autowire = Autowire.BY_NAME, name = "restHighLevelClient")
    public RestHighLevelClient restHighLevelClient() {
        HttpHost httpHost = new HttpHost(esHost, esPort, schema);
        // 线上使用阿里云es，需要账号密码访问
        if (StringUtils.isNotBlank(aliyunUserName)) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(aliyunUserName, aliyunPassWord));
            builder = RestClient.builder(httpHost).setHttpClientConfigCallback(
                    httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
            );
        } else {
            builder = RestClient.builder(httpHost);
        }
        if (openConnectTime) {
            setConnectTimeOutConfig();
        }
        if (openMutiConnect) {
            setMutiConnectConfig();
        }
        return new RestHighLevelClient(builder);
    }


    /**
     * 配置连接时间延时
     */
    private void setConnectTimeOutConfig() {
        builder.setRequestConfigCallback(requestConfigBuilder -> {
            requestConfigBuilder.setConnectTimeout(CONNECT_TIMEOUT_MILLIS);
            requestConfigBuilder.setSocketTimeout(SOCKET_TIMEOUT_MILLIS);
            requestConfigBuilder.setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT_MILLIS);
            return requestConfigBuilder;
        });
    }

    /**
     * 使用异步httpclient时设置并发连接数
     */
    private void setMutiConnectConfig() {
        builder.setHttpClientConfigCallback(httpClientBuilder -> {
            httpClientBuilder.setMaxConnTotal(MAX_CONN_TOTAL);
            httpClientBuilder.setMaxConnPerRoute(MAX_CONN_PER_ROUTE);
            return httpClientBuilder;
        });
    }
}
