package com.wanshifu.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

/**
 * 动态数据源切面
 * <AUTHOR>
 * @date 2021/12/16
 */
@Slf4j
public class ClearDataSourceKeyInterceptor implements MethodInterceptor {

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        //先清除DataSourceKey防止串号
        DynamicDataSource.clearDataSourceKey();
        return invocation.proceed();
    }
}

