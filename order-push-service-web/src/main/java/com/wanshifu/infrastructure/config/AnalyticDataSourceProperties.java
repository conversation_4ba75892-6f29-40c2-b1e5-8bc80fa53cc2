package com.wanshifu.infrastructure.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>@wshifu.com
 * @Date 2023-11-28 17:25
 * @Description
 * @Version v1
 **/
@ConfigurationProperties(prefix = AnalyticDataSourceProperties.PREFIX)
public class AnalyticDataSourceProperties {
    public static final String PREFIX = "wanshifu.dataSource.analytic";
    private String url;
    private String username;
    private String password;
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

}
