package com.wanshifu.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @description 统一线程池配置管理
 * @date 2024/6/20 16:34
 */
@Configuration
@Slf4j
public class ThreadPoolConfig {

    /**
     * 更新样板城市订单弹窗时间线程池
     * @return
     */
    @Bean(name = "updateTmplCityTipTimeExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor getUpdateTmplCityTipTimeExecutorExecutor() {
        return new ThreadPoolExecutor(10, 10, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("updateTmplCityTipTime-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 重试删除order_push表数据线程池
     * @return
     */
    @Bean(name = "retryDeleteOrderPushExecutor", destroyMethod = "shutdown")
    public ScheduledThreadPoolExecutor getDeleteOrderPushExecutor() {
        return new ScheduledThreadPoolExecutor(10, new CustomizableThreadFactory("retryDeleteOrderPushExecutor-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 凌晨清理order_push表过期数据线程池
     * @return
     */
    @Bean(name = "clearExpireOrderPushExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor clearExpireOrderPushExecutor() {
        return new ThreadPoolExecutor(5, 5, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("clearExpireOrderPush-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 凌晨清理order_push_score表过期数据线程池
     * @return
     */
    @Bean(name = "clearExpireOrderPushScoreExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor clearExpireOrderPushScoreExecutor() {
        return new ThreadPoolExecutor(5, 5, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("clearExpireOrderPushScore-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 样板城市订单推单日志记录线程池
     * @return
     */
    @Bean(name = "recordTmplCityOrderPushLogExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor recordTmplCityOrderPushLogExecutor() {
        return new ThreadPoolExecutor(3, 3, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("recordTmplCityOrderPushLog-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 更新分数评分线程池
     * @return
     */
    @Bean(name = "updateScoreExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor updateScoreExecutor() {
        return new ThreadPoolExecutor(20, 20, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(2000),
                new CustomizableThreadFactory("updateScore-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 角标单独处理的线程池
     */
    @Bean(name = "analyticWaitOfferCountExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor analyticWaitOfferCountExecutor() {
        return new ThreadPoolExecutor(10, 10, 60,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(2000),
                new CustomizableThreadFactory("analyticWaitOfferCount-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }



    /**
     * 异步更新线程池
     * @return
     */
    @Bean(name = "asyncUpdateExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor asyncUpdateExecutor() {
        return new ThreadPoolExecutor(10, 10, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("asyncUpdateExecutor-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 订单推单异步线程池
     * @return
     */
    @Bean(name = "orderPushExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor orderPushExecutor() {
        return new ThreadPoolExecutor(10, 10, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("orderPushExecutor-executor-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 并发处理线程池
     * @return
     */
    @Bean(name = "concurrentProcessExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor concurrentProcessExecutor() {
        return new ThreadPoolExecutor(10, 10, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("concurrentProcess-executor-"),
                new ThreadPoolExecutor.DiscardPolicy());
    }


    /**
     * 边缘业务线程池（）
     * @return
     */
    @Bean(name = "otherBusDiscardPolicyExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor otherBusDiscardPolicyExecutor() {
        return new ThreadPoolExecutor(10, 10, 10, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("otherBusDiscardPolicy-executor-"),
                new ThreadPoolExecutor.DiscardPolicy());
    }
}
