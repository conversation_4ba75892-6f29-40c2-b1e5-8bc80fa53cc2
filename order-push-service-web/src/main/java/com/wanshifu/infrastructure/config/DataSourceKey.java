package com.wanshifu.infrastructure.config;


import java.util.HashMap;
import java.util.Map;

public enum DataSourceKey {

    /**
     * adb数据源
     */
    DEFAULT_ANALYTIC_DATASOURCE("default_analytic_dataSource"),

    /**
     * 默认数据源order_push_service
     */
    DEFAULT_PUSH_DATASOURCE("default_push_dataSource");

    public final String value;


    DataSourceKey(String value) {
        this.value = value;
    }


    private static final Map<String, DataSourceKey> valueMapping = new HashMap<>((int) (DataSourceKey.values().length / 0.75));

    static {
        for (DataSourceKey instance : DataSourceKey.values()) {
            valueMapping.put(instance.value, instance);
        }
    }

    public static DataSourceKey asValue(String value) {
        return valueMapping.get(value);
    }


}
