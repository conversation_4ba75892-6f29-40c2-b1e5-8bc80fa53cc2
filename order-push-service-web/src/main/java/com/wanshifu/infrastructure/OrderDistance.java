package com.wanshifu.infrastructure;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 订单距离表
 */
@Data
@ToString
@Table(name = "order_distance")
public class OrderDistance {

    /**
     * PK,订单距离记录ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "distance_id")
    private Long distanceId;

    /**
     * 订单ID
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 距离的订单ID
     */
    @Column(name = "distance_order_id")
    private Long distanceOrderId;

    /**
     * 订单类目id
     */
    @Column(name = "category_id")
    private Integer categoryId;

    /**
     * 距离订单类目id
     */
    @Column(name = "distance_category_id")
    private Integer distanceCategoryId;

    /**
     * 城市ID（二级地址，对应address表division_id字段）
     */
    @Column(name = "second_division_id")
    private Long secondDivisionId;

    /**
     * 地区ID（三级地址，对应address表division_id字段）
     */
    @Column(name = "third_division_id")
    private Long thirdDivisionId;

    /**
     * 订单地址距离(订单之间的距离),单位米
     */
    @Column(name = "order_address_distance")
    private Long orderAddressDistance;

    /**
     * 删除状态,1:删除,0:未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 备注
     */
    @Column(name = "note")
    private String note;

    /**
     * 距离记录创建时间
     */
    @Column(name = "distance_create_time")
    private Date distanceCreateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}