package com.wanshifu.infrastructure.gatewayreq.push;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * order_push、order_push_score分表改造
 * <AUTHOR>
 * @description 师傅小程序获取待报价列表GatewayRqt
 * @date 2024/4/25 16:41
 */
@Data
public class MasterAppletListOrderPushGatewayRqt {

    /**
     * 师傅地址id
     */
    @NotNull
    private Long masterDivisionId;

    /**
     * 推单数据省下级地址id
     * (当前业务传订单区域id或者师傅区域id就行，order-push-service负责计算省下级地址id)
     */
    @NotNull
    private List<Long> provinceNextId;


    private String masterSourceType;
}
