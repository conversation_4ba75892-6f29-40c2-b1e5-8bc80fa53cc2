package com.wanshifu.infrastructure;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 订单距离条件配置表
 */
@Data
@ToString
@Table(name = "order_distance_config")
public class OrderDistanceConfig {

    /**
     * 条件配置,主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "config_id")
    private Long configId;

    /**
     * 订单类型,1:顺路单,0:附近单,2:顺路单组合
     */
    @Column(name = "order_type")
    private Integer orderType;

    /**
     * 距离类型,1:订单距离,2:师傅距离
     */
    @Column(name = "distance_type")
    private Integer distanceType;

    /**
     * 城市ID（二级地址, 新地址库ID）
     */
    @Column(name = "second_division_id")
    private Long secondDivisionId;

    /**
     * 地址距离,单位米
     */
    @Column(name = "distance")
    private Long distance;

    /**
     * 删除状态,1:删除,0:未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}