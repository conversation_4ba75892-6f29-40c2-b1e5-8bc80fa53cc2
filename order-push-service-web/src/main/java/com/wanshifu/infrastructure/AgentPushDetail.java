package com.wanshifu.infrastructure;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 代理商推送记录表
 */
@Data
@ToString
@Table(name = "agent_push_detail")
public class AgentPushDetail {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;


    /**
     * 代理商id
     */
    @Column(name = "agent_id")
    private Long agentId;


    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;


    /**
     * 师傅id
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 无人报价时间配置
     */
    @Column(name = "nobody_offer_hour")
    private Integer nobodyOfferHour;

    /**
     * 推送时间
     */
    @Column(name = "push_time")
    private Date pushTime;

    /**
     * 合作商师傅首次查看订单时间
     */
    @Column(name = "first_view_time")
    private Date firstViewTime;

    /**
     * 报价时间
     */
    @Column(name = "offer_time")
    private Date offerTime;


    @Column(name = "disinterest_time")
    private Date disinterestTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}