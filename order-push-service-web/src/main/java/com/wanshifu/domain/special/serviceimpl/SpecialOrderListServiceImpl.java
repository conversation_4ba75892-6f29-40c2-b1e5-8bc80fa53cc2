package com.wanshifu.domain.special.serviceimpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.domain.base.model.RedisKeyConstant;
import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.base.tools.BeanEnhanceUtil;
import com.wanshifu.domain.push.gateway.OrderPushListGateway;
import com.wanshifu.domain.push.gateway.OrderPushResourceGateway;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.fulfill.CommonOrderFulfillService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.special.SpecialOrderListService;
import com.wanshifu.domain.special.ability.SpecialOrderAbilityService;
import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.infrastructure.*;
import com.wanshifu.infrastructure.MasterOrderDistance;
import com.wanshifu.infrastructure.OrderDistanceConditionConfig;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.repository.OrderDistanceConfigRepository;
import com.wanshifu.infrastructure.repository.SpecialOrderCategoryConfigRepository;
import com.wanshifu.order.offer.api.NormalOrderListApi;
import com.wanshifu.order.offer.domains.api.request.BatchGetOrderParam;
import com.wanshifu.order.offer.domains.api.request.BatchOrderServiceAttributeInfoResp;
import com.wanshifu.order.offer.domains.bo.service.ServiceInfo;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.push.request.special.SideOrderCombinationDTO;
import com.wanshifu.order.push.response.special.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.repository.OrderDistanceConditionConfigRepository;
import com.wanshifu.master.order.domains.api.response.serveInfo.ServeDetailResp;
import com.wanshifu.master.order.domains.enums.ServeInfoNodeType;
import com.wanshifu.master.order.domains.enums.ServeNodeStatus;
import com.wanshifu.master.order.domains.enums.ServeStatus;
import com.wanshifu.master.order.domains.enums.ServeType;
import com.wanshifu.master.order.domains.po.OrderServeInfo;
import com.wanshifu.master.order.domains.po.ServeReserveCustomer;
import com.wanshifu.order.offer.domains.api.response.BranchGetOrderPartsResp;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.SimpleOrderGrab;
import com.wanshifu.order.offer.domains.po.*;
import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderExtraDataDTO;
import com.wanshifu.order.push.enums.PushBusinessCode;
import com.wanshifu.order.push.request.special.SideOrderListDTO;
import com.wanshifu.order.push.request.special.SpecialOrderCountDTO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
//import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.GeoDistanceSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GeodeticCurve;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-02-21 11:48
 * @Description
 * @Version v1
 **/
@Slf4j
@Service
public class SpecialOrderListServiceImpl implements SpecialOrderListService {
    @Resource
    private SpecialOrderAbilityService specialOrderAbilityService;
    @Resource
    private SpecialOrderGateway specialOrderGateway;
    @Resource
    private CommonOrderOfferService orderOfferService;
    @Resource
    private CommonOrderFulfillService commonOrderFulfillService;
    @Resource
    protected OrderDistanceConditionConfigRepository orderDistanceConditionConfigRepository;

    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;


    @Resource
    OrderPushResourceGateway orderPushResourceGateway;

    @Resource
    private CommonAddressService commonAddressService;

    @Value("${specialOrder.sideOrderSwitch:on}")
    private String sideOrderSwitch;


    @Resource
    private ApolloSwitchUtils apolloSwitchUtils;


    @Autowired
    private RedisHelper redisHelper;

    /**
     * 顺路单组合内订单数量
     */
    @Value("${sideOrderCombination.orderCnt:5}")
    private Integer sideOrderCombinationOrderCnt;


    /**
     * 顺路单组合查询待接单一口价订单数量
     */
    @Value("${sideOrderCombination.orderPushCnt:500}")
    private Integer sideOrderCombinationOrderPushCnt;

    @Override
    public List<SideOrderCountResp> sideOrderCount(SpecialOrderCountDTO rqt) {
        List<SideOrderCountResp> countRespList = new ArrayList<>();
        //是否开启顺路单列表
        if (!"on".equals(sideOrderSwitch)) {
            return countRespList;
        }
        List<Long> offerOrderIds = rqt.getOrderIds();

        if (Objects.isNull(offerOrderIds)) {
            return countRespList;
        }


        //批量查询订单信息
        List<Long> orderIds = rqt.getOrderIds();
        Long masterId = rqt.getMasterId();
        HashMap<Long, OrderBase> orderBaseMap = new HashMap<>();
        HashMap<Long, OrderGrab> orderGrabMap = new HashMap<>();
        HashMap<Long, OrderExtraData> orderExtraMap = new HashMap<>();
        Long secondDivisionId = 0L;
        List<Integer> categoryIds = new ArrayList<>();
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(), "sideOrderCount", JSONObject.toJSONString(rqt));
        /**
         for (Long officeOrderId : orderIds) {
         SimpleOrderGrab simpleOrderGrab = orderOfferService.getSimpleOrderGrabByOrderId(officeOrderId);

         OrderBase orderBase = simpleOrderGrab.getOrderBase();

         secondDivisionId = simpleOrderGrab.getOrderGrab().getSecondDivisionId();
         Long orderId = orderBase.getOrderId();
         orderBaseMap.put(orderId, orderBase);
         orderGrabMap.put(orderId, simpleOrderGrab.getOrderGrab());
         orderExtraMap.put(orderId, simpleOrderGrab.getOrderExtraData());


         categoryIds.add(orderBase.getCategoryId());


         }
         */


        long startTime = System.currentTimeMillis();
        BatchGetOrderParam batchGetOrderParam = new BatchGetOrderParam();
        batchGetOrderParam.setOrderIds(orderIds);
        List<SimpleOrderGrab> simpleOrderGrabList = orderOfferService.getSimpleOrderGrabByOrderId(batchGetOrderParam);

        if (CollectionUtil.isNotEmpty(simpleOrderGrabList) && Objects.nonNull(simpleOrderGrabList.get(0).getOrderGrab())) {

            secondDivisionId = simpleOrderGrabList.get(0).getOrderGrab().getSecondDivisionId();
        }


        for (SimpleOrderGrab simpleOrderGrab : simpleOrderGrabList) {


            OrderBase orderBase = simpleOrderGrab.getOrderBase();


            Long orderId = orderBase.getOrderId();
            orderBaseMap.put(orderId, orderBase);
            orderGrabMap.put(orderId, simpleOrderGrab.getOrderGrab());
            orderExtraMap.put(orderId, simpleOrderGrab.getOrderExtraData());

            if (!categoryIds.contains(orderBase.getCategoryId())) {
                categoryIds.add(orderBase.getCategoryId());
            }


        }

        long endTime = System.currentTimeMillis();

        log.info("<<<查询基本信息扩展信息等所花时间:{}ms", (endTime - startTime));


        List<OrderDistanceConditionConfig> conditionConfigs = new ArrayList<OrderDistanceConditionConfig>();
        //查询配置
        List<OrderDistanceConditionConfig> configs = redisHelper.getObjectList(RedisKeyConstant.ORDER_DISTANCE_CONFIG_BTW_CITY_KEY.concat(secondDivisionId.toString()), OrderDistanceConditionConfig.class);


        if (CollUtil.isEmpty(configs)) {
            configs = orderDistanceConditionConfigRepository.selectConditionConfigByList(1, null, null, secondDivisionId);

            if (!CollUtil.isEmpty(configs)) {
                redisHelper.setObjectList(RedisKeyConstant.ORDER_DISTANCE_CONFIG_BTW_CITY_KEY.concat(secondDivisionId.toString()), configs, 600);

            }

        }

        for (OrderDistanceConditionConfig orderDistanceConditionConfig : configs) {
            if (categoryIds.contains(orderDistanceConditionConfig.getCategoryId())) {
                conditionConfigs.add(orderDistanceConditionConfig);
            }

        }

        HashMap<Integer, Long> distanceMap = new HashMap<>();
        HashMap<Integer, Long> masterDistanceMap = new HashMap<>();
        conditionConfigs.forEach(it -> {
            if (it.getDistanceType() == 1) {
                distanceMap.put(it.getCategoryId(), it.getAddressDistance());
            } else if (it.getDistanceType() == 0)//师傅距离
            {
                masterDistanceMap.put(it.getCategoryId(), it.getAddressDistance());
            }
        });


        endTime = System.currentTimeMillis();

        log.info("<<<查询配置路径信息所花时间:{}ms", (endTime - startTime));


        //查询order_push表，查询这些待报价列表的push_distance,push_distance_type,category_id


        //批量查询师傅和订单推单记录
        List<OrderPush> orderPushList = orderPushResourceGateway.batchGetOrderPushByOrderIdsAndMasterId(provinceNextId, orderIds, masterId);
        if (CollectionUtils.isEmpty(orderPushList)) {
            return new ArrayList<>();
        }


        List<Long> filterOrderIds = new ArrayList<>();
        //过滤具有查询条件的待报价订单
        for (OrderPush orderPush : orderPushList) {
            //push_distance,push_distance_type,category_id

            Long orderId = orderPush.getOrderId();
            Long pushDistance = orderPush.getPushDistance();

            log.info(">>>>师傅距离:{},orderId:{}", pushDistance, orderId);
            Integer categoryId = orderPush.getCategoryId();
            //如果这个订单的类目在配置名单中
            if (masterDistanceMap.keySet().contains(categoryId)) {
                Long addressDistance = masterDistanceMap.get(categoryId);
                if (pushDistance > addressDistance) {
                    filterOrderIds.add(orderId);
                }


            }
        }

        endTime = System.currentTimeMillis();

        log.info("<<<查询过滤后的order_push信息所花时间:{}", (endTime - startTime));


        if (CollUtil.isEmpty(filterOrderIds)) {
            return countRespList;

        }

        log.info(">>filterOrderIds:{}", filterOrderIds.toArray());

        for (Long orderId : filterOrderIds) {
            OrderExtraData orderExtraData = orderExtraMap.get(orderId);
            BigDecimal latitude = orderExtraData.getBuyerAddressLatitude();
            BigDecimal longitude = orderExtraData.getBuyerAddressLongitude();
            if (Objects.isNull(latitude) || Objects.isNull(longitude)) {
                return countRespList;
            }

            Integer categoryId = orderBaseMap.get(orderId).getCategoryId();
            Long addRessDistance = distanceMap.get(categoryId);


            try {
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
                queryBuilder.filter(QueryBuilders.termsQuery("master_id", Arrays.asList(masterId)));
                //queryBuilder.filter(QueryBuilders.termsQuery("category_id", Arrays.asList(categoryId)));
                queryBuilder.filter().add(QueryBuilders.geoDistanceQuery("order_location").point(latitude.doubleValue(), longitude.doubleValue()).distance(addRessDistance, DistanceUnit.METERS));


                SearchRequest searchRequest = new SearchRequest("order_serve_info_location");

                SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
                searchSourceBuilder.query(queryBuilder);
                searchSourceBuilder.size(0); // 不需要返回文档内容，只计算数量


                searchRequest.source(searchSourceBuilder);

                //closeClient(client);

                SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
                long count = searchResponse.getHits().getTotalHits();
                if (count >= 10) {
                    count = 10;
                }
                SideOrderCountResp sideOrderCountResp = new SideOrderCountResp();
                sideOrderCountResp.setOrderId(orderId);
                sideOrderCountResp.setNumber((int) count);

                countRespList.add(sideOrderCountResp);


                log.info("<<---->>查询 es cost time:{}ms", (endTime - startTime));

            } catch (Exception e) {
                // Handle the exception appropriately
                e.printStackTrace(); // Or log it using your preferred logging framework
            }
        }
        if (countRespList.size() > 0) {
            long endTimeL = System.currentTimeMillis();
            log.warn("sideOrderCountForEs spendTime {}ms,params:{}", (endTimeL - endTime), JSONObject.toJSONString(rqt));
        }
        return countRespList;
    }


    @Override
    public List<SideOrderListResp> sideOrderListFromES(SideOrderListDTO rqt) {
        List<SideOrderListResp> filterOrderDistanceList = new ArrayList<>();
        //是否开启顺路单列表
        if (!"on".equals(sideOrderSwitch)) {
            return filterOrderDistanceList;
        }
        Long officeOrderId = rqt.getOfficeOrderIds();
        if (Objects.isNull(officeOrderId)) {
            return filterOrderDistanceList;
        }
        long startTime = System.currentTimeMillis();
        List<OrderServeInfoResp> orderServeInfoResps = getSideInfoFromEs(officeOrderId, rqt.getMasterId());
        if (CollectionUtils.isEmpty(orderServeInfoResps)) {
            return filterOrderDistanceList;
        }
        long endTime = System.currentTimeMillis();
        log.warn("sideOrderListFromES orderId:{}, getSideInfoFromEsSpendTime {}ms", officeOrderId, (endTime - startTime));
        List<Long> serveOrderIdList = orderServeInfoResps.stream().map(OrderServeInfoResp::getDistanceOrderId).collect(Collectors.toList());
        List<OrderBaseComposite> orderBaseCompositeBatch = orderOfferService.getOrderBaseCompositeBatch(serveOrderIdList);
        if (CollectionUtils.isEmpty(orderBaseCompositeBatch)) {
            return filterOrderDistanceList;
        }
        Map<Long, OrderBase> orderBaseMap = new HashMap<>();
        Map<Long, OrderExtraData> orderExtraDataMap = new HashMap<>();
        Map<Long, OrderLogisticsInfo> orderLogisticsInfoMap = new HashMap<>();
        Map<Long, OrderGrab> orderGrabMap = new HashMap<>();
        orderBaseCompositeBatch.forEach(it -> {
            OrderBase orderBaseResp = it.getOrderBase();
            Long orderId = orderBaseResp.getOrderId();
            orderBaseMap.put(orderId, orderBaseResp);
            orderExtraDataMap.put(orderId, it.getOrderExtraData());
            orderLogisticsInfoMap.put(orderId, it.getOrderLogisticsInfo());
            orderGrabMap.put(orderId, it.getOrderGrab());
        });


        List<Long> orderServeIdList = orderServeInfoResps.stream().map(OrderServeInfoResp::getOrderServeId).distinct().collect(Collectors.toList());
        Map<Long, ServeDetailResp> serveDetailRespMap = getServeDetailRespMap(orderServeIdList);

        for (OrderServeInfoResp orderDistance : orderServeInfoResps) {
            Long distanceOrderId = orderDistance.getDistanceOrderId();
            Long orderServeId = orderDistance.getOrderServeId();
            OrderBase serveOrderBase = orderBaseMap.get(distanceOrderId);
            OrderExtraData orderExtraData = orderExtraDataMap.get(distanceOrderId);
            OrderGrab orderGrab = orderGrabMap.get(distanceOrderId);
            OrderLogisticsInfo orderLogisticsInfo = orderLogisticsInfoMap.get(distanceOrderId);
            ServeDetailResp serveDetailResp = serveDetailRespMap.get(orderServeId);
            OrderServeInfo orderServeInfo = new OrderServeInfo();
            ServeReserveCustomer serveReserveCustomer = new ServeReserveCustomer();
            if (Objects.nonNull(serveDetailResp)) {
                orderServeInfo = serveDetailResp.getOrderServeInfo();
                serveReserveCustomer = serveDetailResp.getServeReserveCustomer();
            }

            if (!ObjectUtil.isAllNotEmpty(serveOrderBase, orderServeInfo, orderExtraData, orderGrab)) {
                log.info("订单信息为空,serveOrderBase={},orderServeInfo={},orderExtraData={}", JSONObject.toJSONString(serveOrderBase), JSONObject.toJSONString(orderServeInfo), JSONObject.toJSONString(orderExtraData));
                continue;
            }
            if (!ServeStatus.SERVING.code.equals(orderServeInfo.getServeStatus())) {
                log.info("订单服务信息为空,orderServeInfo={}", orderServeInfo);
                continue;
            }
            SideOrderListResp.OrderInfo orderInfo = new SideOrderListResp.OrderInfo();
            orderInfo.setOrderAddressDistance(orderDistance.getOrderAddressDistance());
            if (ServeType.isDeliveryTypeNonReturn(serveOrderBase.getServeType()) && ObjectUtil.isNotNull(orderLogisticsInfo)) {
                orderInfo.setPickupAddress(orderLogisticsInfo.getPickupAddress());
            }
            orderInfo.setAppointType(orderGrab.getAppointType());
            SideOrderListResp.OrderServeCompositeInfo compositeInfo = new SideOrderListResp.OrderServeCompositeInfo();
            String nextServeNode = orderServeInfo.getNextServeNode();
            compositeInfo.setNextServeNode(nextServeNode);
            if (ObjectUtil.isNotNull(serveReserveCustomer)) {
                compositeInfo.setReserveStartTime(serveReserveCustomer.getReserveStartTime());
                compositeInfo.setReserveEndTime(serveReserveCustomer.getReserveEndTime());
            }
            String orderType = "";
            if (ServeNodeStatus.RESERVE_CUSTOMER.code.equals(nextServeNode)) {
                orderType = "wait_reserve_customer";
            } else if (ServeNodeStatus.LOGISTICS_SIGN.code.equals(nextServeNode)) {
                orderType = "wait_logistics_sign";
            } else if (ServeNodeStatus.SERVE_COMPLETE.code.equals(nextServeNode)) {
                orderType = "wait_complete";
            } else {
                orderType = "wait_serve_sign";
            }
            OrderBaseDTO orderBaseDTO = BeanEnhanceUtil.copyBean(serveOrderBase, OrderBaseDTO::new);
            OrderExtraDataDTO orderExtraDataDTO = BeanEnhanceUtil.copyBean(orderExtraData, OrderExtraDataDTO::new);

            filterOrderDistanceList.add(new SideOrderListResp(orderType, orderInfo, orderBaseDTO,
                    orderExtraDataDTO, compositeInfo));

            if (filterOrderDistanceList.size() >= 10) {
                break;
            }
        }
        long endTime2 = System.currentTimeMillis();
        log.warn("sideOrderListFromES orderId:{},groupDataSpendTime {}ms", officeOrderId, (endTime2 - endTime));
        if (filterOrderDistanceList.size() > 0) {
            log.warn("sideOrderListFromES returnFilterOrderDistanceList,orderId:{},spendFullTime {}ms", officeOrderId, (endTime2 - startTime));
        }
        return filterOrderDistanceList;
    }

    private List<OrderServeInfoResp> getSideInfoFromEs(Long officeOrderId, Long masterId) {
        List<OrderServeInfoResp> orderServeInfoResps = new ArrayList<>();

        Integer categoryId = 0;
        Long addRessDistance = 0L;
        Long masterAddRessDistance = 0L;
        BigDecimal latitude;
        BigDecimal longitude;
        Long secondDivisionId = 0L;
        Long thirdDivisionId;
        long startTime = System.currentTimeMillis();
        SimpleOrderGrab simpleOrderGrab = orderOfferService.getSimpleOrderGrabByOrderId(officeOrderId);

        long endTime = System.currentTimeMillis();
        log.info("<<从getSimpleOrderGrabByOrderId接口查询订单基本信息扩展信息所花时间:{}ms", (endTime - startTime));
        if (Objects.isNull(simpleOrderGrab)) {
            throw new BusException(PushBusinessCode.BUS_VALIDATION_ERROR.code, "获取订单基础信息失败");
        }
        if (Objects.isNull(simpleOrderGrab.getOrderGrab())|| Objects.isNull(simpleOrderGrab.getOrderExtraData())) {
            return orderServeInfoResps;
        }

        latitude = simpleOrderGrab.getOrderExtraData().getBuyerAddressLatitude();
        longitude = simpleOrderGrab.getOrderExtraData().getBuyerAddressLongitude();

        if (Objects.isNull(latitude) || Objects.isNull(longitude)) {
            return orderServeInfoResps;
        }
        categoryId = simpleOrderGrab.getOrderBase().getCategoryId();
        secondDivisionId = simpleOrderGrab.getOrderGrab().getSecondDivisionId();
        thirdDivisionId = simpleOrderGrab.getOrderBase().getThirdDivisionId();
        if (Objects.isNull(thirdDivisionId) || thirdDivisionId == 0L) {
            return null;
        }
        log.info("<<latitude:{}，longitude:{}", latitude,longitude);
        log.info("<<查询订单基本信息扩展信息所花时间:{}ms", (endTime - startTime));

        long startTime1 = System.currentTimeMillis();
        List<OrderDistanceConditionConfig> configs = redisHelper.getObjectList(RedisKeyConstant.ORDER_DISTANCE_CONFIG_BTW_CITY_KEY.concat(secondDivisionId.toString()), OrderDistanceConditionConfig.class);

        endTime = System.currentTimeMillis();
        log.info("<<查询距离配置信息读取redis所花时间:{}ms", (endTime - startTime1));


        if (CollUtil.isEmpty(configs)) {
            log.info("<<redis中不存在配置信息，从db中读取");
            List<OrderDistanceConditionConfig> conditionConfigs = orderDistanceConditionConfigRepository.selectConditionConfigByList(1, null, null, secondDivisionId);

            if (CollUtil.isEmpty(conditionConfigs)) {
                return orderServeInfoResps;
            }
            redisHelper.setObjectList(RedisKeyConstant.ORDER_DISTANCE_CONFIG_BTW_CITY_KEY.concat(secondDivisionId.toString()), conditionConfigs, 600);


            for (OrderDistanceConditionConfig orderDistanceConditionConfig : conditionConfigs) {
                if (categoryId.equals(orderDistanceConditionConfig.getCategoryId())) {

                    if (orderDistanceConditionConfig.getDistanceType() == 1) {
                        addRessDistance = orderDistanceConditionConfig.getAddressDistance();
                    }
                    if (orderDistanceConditionConfig.getDistanceType() == 0) {
                        masterAddRessDistance = orderDistanceConditionConfig.getAddressDistance();

                    }

                }
            }

        } else {
            for (OrderDistanceConditionConfig orderDistanceConditionConfig : configs) {
                if (categoryId.equals(orderDistanceConditionConfig.getCategoryId())) {

                    if (orderDistanceConditionConfig.getDistanceType() == 1) {
                        addRessDistance = orderDistanceConditionConfig.getAddressDistance();
                    }
                    if (orderDistanceConditionConfig.getDistanceType() == 0) {
                        masterAddRessDistance = orderDistanceConditionConfig.getAddressDistance();

                    }

                }
            }

        }
        endTime = System.currentTimeMillis();
        log.info("<<查询距离配置信息所花时间:{}ms", (endTime - startTime));

        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(thirdDivisionId, "sideOrderListForES", officeOrderId.toString());
        //批量查询师傅和订单推单记录
        OrderPush orderPush = orderPushResourceGateway.getOrderPush(provinceNextIds, officeOrderId, masterId);
        endTime = System.currentTimeMillis();
        log.info("<<查询order-push信息所花时间:{}ms", (endTime - startTime));


        if(Objects.isNull(orderPush)){
            log.warn("<<没有查询到该推单信息.orderId:{},masterId{}", officeOrderId, masterId);
            return orderServeInfoResps;
        }

        Long pushDistance = orderPush.getPushDistance();

        if (pushDistance < masterAddRessDistance) {
            log.info("<<师傅与订单距离小于设置的距离orderId:{},masterId{},pushDistance:{},masterAddRessDistance:{}", officeOrderId, masterId, pushDistance, masterAddRessDistance);
            return orderServeInfoResps;
        }


        try {

            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            log.info(">>查询es的条件,master_id:{},latitude:{},longitude:{},addRessDistance:{}",masterId,latitude,longitude,addRessDistance);
            queryBuilder.filter(QueryBuilders.termsQuery("master_id", Arrays.asList(masterId)));
            //  queryBuilder.filter(QueryBuilders.termsQuery("category_id", Arrays.asList(categoryId)));
            queryBuilder.filter().add(QueryBuilders.geoDistanceQuery("order_location").point(latitude.doubleValue(), longitude.doubleValue()).distance(addRessDistance, DistanceUnit.METERS));
            SearchRequest searchRequest = new SearchRequest("order_serve_info_location");
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(queryBuilder);
            searchSourceBuilder.sort("order_serve_id", SortOrder.DESC);
            searchSourceBuilder.from(0);
            searchSourceBuilder.size(10);

            // 设置geo_distance排序


            /**

             使用GeoDistanceSortBuilder构建查询条件
             GeoDistanceSortBuilder distanceSortBuilder = new GeoDistanceSortBuilder("order_location", latitude.doubleValue(), longitude.doubleValue());
             // 设置单位为米，升序排序
             distanceSortBuilder.unit(DistanceUnit.METERS).order(SortOrder.ASC);
             searchSourceBuilder.sort(distanceSortBuilder);
             *
             */


            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
            long count = searchResponse.getHits().getTotalHits();

            log.info("<<查询es返回的记录条数:{}",count);
            //  List<Long> serveOrderIdList = new ArrayList<>();
            //   List<Long> orderServeIdList = new ArrayList<>();
            for (SearchHit hit : searchResponse.getHits()) {
                if (hit != null) {
                    String source = hit.getSourceAsString();

                    JSONObject item = JSONObject.parseObject(source);

                    /**
                     if (!serveOrderIdList.contains(item.getLong("order_id"))) {
                     serveOrderIdList.add(item.getLong("order_id"));
                     }

                     if (!orderServeIdList.contains(item.getLong("order_serve_id"))) {
                     orderServeIdList.add(item.getLong("order_serve_id"));
                     }
                     */

                    JSONObject locationNode =(JSONObject) item.get("order_location");
                    double lat = Double.parseDouble(locationNode.get("lat").toString());
                    double lon = Double.parseDouble(locationNode.get("lon").toString());

                    log.info("<<服务单的经纬度值:lat:{},lon:{}",lat,lon);


                    /**
                     BigDecimal orderDistance = new BigDecimal(hit.getSortValues()[0].toString())
                     .setScale(0, BigDecimal.ROUND_HALF_UP);


                     log.info("<<服务单的距离:orderDistance:{}",orderDistance);

                     */

                    long distance= getStraightLintDistance(latitude.doubleValue(), longitude.doubleValue(),lat,lon);
                    log.info("<<服务单的的距离:distance:{},",distance);
                    OrderServeInfoResp masterOrderDistance = new OrderServeInfoResp();
                    masterOrderDistance.setOrderId(item.getLong("order_id"));
                    masterOrderDistance.setDistanceOrderId(item.getLong("order_id"));
                    masterOrderDistance.setMasterId(item.getLong("master_id"));
                    masterOrderDistance.setOrderServeId(item.getLong("order_serve_id"));
                    masterOrderDistance.setOrderAddressDistance(distance);
                    orderServeInfoResps.add(masterOrderDistance);

                }
            }

            endTime = System.currentTimeMillis();
            log.info("<<查询es的顺路单列表信息所花时间:{}ms", (endTime - startTime));

        } catch (Exception e) {
            // Handle the exception appropriately
            e.printStackTrace(); // Or log it using your preferred logging framework
        }
        return orderServeInfoResps;
    }

    @Override
    public List<SideOrderListResp> sideOrderList(SideOrderListDTO rqt) {
        List<SideOrderListResp> sideOrderListResps = new ArrayList<>();
        //是否开启顺路单列表
        if (!specialOrderAbilityService.getSpecialOrderSwitch(1)) {
            return sideOrderListResps;
        }
        Long officeOrderId = rqt.getOfficeOrderIds();
        OrderBase orderBase;
        OrderGrab officeOrderGrab;
        SimpleOrderGrab simpleOrderGrab = orderOfferService.getSimpleOrderGrabByOrderId(officeOrderId);
        if (Objects.isNull(simpleOrderGrab)) {
            throw new BusException(PushBusinessCode.BUS_VALIDATION_ERROR.code, "获取订单基础信息失败");
        }
        orderBase = simpleOrderGrab.getOrderBase();
        officeOrderGrab = simpleOrderGrab.getOrderGrab();

        if (Objects.isNull(orderBase) || Objects.isNull(officeOrderGrab)) {
            return sideOrderListResps;
        }
        //最大10条
        List<MasterOrderDistance> filterOrderDistanceList = specialOrderGateway.getFilterSideOrderPush(officeOrderId, rqt.getMasterId(), officeOrderGrab.getSecondDivisionId());

        //满足配置的顺路单
        if (CollUtil.isNotEmpty(filterOrderDistanceList)) {
            //查询订单信息
            List<Long> serveOrderIdList = filterOrderDistanceList.stream().map(MasterOrderDistance::getDistanceOrderId).distinct().collect(Collectors.toList());
            List<OrderBaseComposite> orderBaseCompositeBatch = orderOfferService.getOrderBaseCompositeBatch(serveOrderIdList);
            if (CollectionUtils.isEmpty(orderBaseCompositeBatch)) {
                return sideOrderListResps;
            }
            Map<Long, OrderBase> orderBaseMap = new HashMap<>();
            Map<Long, OrderExtraData> orderExtraDataMap = new HashMap<>();
            Map<Long, OrderLogisticsInfo> orderLogisticsInfoMap = new HashMap<>();
            Map<Long, OrderGrab> orderGrabMap = new HashMap<>();
            orderBaseCompositeBatch.forEach(it -> {
                OrderBase orderBaseResp = it.getOrderBase();
                Long orderId = orderBaseResp.getOrderId();
                orderBaseMap.put(orderId, orderBaseResp);
                orderExtraDataMap.put(orderId, it.getOrderExtraData());
                orderLogisticsInfoMap.put(orderId, it.getOrderLogisticsInfo());
                orderGrabMap.put(orderId, it.getOrderGrab());
            });


            List<Long> orderServeIdList = filterOrderDistanceList.stream().map(MasterOrderDistance::getOrderServeId).distinct().collect(Collectors.toList());
            Map<Long, ServeDetailResp> serveDetailRespMap = getServeDetailRespMap(orderServeIdList);

            for (MasterOrderDistance orderDistance : filterOrderDistanceList) {
                Long distanceOrderId = orderDistance.getDistanceOrderId();
                Long orderServeId = orderDistance.getOrderServeId();
                OrderBase serveOrderBase = orderBaseMap.get(distanceOrderId);
                OrderExtraData orderExtraData = orderExtraDataMap.get(distanceOrderId);
                OrderGrab orderGrab = orderGrabMap.get(distanceOrderId);
                OrderLogisticsInfo orderLogisticsInfo = orderLogisticsInfoMap.get(distanceOrderId);
                ServeDetailResp serveDetailResp = serveDetailRespMap.get(orderServeId);
                OrderServeInfo orderServeInfo = new OrderServeInfo();
                ServeReserveCustomer serveReserveCustomer = new ServeReserveCustomer();
                if (Objects.nonNull(serveDetailResp)) {
                    orderServeInfo = serveDetailResp.getOrderServeInfo();
                    serveReserveCustomer = serveDetailResp.getServeReserveCustomer();
                }

                if (!ObjectUtil.isAllNotEmpty(serveOrderBase, orderServeInfo, orderExtraData, orderGrab)) {
                    log.info("订单信息为空,serveOrderBase={},orderServeInfo={},orderExtraData={}", JSONObject.toJSONString(serveOrderBase), JSONObject.toJSONString(orderServeInfo), JSONObject.toJSONString(orderExtraData));
                    continue;
                }
                if (!ServeStatus.SERVING.code.equals(orderServeInfo.getServeStatus())) {
                    log.info("订单服务信息为空,orderServeInfo={}", orderServeInfo);
                    continue;
                }
                SideOrderListResp.OrderInfo orderInfo = new SideOrderListResp.OrderInfo();
                orderInfo.setOrderAddressDistance(orderDistance.getOrderAddressDistance());
                if (ServeType.isDeliveryTypeNonReturn(serveOrderBase.getServeType()) && ObjectUtil.isNotNull(orderLogisticsInfo)) {
                    orderInfo.setPickupAddress(orderLogisticsInfo.getPickupAddress());
                }
                orderInfo.setAppointType(orderGrab.getAppointType());
                SideOrderListResp.OrderServeCompositeInfo compositeInfo = new SideOrderListResp.OrderServeCompositeInfo();
                String nextServeNode = orderServeInfo.getNextServeNode();
                compositeInfo.setNextServeNode(nextServeNode);
                if (ObjectUtil.isNotNull(serveReserveCustomer)) {
                    compositeInfo.setReserveStartTime(serveReserveCustomer.getReserveStartTime());
                    compositeInfo.setReserveEndTime(serveReserveCustomer.getReserveEndTime());
                }
                String orderType = "";
                if (ServeNodeStatus.RESERVE_CUSTOMER.code.equals(nextServeNode)) {
                    orderType = "wait_reserve_customer";
                } else if (ServeNodeStatus.LOGISTICS_SIGN.code.equals(nextServeNode)) {
                    orderType = "wait_logistics_sign";
                } else if (ServeNodeStatus.SERVE_COMPLETE.code.equals(nextServeNode)) {
                    orderType = "wait_complete";
                } else {
                    orderType = "wait_serve_sign";
                }
                OrderBaseDTO orderBaseDTO = BeanEnhanceUtil.copyBean(serveOrderBase, OrderBaseDTO::new);
                OrderExtraDataDTO orderExtraDataDTO = BeanEnhanceUtil.copyBean(orderExtraData, OrderExtraDataDTO::new);

                sideOrderListResps.add(new SideOrderListResp(orderType, orderInfo, orderBaseDTO,
                        orderExtraDataDTO, compositeInfo));

                if (sideOrderListResps.size() >= 10) {
                    continue;
                }
            }


        }
        return sideOrderListResps;
    }


    private Map<Long, ServeDetailResp> getServeDetailRespMap(List<Long> orderServeIdList) {
        Map<Long, ServeDetailResp> serveDetailRespMap = new HashMap<>();
        List<ServeDetailResp> serveDetailResps = commonOrderFulfillService.batchGetServeInfoByServeIds(orderServeIdList,
                ServeInfoNodeType.RESERVE_CUSTOMER);
        if (CollectionUtils.isNotEmpty(serveDetailResps)) {
            serveDetailRespMap = serveDetailResps.stream().collect(Collectors.toMap(serveDetailResp -> serveDetailResp.getOrderServeInfo().getOrderServeId(), Function.identity()));
        }
        return serveDetailRespMap;
    }


    /**
     * 获取直线距离
     *
     * @param sourceLatitude
     * @param sourceLongitude
     * @param targetLatitude
     * @param targetLongitude
     * @return
     */
    public Long getStraightLintDistance(double sourceLatitude, double sourceLongitude, double targetLatitude, double targetLongitude) {
        GlobalCoordinates source = new GlobalCoordinates(sourceLatitude, sourceLongitude);
        GlobalCoordinates target = new GlobalCoordinates(targetLatitude, targetLongitude);

        GeodeticCurve curve = new GeodeticCalculator().calculateGeodeticCurve(Ellipsoid.Sphere, source, target);

        return new Double(curve.getEllipsoidalDistance()).longValue();
    }


    @Override
    public SideOrderCombinationResp sideOrderCombinationList(SideOrderCombinationDTO rqt){

        if(!apolloSwitchUtils.isOpenSideOrderCombination()){
            return null;
        }

        try{
            SideOrderCombinationResp sideOrderCombinationResp = getSideOrderCombinationFromEs(rqt.getProvinceNextId(),rqt.getOrderIds(),rqt.getMasterId());
            return sideOrderCombinationResp;
        }catch(Exception e){
            log.error("sideOrderCombinationList error",e);
        }

        return null;
    }


    @Resource
    private OrderDistanceConfigRepository orderDistanceConfigRepository;

    @Resource
    private SpecialOrderCategoryConfigRepository specialOrderCategoryConfigRepository;

    @Resource
    private NormalOrderListApi normalOrderListApi;

    private SideOrderCombinationResp  getSideOrderCombinationFromEs(Long provinceNextId,List<Long> orderIdList, Long masterId) {


        Long startTime = System.currentTimeMillis();
        List<BatchOrderServiceAttributeInfoResp> serviceAttributeInfoRespList = normalOrderListApi.getOrderServiceAttributeInfoBatch(StringUtils.join(orderIdList, ","));

        Long endTime = System.currentTimeMillis();
        log.info(String.format("getSideOrderCombinationFromEs getOrderServiceAttributeInfoBatch take times: %d ms",(endTime - startTime)));


        if(CollectionUtils.isEmpty(serviceAttributeInfoRespList)){
            return null;
        }

        List<BatchOrderServiceAttributeInfoResp> resplist = serviceAttributeInfoRespList.stream().filter(resp -> Objects.nonNull(resp.getOrderGrab())
                && resp.getOrderGrab().getAppointType() == AppointType.DEFINITE_PRICE.value.intValue()
                && Objects.nonNull(resp.getOrderExtraData().getBuyerAddressLatitude()) && resp.getOrderExtraData().getBuyerAddressLatitude().compareTo(BigDecimal.ZERO) > 0
                && Objects.nonNull(resp.getOrderExtraData().getBuyerAddressLongitude()) && resp.getOrderExtraData().getBuyerAddressLongitude().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
//
//        List<SimpleOrderGrab> finalSimpleOrderGrabList = simpleOrderGrabList.stream().filter(simpleOrderGrab -> Objects.nonNull(simpleOrderGrab.getOrderGrab())
//                && simpleOrderGrab.getOrderGrab().getAppointType() == AppointType.DEFINITE_PRICE.value.intValue()
//                && Objects.nonNull(simpleOrderGrab.getOrderExtraData().getBuyerAddressLatitude()) && simpleOrderGrab.getOrderExtraData().getBuyerAddressLatitude().compareTo(BigDecimal.ZERO) > 0
//                && Objects.nonNull(simpleOrderGrab.getOrderExtraData().getBuyerAddressLongitude()) && simpleOrderGrab.getOrderExtraData().getBuyerAddressLongitude().compareTo(BigDecimal.ZERO) > 0)
//        .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(resplist)) {
            return null;
        }


        //计算省下级地址id
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(provinceNextId,
                "getSideOrderCombinationFromEs",
                JSONUtil.toJsonStr(orderIdList));

        provinceNextIds.remove(99999L);

        if(CollectionUtils.isEmpty(provinceNextIds)){
            return null;
        }

        provinceNextId = provinceNextIds.get(0);



        startTime = System.currentTimeMillis();


        String orderDistanceConfigKey = String.format(RedisKeyConstant.ORDER_DISTANCE_CONFIG_KEY,provinceNextId,3);
        List<OrderDistanceConfig> orderDistanceConfigList = redisHelper.getObjectList(orderDistanceConfigKey, OrderDistanceConfig.class);

        if(CollectionUtils.isEmpty(orderDistanceConfigList)){
            orderDistanceConfigList = orderDistanceConfigRepository.selectByOrderTypeAndDivision(3,provinceNextId);
            if(CollectionUtils.isEmpty(orderDistanceConfigList)){
                return null;
            }
            redisHelper.setObjectList(orderDistanceConfigKey, orderDistanceConfigList, 600);
        }


        endTime = System.currentTimeMillis();
        log.info(String.format("getSideOrderCombinationFromEs orderDistanceConfig take times: %d ms",(endTime - startTime)));



        OrderDistanceConfig orderDistanceConfig = orderDistanceConfigList.stream().filter(config -> config.getDistanceType() ==1).findFirst().orElse(null);
        OrderDistanceConfig masterDistanceConfig = orderDistanceConfigList.stream().filter(config -> config.getDistanceType() ==2).findFirst().orElse(null);

        if(Objects.isNull(orderDistanceConfig) || Objects.isNull(masterDistanceConfig) || orderDistanceConfig.getDistance() < 0 || masterDistanceConfig.getDistance() < 0){
            return null;
        }


        //批量查询师傅和订单推单记录
        List<OrderPush> orderPushList = orderPushResourceGateway.getMasterOrderPush(provinceNextIds, masterId, AppointType.DEFINITE_PRICE.value,sideOrderCombinationOrderPushCnt);

        if(CollectionUtils.isEmpty(orderPushList)){
            return null;
        }

        List<Long> finalOrderIdList = orderPushList.stream().filter(orderPush -> orderIdList.contains(orderPush.getOrderId()) && Objects.nonNull(orderPush.getPushDistance())
                && orderPush.getPushDistance() >= masterDistanceConfig.getDistance()).map(OrderPush::getOrderId).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(finalOrderIdList)){
            return null;
        }

        endTime = System.currentTimeMillis();
        log.info(String.format("getSideOrderCombinationFromEs getMasterOrderPush take times: %d ms",(endTime - startTime)));


        List<Long> definitePriceOrderIdList = orderPushList.stream().map(OrderPush::getOrderId).distinct().collect(Collectors.toList());



        startTime = System.currentTimeMillis();

        String specialOrderCategoryConfigKey = String.format(RedisKeyConstant.SIDE_ORDER_COMBINATION_CATEGORY_KEY,provinceNextId,3);
        SpecialOrderCategoryConfig specialOrderCategoryConfig = redisHelper.getObject(specialOrderCategoryConfigKey, SpecialOrderCategoryConfig.class);

        if(Objects.isNull(specialOrderCategoryConfig)){
            specialOrderCategoryConfig = specialOrderCategoryConfigRepository.selectByOrderTypeAndDivision(3,provinceNextId);
            if(Objects.isNull(specialOrderCategoryConfig)){
                return null;
            }
            redisHelper.setObject(specialOrderCategoryConfigKey, specialOrderCategoryConfig, 600);
        }

        endTime = System.currentTimeMillis();
        log.info(String.format("getSideOrderCombinationFromEs specialOrderCategoryConfig take times: %d ms",(endTime - startTime)));



        for(BatchOrderServiceAttributeInfoResp resp : resplist){


            Long orderId = resp.getOrderBase().getOrderId();

            if(!finalOrderIdList.contains(orderId)){
               continue;
            }

           BigDecimal latitude;
           BigDecimal longitude;


           latitude = resp.getOrderExtraData().getBuyerAddressLatitude();
           longitude = resp.getOrderExtraData().getBuyerAddressLongitude();
           List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = resp.getOrderServiceAttributeInfos();


            try{


                startTime = System.currentTimeMillis();
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

                if(Objects.nonNull(specialOrderCategoryConfig) && StringUtils.isNotBlank(specialOrderCategoryConfig.getCategoryLimit())){
                    if("lv_1_category".equals(specialOrderCategoryConfig.getCategoryLimit())){
                        queryBuilder.filter(QueryBuilders.termQuery("category_id", resp.getOrderBase().getCategoryId()));
                    }else if("lv_2_category".equals(specialOrderCategoryConfig.getCategoryLimit())){
                        Set<Long> lv2CategoryIds = new HashSet<>();
                        orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> {
                            ServiceInfo serviceInfo = JSON.parseObject(orderServiceAttributeInfo.getMasterServiceInfos(), ServiceInfo.class);
                            lv2CategoryIds.add(serviceInfo.getGoodsCategoryId());
                        });
                        if(CollectionUtils.isNotEmpty(lv2CategoryIds)){
                            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                            lv2CategoryIds.forEach(lv2CategoryId -> boolQueryBuilder.should(QueryBuilders.termQuery("lv_2_category_ids", String.valueOf(lv2CategoryId))));
                            boolQueryBuilder.minimumShouldMatch(1);
                            queryBuilder.filter(boolQueryBuilder);
                        }
                    }else if("lv_3_category".equals(specialOrderCategoryConfig.getCategoryLimit())){
                        Set<Long> lv3CategoryIds = new HashSet<>();
                        orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> {
                            ServiceInfo serviceInfo = JSON.parseObject(orderServiceAttributeInfo.getMasterServiceInfos(), ServiceInfo.class);
                            lv3CategoryIds.add(serviceInfo.getGoodsCategoryChildId());
                        });

                        lv3CategoryIds.remove(0L);
                        if(CollectionUtils.isNotEmpty(lv3CategoryIds)){
                            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                            lv3CategoryIds.forEach(lv3CategoryId -> boolQueryBuilder.should(QueryBuilders.termQuery("lv_3_category_ids", String.valueOf(lv3CategoryId))));
                            boolQueryBuilder.minimumShouldMatch(1);
                            queryBuilder.filter(boolQueryBuilder);
                        }


                    }

                }

                definitePriceOrderIdList.remove(orderId);
                if(CollectionUtils.isEmpty(definitePriceOrderIdList)){
                    continue;
                }
                queryBuilder.filter(QueryBuilders.termsQuery("order_id", definitePriceOrderIdList));
                queryBuilder.filter().add(QueryBuilders.geoDistanceQuery("order_location").point(latitude.doubleValue(), longitude.doubleValue()).distance(orderDistanceConfig.getDistance(), DistanceUnit.METERS));
                SearchRequest searchRequest = new SearchRequest("order_data_info_new");
                SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
                searchSourceBuilder.query(queryBuilder);
                searchSourceBuilder.sort("order_id", SortOrder.DESC);
                searchSourceBuilder.from(0);
                searchSourceBuilder.size(sideOrderCombinationOrderCnt);


                searchRequest.source(searchSourceBuilder);

                log.info(String.format("sideOrderCombination searchQuery:%s",searchRequest.toString()));
                SearchResponse searchResponse = restHighLevelClient.search(searchRequest);
                log.info(String.format("getSideOrderCombinationFromEs queryResult:%s",searchResponse.toString()));
                endTime = System.currentTimeMillis();
                log.info(String.format("getSideOrderCombinationFromEs esQuery take times: %d ms",(endTime - startTime)));
                long count = searchResponse.getHits().getTotalHits();
                if(Objects.nonNull(searchResponse.getHits().getHits()) && searchResponse.getHits().getHits().length > 0){

                    List<Long> sideOrderList = new ArrayList<>();
                    for (SearchHit hit : searchResponse.getHits()) {
                        if (hit != null) {
                            String source = hit.getSourceAsString();
                            JSONObject item = JSONObject.parseObject(source);
                            sideOrderList.add(item.getLong("order_id"));
                        }
                    }


                    if(CollectionUtils.isNotEmpty(sideOrderList)){
                        SideOrderCombinationResp sideOrderCombinationResp = new SideOrderCombinationResp();
                        sideOrderCombinationResp.setOrderId(orderId);
                        sideOrderCombinationResp.setSideOrderList(sideOrderList);
                        return sideOrderCombinationResp;
                    }
                }


            }catch(Exception e){
                log.error("getSideOrderCombinationFromEs error",e);
            }

        }

        return null;
    }
}
