package com.wanshifu.domain.special.gateway;

import com.wanshifu.domain.special.model.SpecialOrderCountReqBo;
import com.wanshifu.domain.special.model.SpecialOrderCountRespBo;
import com.wanshifu.infrastructure.MasterOrderDistance;
import com.wanshifu.infrastructure.OrderDistance;
import com.wanshifu.infrastructure.OrderDistanceConditionConfig;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.order.offer.domains.po.OrderGrab;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-02-19 15:00
 * @Description
 * @Version v1
 **/
public interface SpecialOrderGateway {

    /**
     * 获取特殊订单数量
     * @param specialOrderCountReqBo
     * @return
     */
    SpecialOrderCountRespBo getSpecialOrderCount(SpecialOrderCountReqBo specialOrderCountReqBo);

    /**
     * 获取订单附近单，推单记录列表 5条
     *
     * @param provinceNextIds            省下级地址id
     * @param officeOrderId
     * @param masterId
     * @param secondDivisionId
     * @param orderType
     * @param categoryId
     * @param pushDistance
     * @param skipCheckOrderPushDistance
     * @param tmplCityFlag 样板城市订单标识
     * @return
     */
    List<OrderPush> getFilterNearbyOrderPush(List<Long> provinceNextIds, Long officeOrderId, Long masterId,
                                             Long secondDivisionId, Integer orderType, Integer categoryId,
                                             Long pushDistance, boolean skipCheckOrderPushDistance, List<Integer> tmplCityFlag);

    /**
     * 获取订单顺路单 顺路单10条
     * @param officeOrderId
     * @param masterId
     * @param secondDivisionId
     * @return
     */
    List<MasterOrderDistance> getFilterSideOrderPush(Long officeOrderId, Long masterId, Long secondDivisionId);
    /**
     * 获取特殊订单开关
     * @param orderType
     * @return
     */
    public Boolean getSpecialOrderSwitch(Integer orderType);

    /**
     * 定时任务-清理顺路单数据
     * @param fromDayNumber
     * @param queryNumber
     * @param perDeleteNumber
     */
    public Integer clearSideOrder(        int fromDayNumber,
                                          int queryNumber,
                                          int perDeleteNumber);

    /**
     * 定时任务-清理附近单数据
     * @param fromDayNumber
     * @param queryNumber
     * @param perDeleteNumber
     */
    public Integer clearNearbyOrder(        int fromDayNumber,
                                            int queryNumber,
                                            int perDeleteNumber);

    /**
     * 顺路单过滤配置条件
     * @param orderType
     * @param categoryId
     * @param regionId
     * @param pushDistance
     * @return
     */
    public Boolean checkSideOrderDistanceConfig(Integer orderType, Integer categoryId, Long regionId, Long pushDistance);

    /**
     * 首次待报价列表-发送附近单距离
     * @param orderId
     * @return
     */
    Integer nearbyOrderPushDistance(Long orderId);

    /**
     * 计算附近单
     * @param orderBase
     * @param orderGrab
     * @param orderExtraData
     * @param nearByOrderBaseList
     * @param nearByOrderExtraDataMap
     * @return
     */
    Integer insertNearbyOrderList(OrderBase orderBase, OrderGrab orderGrab, OrderExtraData orderExtraData, List<OrderBase> nearByOrderBaseList, Map<Long, OrderExtraData> nearByOrderExtraDataMap);

    OrderDistanceConditionConfig selectByOrderDistanceTypeAndCategoryAndDivision(Integer orderType, Integer categoryId, Long divisionId);

    OrderDistance selectOrderDistanceByOrderId(Long orderId);

    /**
     * 保存订单距离数据
     * @param officeOrderIds
     * @param officeOrderBaseMap
     * @param officeOrderExtraDataMap
     * @param servingOrderBaseMap
     * @param servingOrderExtraDataMap
     * @param orderServeInfoMap
     * @param masterId
     * @param servingOrderIds
     * @param secondDivisionId
     * @return
     */
    Integer batchInsertOrderDistance(List<Long> officeOrderIds, Map<Long, OrderBase> officeOrderBaseMap, Map<Long, OrderExtraData> officeOrderExtraDataMap, Map<Long, OrderBase> servingOrderBaseMap, Map<Long, OrderExtraData> servingOrderExtraDataMap, Map<Long, Long> orderServeInfoMap, Long masterId, List<Long> servingOrderIds, Long secondDivisionId);

    /**
     * 清除附近单列表
     * @param orderId
     */
    Integer clearNearbyOrderList(Long orderId);

    /**
     * 物理删除订单距离
     * @param orderId
     * @return
     */
    Integer softDeleteOrderDistance(Long orderId);

    /**
     * 检查是否满足订单距离配置
     * @param orderType
     * @param regionId
     * @return
     */

    public Boolean checkNearbyOrderDistanceConfig(Integer orderType, Long regionId) ;

}
