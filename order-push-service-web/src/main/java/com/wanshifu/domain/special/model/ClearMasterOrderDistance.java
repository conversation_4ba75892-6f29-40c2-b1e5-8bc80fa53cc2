package com.wanshifu.domain.special.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-01-12 10:52
 * @Description
 * @Version v1
 **/
@Data
public class ClearMasterOrderDistance {

    @NotEmpty
    private List<Long> orderDistanceIds;

    /**
     * 师傅订单距离状态
     *  2-服务终止;3-服务完成
     */
    @NotNull
    private Integer masterOrderDistanceStatus;
}
