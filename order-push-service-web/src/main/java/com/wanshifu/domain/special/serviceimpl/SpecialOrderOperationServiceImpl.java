package com.wanshifu.domain.special.serviceimpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wanshifu.domain.sdk.fulfill.CommonOrderFulfillService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.special.SpecialOrderOperationService;
import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.OrderDistance;
import com.wanshifu.infrastructure.OrderDistanceConditionConfig;
import com.wanshifu.master.order.domains.api.request.fulfillment.GetMasterBatchNewOrderServeListRqt;
import com.wanshifu.master.order.domains.api.response.fulfillment.GetMasterBatchNewOrderServeListResp;
import com.wanshifu.master.order.domains.po.OrderServeInfo;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.OrderBaseSampleComposite;
import com.wanshifu.order.offer.domains.enums.OrderStatus;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.push.enums.PushBusinessCode;
import com.wanshifu.order.push.request.WaitOfferAndServingOrderDistanceCountRqt;
import com.wanshifu.order.push.request.special.ClearSpecialOrderDTO;
import com.wanshifu.order.push.response.WaitOfferAndServingOrderDistanceCountResp;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-02-21 15:41
 * @Description
 * @Version v1
 **/
@Service
public class SpecialOrderOperationServiceImpl implements SpecialOrderOperationService {

    @Resource
    private SpecialOrderGateway specialOrderGateway;

    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    @Resource
    private CommonOrderFulfillService commonOrderFulfillService;

    @Override
    public Integer clearSideOrder(ClearSpecialOrderDTO dto) {
        int fromDayNumber = dto.getFromDayNumber();
        int queryNumber = dto.getQueryNumber();
        int perDeleteNumber = dto.getPerDeleteNumber();
        return specialOrderGateway.clearSideOrder(fromDayNumber,queryNumber,perDeleteNumber);
    }


    @Override
    public Integer clearNearbyOrder(ClearSpecialOrderDTO dto) {
        int fromDayNumber = dto.getFromDayNumber();
        int queryNumber = dto.getQueryNumber();
        int perDeleteNumber = dto.getPerDeleteNumber();
        return specialOrderGateway.clearNearbyOrder(fromDayNumber,queryNumber,perDeleteNumber);
    }

    @Override
    public Integer insertNearbyOrderList(Long orderId) {
        OrderBaseSampleComposite orderBaseSampleComposite = commonOrderOfferService.orderSampleDetailByOrderId(orderId);
        if(Objects.isNull(orderBaseSampleComposite)){
            throw new BusException(PushBusinessCode.MQ_IGNORE_ERROR.code,"订单信息不存在,orderId:" + orderId);
        }

        //条件1:判断订单是否交易中
        OrderBase orderBase = orderBaseSampleComposite.getOrderBase();
        if (ObjectUtil.isNull(orderBase) || !OrderStatus.TRADING.code.equals(orderBase.getOrderStatus())) {
            throw new BusException(PushBusinessCode.MQ_IGNORE_ERROR.code,"订单不在交易中,orderId:" + orderId);
        }
        OrderExtraData orderExtraData = orderBaseSampleComposite.getOrderExtraData();
        if (ObjectUtil.isNull(orderExtraData)) {
            throw new BusException(PushBusinessCode.MQ_IGNORE_ERROR.code, "订单额外信息为空,orderId:" + orderId);
        }
        //条件2:判断订单是否已被指派
        OrderGrab orderGrab = orderBaseSampleComposite.getOrderGrab();
        if (ObjectUtil.isNull(orderGrab) || orderGrab.getConfirmServeStatus() == 1) {
            throw new BusException(PushBusinessCode.MQ_IGNORE_ERROR.code, "订单已雇佣师傅,orderId:" + orderId);
        }

        //条件3:判断订单是否满足距离配置
        OrderDistanceConditionConfig config = specialOrderGateway.selectByOrderDistanceTypeAndCategoryAndDivision(2, null, orderGrab.getSecondDivisionId());
        if (ObjectUtil.isNull(config)) {
            throw new BusException(PushBusinessCode.MQ_IGNORE_ERROR.code,"订单没有附近单配置,orderId:" + orderId);
        }
        //条件3:幂等:已计算不再重新计算
        OrderDistance orderDistance = specialOrderGateway.selectOrderDistanceByOrderId(orderId);
        if (ObjectUtil.isNotNull(orderDistance)) {
            throw new BusException(PushBusinessCode.MQ_IGNORE_ERROR.code,"订单已经计算过附近单,orderId:" + orderId);
        }
        //条件4:查询2天内的附近单
        List<OrderBase> nearByOrderBaseList = commonOrderOfferService.selectNearbyOrderListByThreeDays(orderId, 2);
        if (CollUtil.isEmpty(nearByOrderBaseList)) {
            throw new BusException(PushBusinessCode.MQ_IGNORE_ERROR.code,"订单没有附近单,orderId:" + orderId);
        }
        List<Long> nearByOrderIds = nearByOrderBaseList.stream().map(OrderBase::getOrderId).collect(Collectors.toList());
        //查询1:查询附近订单额外信息
        Map<Long, OrderExtraData> nearByOrderExtraDataMap = commonOrderOfferService.batchGetOrderExtraData(nearByOrderIds).stream().collect(Collectors.toMap(OrderExtraData::getOrderId, Function.identity()));


        return specialOrderGateway.insertNearbyOrderList(orderBase, orderGrab, orderExtraData, nearByOrderBaseList, nearByOrderExtraDataMap);

    }

}
