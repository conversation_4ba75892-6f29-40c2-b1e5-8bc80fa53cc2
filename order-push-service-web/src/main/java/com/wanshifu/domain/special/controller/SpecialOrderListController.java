package com.wanshifu.domain.special.controller;

import com.wanshifu.domain.special.SpecialOrderListService;
import com.wanshifu.order.push.api.SpecialOrderListApi;
import com.wanshifu.order.push.request.special.SideOrderCombinationDTO;
import com.wanshifu.order.push.request.special.SideOrderListDTO;
import com.wanshifu.order.push.request.special.SpecialOrderCountDTO;
import com.wanshifu.order.push.response.special.OrderServeInfoResp;
import com.wanshifu.order.push.response.special.SideOrderCombinationResp;
import com.wanshifu.order.push.response.special.SideOrderCountResp;
import com.wanshifu.order.push.response.special.SideOrderListResp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-02-02 17:00
 * @Description
 * @Version v1
 **/
@RestController
@RequestMapping("/specialOrder/list")
public class SpecialOrderListController implements SpecialOrderListApi {
    @Resource
    private SpecialOrderListService specialOrderListService;


    @PostMapping("sideOrderList")
    @Override
    @Deprecated
    public List<SideOrderListResp> sideOrderList(@Valid @RequestBody SideOrderListDTO dto) {
        return specialOrderListService.sideOrderList(dto);
    }

    @PostMapping("sideOrderListForES")
    @Override
    public List<SideOrderListResp> sideOrderListForES(@Valid @RequestBody SideOrderListDTO dto) {
        return specialOrderListService.sideOrderListFromES(dto);
    }

    @PostMapping("sideOrderCount")
    @Override
    public List<SideOrderCountResp> sideOrderCount(@Valid @RequestBody SpecialOrderCountDTO dto) {
        return specialOrderListService.sideOrderCount(dto);
    }


    @PostMapping("sideOrderCombinationList")
    @Override
    public SideOrderCombinationResp sideOrderCombinationList(@Valid @RequestBody SideOrderCombinationDTO dto) {
        return specialOrderListService.sideOrderCombinationList(dto);
    }
}
