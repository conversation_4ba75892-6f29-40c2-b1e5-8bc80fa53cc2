package com.wanshifu.domain.special;
import com.wanshifu.order.push.request.WaitOfferAndServingOrderDistanceCountRqt;
import com.wanshifu.order.push.request.special.ClearSpecialOrderDTO;
import com.wanshifu.order.push.response.WaitOfferAndServingOrderDistanceCountResp;

/**
 * <AUTHOR>
 * @Date 2024-02-21 15:36
 * @Description
 * @Version v1
 **/
public interface SpecialOrderOperationService {
    /**
     * 清理3天之前拉取的顺路单订单距离历史数据
     * @param dto
     * @return
     */
    public Integer clearSideOrder(ClearSpecialOrderDTO dto);


    /**
     * 清理3天之前附近单数据
     * @param dto
     * @return
     */
    public Integer clearNearbyOrder(ClearSpecialOrderDTO dto);

    /**
     * 计算附近单
     * @param orderId
     * @return
     */
    Integer insertNearbyOrderList(Long orderId);


}
