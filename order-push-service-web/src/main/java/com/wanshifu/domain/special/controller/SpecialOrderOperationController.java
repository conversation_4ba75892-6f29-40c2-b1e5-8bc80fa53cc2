package com.wanshifu.domain.special.controller;

import com.wanshifu.domain.special.SpecialOrderOperationService;
import com.wanshifu.order.offer.domains.api.request.ClearNearbyOrderDistanceRqt;
import com.wanshifu.order.push.api.SpecialOrderOperationApi;
import com.wanshifu.order.push.request.special.ClearSpecialOrderDTO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2024-02-02 17:00
 * @Description
 * @Version v1
 **/
@RestController
@RequestMapping("/specialOrder/operation")
public class SpecialOrderOperationController implements SpecialOrderOperationApi {
    @Resource
    private SpecialOrderOperationService specialOrderOperationService;

    /**
     * 清理3天之前拉取的顺路单订单距离历史数据
     *
     * @param dto
     * @return Integer
     * @interface
     * <AUTHOR>
     */
    @PostMapping("clearSideOrder")
    @Override
    public Integer clearSideOrder(@Valid @RequestBody ClearSpecialOrderDTO dto) {
        return specialOrderOperationService.clearSideOrder(dto);
    }


    /**
     * 清理3天之前报价订单附近单历史数据
     * @param dto
     * @return Integer
     * @interface
     * <AUTHOR>
     */
    @PostMapping("clearNearbyOrder")
    @Override
    public Integer clearNearbyOrder(@Validated @RequestBody ClearSpecialOrderDTO dto) {
        return specialOrderOperationService.clearNearbyOrder(dto);
    }


}
