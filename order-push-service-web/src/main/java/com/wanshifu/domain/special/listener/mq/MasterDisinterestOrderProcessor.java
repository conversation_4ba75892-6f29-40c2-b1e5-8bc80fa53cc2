package com.wanshifu.domain.special.listener.mq;

import com.wanshifu.domain.agent.AgentPushDetailOperateService;
import com.wanshifu.domain.base.handler.AbstractOfferPlatformTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.special.model.MasterDisinterestOrderMessage;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.order.push.request.agent.UpdateAgentByDisinterestOrderRqt;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-08-26 17:17
 * @Description
 * @Version v1
 **/
@Slf4j
@ConsumeTag(value = ConsumeTagEnum.MASTER_DISINTEREST_ORDER, maxReconsumeTime = 3)
public class MasterDisinterestOrderProcessor extends AbstractOfferPlatformTopicMessageTag<MasterDisinterestOrderMessage> {
    @Resource
    private AgentPushDetailOperateService agentPushDetailOperateService;

    @Override
    public void postHandler(MasterDisinterestOrderMessage masterDisinterestOrder) {
        //处理不感兴趣时，清理代理商推单记录
        List<String> orderTags = masterDisinterestOrder.getOrderTags();
        Long orderId = masterDisinterestOrder.getOrderId();
        Long masterId = masterDisinterestOrder.getMasterId();
        Date disinterestTime = masterDisinterestOrder.getDisinterestTime();
        if (CollectionUtils.isNotEmpty(orderTags) &&  orderTags.stream().anyMatch(it -> it.equals("agent"))) {
            UpdateAgentByDisinterestOrderRqt rqt = new UpdateAgentByDisinterestOrderRqt();
            rqt.setOrderId(orderId);
            rqt.setMasterId(masterId);
            rqt.setAbandonTime(disinterestTime);
            agentPushDetailOperateService.disinterestOrder(rqt);
        }
    }
}
