package com.wanshifu.domain.special;

import com.wanshifu.order.push.domains.dto.OrderDistanceDTO;
import com.wanshifu.order.push.request.special.*;
import com.wanshifu.order.push.response.special.GetOrderNearbyNumberResp;
import com.wanshifu.order.push.response.special.SpecialOrderCountResp;

/**
 * <AUTHOR>
 * @Date 2024-02-02 17:31
 * @Description
 * @Version v1
 **/

public interface SpecialOrderResourceService {

    /**
     * 统计特殊订单
     * @param rqt
     * @return
     */
    public SpecialOrderCountResp specialOrderCount(SpecialOrderCountDTO rqt);

    /**
     * 获取附近订单数量
     * @param dto
     * @return
     */
    public GetOrderNearbyNumberResp getOrderNearbyNumber(GetOrderNearbyNumberDTO dto);

    /**
     * 检查是否满足订单距离配置
     * @param checkNearbyOrderDistanceConfigRqt
     * @return
     */
    Boolean checkNearbyOrderDistanceConfig(CheckNearbyOrderDistanceConfigRqt checkNearbyOrderDistanceConfigRqt);

    /**
     * 查询订单距离信息通过订单id
     * @param getOrderDistanceRqt
     * @return
     */
    OrderDistanceDTO selectOrderDistanceByOrderId(GetOrderDistanceRqt getOrderDistanceRqt);

    /**
     * 顺路单过滤配置条件
     * @param checkSideOrderDistanceConfigRqt
     * @return
     */
    Boolean checkSideOrderDistanceConfig(CheckSideOrderDistanceConfigRqt checkSideOrderDistanceConfigRqt);
}
