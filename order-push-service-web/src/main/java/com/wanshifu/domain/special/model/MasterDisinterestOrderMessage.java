package com.wanshifu.domain.special.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-08-26 17:12
 * @Description
 * @Version v1
 **/
@Data
public class MasterDisinterestOrderMessage {
    /**
     * 全局订单ID
     */
    @NotNull
    private Long globalOrderTraceId;

    /**
     * 订单ID
     */
    @NotNull
    private Long orderId;

    /**
     * 订单编号
     */
    @NotEmpty
    private String orderNo;

    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 下单人账号ID
     */
    @NotNull
    private Long accountId;

    /**
     * 下单人账号类型
     */
    @NotEmpty
    private String accountType;

    /**
     * 师傅ID
     */
    @NotNull
    private Long masterId;

    /**
     * 不感兴趣时间
     */
    @NotNull
    private Date disinterestTime;

    /**
     * 订单标签(包含代理商标签)
     */
    private List<String> orderTags;


}
