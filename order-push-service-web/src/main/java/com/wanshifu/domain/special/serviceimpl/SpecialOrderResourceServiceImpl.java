package com.wanshifu.domain.special.serviceimpl;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.special.ability.SpecialOrderAbilityService;
import com.wanshifu.domain.special.model.SpecialOrderCountRespBo.SpecialOrderCount;

import com.wanshifu.domain.special.SpecialOrderResourceService;
import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import com.wanshifu.domain.special.model.SpecialOrderCountReqBo;
import com.wanshifu.domain.special.model.SpecialOrderCountRespBo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DataUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.infrastructure.OrderDistance;
import com.wanshifu.order.offer.domains.api.response.BranchGetOrderPartsResp;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.offer.domains.po.OrderParts;
import com.wanshifu.order.push.domains.dto.OrderDistanceDTO;
import com.wanshifu.order.push.request.special.*;
import com.wanshifu.order.push.response.special.GetOrderNearbyNumberResp;
import com.wanshifu.order.push.response.special.SpecialOrderCountResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-02-02 17:31
 * @Description
 * @Version v1
 **/
@Slf4j
@Service
public class SpecialOrderResourceServiceImpl implements SpecialOrderResourceService {

    @Resource
    private SpecialOrderGateway specialOrderGateway;
    @Resource
    private SpecialOrderAbilityService specialOrderAbilityService;
    @Resource
    private CommonOrderOfferService orderOfferService;

    @Resource
    private CommonAddressService commonAddressService;


    @Override
    public SpecialOrderCountResp specialOrderCount(SpecialOrderCountDTO rqt) {
        SpecialOrderCountResp specialOrderCountResp = new SpecialOrderCountResp();
        //订单距离开关,顺路单/附近单
        if (!specialOrderAbilityService.getSpecialOrderSwitch(2)) {
            return specialOrderCountResp;
        }
        //批量查询订单信息
        List<Long> orderIds = rqt.getOrderIds();
        Long masterId = rqt.getMasterId();
        HashMap<Long, OrderBase> orderBaseMap = new HashMap<>();
        HashMap<Long, OrderGrab> orderGrabMap = new HashMap<>();
        HashMap<Long, List<OrderParts>> orderPartsMap = new HashMap<>();

        List<BranchGetOrderPartsResp> orderPartsRespList = orderOfferService.branchGetOrderPartsList(orderIds);
        if (CollectionUtils.isEmpty(orderPartsRespList)) {
            return specialOrderCountResp;
        }
        orderPartsRespList.forEach(it -> {
            OrderBase orderBase = it.getOrderBase();
            List<OrderParts> orderPartsList = it.getOrderPartsList();
            Long orderId = orderBase.getOrderId();
            orderBaseMap.put(orderId, orderBase);
            orderGrabMap.put(orderId, it.getOrderGrab());
            if (CollectionUtils.isNotEmpty(orderPartsList)) {
                orderPartsMap.put(orderId, orderPartsList);
            }

        });

        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.special.serviceimpl.SpecialOrderResourceServiceImpl.specialOrderCount", JSONUtil.toJsonStr(rqt));

        //样板城市订单二期项目，根据不同师傅角色查询推单
        List<Integer> tmplCityFlag = Lists.newArrayList();
        Integer tmplCityMasterRole = rqt.getTmplCityMasterRole();
        if (Objects.nonNull(tmplCityMasterRole)) {
            if (tmplCityMasterRole == 1) {
                //主力师傅只查样板城市订单
                tmplCityFlag.add(1);
            } else if (tmplCityMasterRole == 3) {
                //普通师傅只查非样板城市订单+样板城市订单转推普通师傅订单
                tmplCityFlag.add(0);
                tmplCityFlag.add(2);
            }
        }

        SpecialOrderCountReqBo specialOrderCountGateway = new SpecialOrderCountReqBo();
        specialOrderCountGateway.setMasterId(masterId);
        specialOrderCountGateway.setOrderIds(orderIds);
        specialOrderCountGateway.setOrderBaseMap(orderBaseMap);
        specialOrderCountGateway.setOrderGrabMapMap(orderGrabMap);
        specialOrderCountGateway.setOrderType(2);
        specialOrderCountGateway.setProvinceNextId(provinceNextId);
        specialOrderCountGateway.setTmplCityFlag(tmplCityFlag);
        SpecialOrderCountRespBo specialOrderCountRespBo = specialOrderGateway.getSpecialOrderCount(specialOrderCountGateway);


        if (Objects.isNull(specialOrderCountRespBo)) {
            return specialOrderCountResp;
        }
        List<SpecialOrderCount> nearbyOrderList = specialOrderCountRespBo.getNearbyOrderList();

        //附近单
        if (CollectionUtils.isNotEmpty(nearbyOrderList)) {
            List<SpecialOrderCountResp.SideOrder> nearbyOrders = nearbyOrderList.stream().map(e -> {
                SpecialOrderCountResp.SideOrder sideOrder = new SpecialOrderCountResp.SideOrder();
                sideOrder.setOrderId(e.getOrderId());
                sideOrder.setNumber(e.getNumber());
                return sideOrder;
            }).collect(Collectors.toList());
            specialOrderCountResp.setNearbyOrderList(nearbyOrders);
        }

        //配件信息
        if (MapUtils.isNotEmpty(orderPartsMap)) {
            ArrayList<SpecialOrderCountResp.PartsInfo> partsInfos = new ArrayList<>();
            orderIds.forEach(it ->{
                List<OrderParts> orderParts = orderPartsMap.get(it);
                if (CollectionUtils.isNotEmpty(orderParts)) {
                    BigDecimal masterPartsPrice = orderParts.stream().map(OrderParts::getMasterPartsPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (masterPartsPrice.compareTo(new BigDecimal(0)) > 0) {
                        SpecialOrderCountResp.PartsInfo partsInfo = new SpecialOrderCountResp.PartsInfo();
                        partsInfo.setOrderId(it);
                        partsInfo.setPartsInfoText("携带配件上门服务可获得" + masterPartsPrice + "元配件收入");
                        partsInfos.add(partsInfo);
                    }
                }

            });
            specialOrderCountResp.setPartsInfoList(partsInfos);
        }
        return specialOrderCountResp;
    }


    @Override
    public GetOrderNearbyNumberResp getOrderNearbyNumber(GetOrderNearbyNumberDTO dto) {
        List<Long> orderIds = dto.getOrderIds();
        Long masterId = dto.getMasterId();
        GetOrderNearbyNumberResp numberResp = new GetOrderNearbyNumberResp();
        HashMap<Long, OrderBase> orderBaseMap = new HashMap<>();
        HashMap<Long, OrderGrab> orderGrabMap = new HashMap<>();

        List<BranchGetOrderPartsResp> orderPartsRespList = orderOfferService.branchGetOrderPartsList(orderIds);
        if (CollectionUtils.isEmpty(orderPartsRespList)) {
            return numberResp;
        }
        orderPartsRespList.forEach(it -> {
            OrderBase orderBase = it.getOrderBase();
            Long orderId = orderBase.getOrderId();
            orderBaseMap.put(orderId, orderBase);
            orderGrabMap.put(orderId, it.getOrderGrab());
        });

        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(dto.getProvinceNextId(),
                "com.wanshifu.domain.special.serviceimpl.SpecialOrderResourceServiceImpl.getOrderNearbyNumber", JSONUtil.toJsonStr(dto));

        //样板城市订单二期项目，根据不同师傅角色查询推单
        List<Integer> tmplCityFlag = Lists.newArrayList();
        Integer tmplCityMasterRole = dto.getTmplCityMasterRole();
        if (Objects.nonNull(tmplCityMasterRole)) {
            if (tmplCityMasterRole == 1) {
                //主力师傅只查样板城市订单
                tmplCityFlag.add(1);
            } else if (tmplCityMasterRole == 3) {
                //普通师傅只查非样板城市订单+样板城市订单转推普通师傅订单
                tmplCityFlag.add(0);
                tmplCityFlag.add(2);
            }
        }

        SpecialOrderCountReqBo specialOrderCountGateway = new SpecialOrderCountReqBo();
        specialOrderCountGateway.setMasterId(masterId);
        specialOrderCountGateway.setOrderIds(orderIds);
        specialOrderCountGateway.setOrderType(2);
        specialOrderCountGateway.setOrderBaseMap(orderBaseMap);
        specialOrderCountGateway.setOrderGrabMapMap(orderGrabMap);
        specialOrderCountGateway.setProvinceNextId(provinceNextId);
        specialOrderCountGateway.setTmplCityFlag(tmplCityFlag);

        SpecialOrderCountRespBo specialOrderCountRespBo = specialOrderGateway.getSpecialOrderCount(specialOrderCountGateway);
        if (Objects.isNull(specialOrderCountRespBo) || CollectionUtils.isEmpty(specialOrderCountRespBo.getNearbyOrderList())){
            return numberResp;
        }
        List<GetOrderNearbyNumberResp.NearbyOrderVo> nearbyOrderVoList = specialOrderCountRespBo.getNearbyOrderList().stream().map(e -> {
            GetOrderNearbyNumberResp.NearbyOrderVo nearbyOrderVo = new GetOrderNearbyNumberResp.NearbyOrderVo();
            nearbyOrderVo.setOrderId(e.getOrderId());
            nearbyOrderVo.setNearbyNumber(e.getNumber());
            return nearbyOrderVo;
        }).collect(Collectors.toList());
        numberResp.setNearbyOrderList(nearbyOrderVoList);
        return numberResp;
    }

    @Override
    public Boolean checkNearbyOrderDistanceConfig(CheckNearbyOrderDistanceConfigRqt checkNearbyOrderDistanceConfigRqt) {
        return specialOrderGateway.checkNearbyOrderDistanceConfig(checkNearbyOrderDistanceConfigRqt.getOrderType(), checkNearbyOrderDistanceConfigRqt.getRegionId());
    }

    @Override
    public Boolean checkSideOrderDistanceConfig(CheckSideOrderDistanceConfigRqt checkSideOrderDistanceConfigRqt) {
        return specialOrderGateway.checkSideOrderDistanceConfig(checkSideOrderDistanceConfigRqt.getOrderType(), checkSideOrderDistanceConfigRqt.getCategoryId(), checkSideOrderDistanceConfigRqt.getRegionId(), checkSideOrderDistanceConfigRqt.getPushDistance());
    }

    @Override
    public OrderDistanceDTO selectOrderDistanceByOrderId(GetOrderDistanceRqt getOrderDistanceRqt) {
        OrderDistance orderDistance = specialOrderGateway.selectOrderDistanceByOrderId(getOrderDistanceRqt.getOrderId());
        if(Objects.nonNull(orderDistance)){
            return DataUtils.copyObject(orderDistance, OrderDistanceDTO.class);
        }
        return null;
    }
}
