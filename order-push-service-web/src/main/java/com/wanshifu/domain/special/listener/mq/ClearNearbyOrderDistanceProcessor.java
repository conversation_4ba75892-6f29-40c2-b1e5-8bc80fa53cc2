package com.wanshifu.domain.special.listener.mq;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wanshifu.domain.base.handler.AbstractOfferPlatformTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import com.wanshifu.domain.special.model.ClearNearbyOrderDistance;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.order.push.enums.PushBusinessCode;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023-08-26 17:17
 * @Description
 * @Version v1
 **/
@Slf4j
@ConsumeTag(value = ConsumeTagEnum.CLEAR_NEAR_BY_ORDER_DISTANCE , isSupportLog = false ,  maxReconsumeTime = 3)
public class ClearNearbyOrderDistanceProcessor extends AbstractOfferPlatformTopicMessageTag<ClearNearbyOrderDistance> {

    @Resource
    private SpecialOrderGateway specialOrderManager;

    @Override
    public void postHandler(ClearNearbyOrderDistance clearNearbyOrderDistance) {
        Entry entry = null;
        try {
            entry = SphU.entry(ConsumeTagEnum.CLEAR_NEAR_BY_ORDER_DISTANCE.value, EntryType.IN);
            Long orderId = clearNearbyOrderDistance.getOrderId();
            specialOrderManager.clearNearbyOrderList(orderId);
        } catch (BlockException e) {
            log.warn("mq消费:tag:{}, 内容:{},已触发限流策略", ConsumeTagEnum.CLEAR_NEAR_BY_ORDER_DISTANCE.value, ConsumeTagEnum.CLEAR_NEAR_BY_ORDER_DISTANCE.describe);
            throw new BusException(PushBusinessCode.BUS_VALIDATION_ERROR.code,String.format("处理 %s mq消费，限流请求超时",ConsumeTagEnum.CLEAR_NEAR_BY_ORDER_DISTANCE.describe));
        } finally {
            if (null != entry) {
                entry.close();
            }
        }
    }
}
