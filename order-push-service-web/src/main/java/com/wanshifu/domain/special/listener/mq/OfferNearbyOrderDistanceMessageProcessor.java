package com.wanshifu.domain.special.listener.mq;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wanshifu.domain.base.handler.AbstractOfferPlatformTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.special.SpecialOrderOperationService;
import com.wanshifu.domain.special.model.ClearNearbyOrderDistance;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.order.push.enums.PushBusinessCode;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023-09-22 15:08
 * @Description Offer服务，报价、查询已报价订单，记录附近单距离信息
 * @Version v1
 **/
@Slf4j
@ConsumeTag(value = ConsumeTagEnum.NEARBY_ORDER_DISTANCE, isSupportLog = false, maxReconsumeTime = 3)
public class OfferNearbyOrderDistanceMessageProcessor extends AbstractOfferPlatformTopicMessageTag<ClearNearbyOrderDistance> {

    @Resource
    private SpecialOrderOperationService specialOrderOperationService;

    @Override
    public void postHandler(ClearNearbyOrderDistance clearNearbyOrderDistance) {
        Entry entry = null;
        try {
            entry = SphU.entry(ConsumeTagEnum.NEARBY_ORDER_DISTANCE.value, EntryType.IN);
            specialOrderOperationService.insertNearbyOrderList(clearNearbyOrderDistance.getOrderId());
        } catch (BlockException e) {
            log.warn("mq消费:tag:{}, 内容:{},已触发限流策略", ConsumeTagEnum.NEARBY_ORDER_DISTANCE.value, ConsumeTagEnum.NEARBY_ORDER_DISTANCE.describe);
            throw new BusException(PushBusinessCode.BUS_VALIDATION_ERROR.code,String.format("处理 %s mq消费，限流请求超时",ConsumeTagEnum.NEARBY_ORDER_DISTANCE.describe));
        } finally {
            if (null != entry) {
                entry.close();
            }
        }
    }
}
