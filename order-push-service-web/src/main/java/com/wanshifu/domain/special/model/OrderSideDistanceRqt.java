package com.wanshifu.domain.special.model;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.order.offer.domains.po.OrderBase;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 描述 :
 *
 * <AUTHOR> 86136
 * @date : 2023/3/28 9:46
 */
@Data
public class OrderSideDistanceRqt {

    /**
     * 师傅id
     */
    @NotNull
    @Min(0L)
    private Long userId;

    /**
     * 类型
     */
    private String extraType;

    /**
     * 类型id集合
     */
    @NotNull
    private List<Long> extraIds;

    /**
     * 是否拉取订单距离
     */
    @NotNull
    @ValueIn("1,0")
    private Integer isPullOrderDistance;

    /**
     * 订单基本信息
     */
    private List<OrderBase> orderBaseList;

    /**
     * 操作时间
     */
    private Date executeTime;
}
