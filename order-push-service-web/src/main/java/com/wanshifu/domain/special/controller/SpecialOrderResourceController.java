package com.wanshifu.domain.special.controller;

import com.wanshifu.domain.special.SpecialOrderResourceService;
import com.wanshifu.order.push.api.SpecialOrderResourceApi;
import com.wanshifu.order.push.domains.dto.OrderDistanceDTO;
import com.wanshifu.order.push.request.special.*;
import com.wanshifu.order.push.response.special.GetOrderNearbyNumberResp;
import com.wanshifu.order.push.response.special.SpecialOrderCountResp;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2024-02-02 17:00
 * @Description
 * @Version v1
 **/
@RestController
@RequestMapping("/specialOrder/resource")
public class SpecialOrderResourceController implements SpecialOrderResourceApi {


    @Resource
    private SpecialOrderResourceService specialOrderResourceService;


    /**
     * 特殊订单统计（包含顺路单、附近单）
     * @param rqt
     * @return AsyncNewWaitOfferResp
     */
    @PostMapping("count")
    @Override
    public SpecialOrderCountResp specialOrderCount(@RequestBody @Validated SpecialOrderCountDTO rqt) {
        return specialOrderResourceService.specialOrderCount(rqt);
    }


    /**
     * 获取报价订单附近单数量
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("getOrderNearbyNumber")
    public GetOrderNearbyNumberResp getOrderNearbyNumber(@Valid @RequestBody GetOrderNearbyNumberDTO rqt) {
        return specialOrderResourceService.getOrderNearbyNumber(rqt);
    }

    /**
     * 检查是否满足订单距离配置
     * @param checkNearbyOrderDistanceConfigRqt
     * @return
     */
    @PostMapping("checkNearbyOrderDistanceConfig")
    @Override
    public Boolean checkNearbyOrderDistanceConfig(@Valid @RequestBody CheckNearbyOrderDistanceConfigRqt checkNearbyOrderDistanceConfigRqt) {
        return specialOrderResourceService.checkNearbyOrderDistanceConfig(checkNearbyOrderDistanceConfigRqt);
    }

    /**
     * 顺路单过滤配置条件
     * @param checkSideOrderDistanceConfigRqt
     * @return
     */
    @PostMapping("checkSideOrderDistanceConfig")
    @Override
    public Boolean checkSideOrderDistanceConfig(@Valid @RequestBody CheckSideOrderDistanceConfigRqt checkSideOrderDistanceConfigRqt) {
        return specialOrderResourceService.checkSideOrderDistanceConfig(checkSideOrderDistanceConfigRqt);
    }

    /**
     * 查询订单距离信息通过订单id
     * @param getOrderDistanceRqt
     * @return
     */
    @PostMapping("selectOrderDistanceByOrderId")
    @Override
    public OrderDistanceDTO selectOrderDistanceByOrderId(@Valid @RequestBody GetOrderDistanceRqt getOrderDistanceRqt) {
        return specialOrderResourceService.selectOrderDistanceByOrderId(getOrderDistanceRqt);
    }
}
