package com.wanshifu.domain.special;

import com.wanshifu.order.push.request.special.GetOrderNearbyNumberDTO;
import com.wanshifu.order.push.request.special.SideOrderCombinationDTO;
import com.wanshifu.order.push.request.special.SideOrderListDTO;
import com.wanshifu.order.push.request.special.SpecialOrderCountDTO;
import com.wanshifu.order.push.response.special.*;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-02-02 17:31
 * @Description
 * @Version v1
 **/

public interface SpecialOrderListService {

    /**
     * 查询顺路单列表
     * @param rqt
     * @return
     */
    public List<SideOrderListResp> sideOrderList(SideOrderListDTO rqt);

    /**
     * 查询一个订单的顺路单列表，返回服务中的顺路的服务订单信息
     * @param rqt
     * @return
     */
    List<SideOrderListResp> sideOrderListFromES(SideOrderListDTO rqt);

    List<SideOrderCountResp> sideOrderCount(SpecialOrderCountDTO rqt);


    SideOrderCombinationResp sideOrderCombinationList(SideOrderCombinationDTO rqt);


    }
