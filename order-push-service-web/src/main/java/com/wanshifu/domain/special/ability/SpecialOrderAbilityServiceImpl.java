package com.wanshifu.domain.special.ability;

import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024-02-02 17:37
 * @Description
 * @Version v1
 **/
@Component
public class SpecialOrderAbilityServiceImpl implements SpecialOrderAbilityService {

    @Resource
    private SpecialOrderGateway specialOrderGateway;


    /**
     * 特殊单开关,顺路单:2,附近单:3,全开:1,0:全关
     *
     * @param orderType 1:顺路单,2:附近单,0:全部
     * @return
     */
    public Boolean getSpecialOrderSwitch(Integer orderType) {
        return specialOrderGateway.getSpecialOrderSwitch(orderType);
    }


}
