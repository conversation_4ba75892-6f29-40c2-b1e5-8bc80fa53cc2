package com.wanshifu.domain.special.model;

import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024-02-19 15:02
 * @Description
 * @Version v1
 **/
@Data
public class SpecialOrderCountReqBo {

    private Long masterId;
    private List<Long> orderIds;
    private Map<Long, OrderBase> orderBaseMap;
    private Map<Long, OrderGrab> orderGrabMapMap;

    /**
     * 订单类型 0-全部 1-顺路单 2-附近单
     */
    private Integer orderType = 0;

    /**
     * 省下级地址id
     */
    private List<Long> provinceNextId;

    /**
     * 样板城市订单标识
     * 0：非样板城市订单，1：样板城市订单，2：样板城市订单转推普通师傅，默认0
     */
    private List<Integer> tmplCityFlag;
}

