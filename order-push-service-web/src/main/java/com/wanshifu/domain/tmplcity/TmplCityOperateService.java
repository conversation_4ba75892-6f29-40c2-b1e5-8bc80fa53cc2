package com.wanshifu.domain.tmplcity;

import com.wanshifu.order.push.request.tmplcity.SaveTmplCityOrderRecommendMasterRqt;

/**
 * <AUTHOR>
 * @date 2024/6/25 16:48
 */
public interface TmplCityOperateService {

    /**
     * 新增样板城市订单推荐师傅
     * @param rqt SaveTmplCityOrderRecommendMasterRqt
     */
    void saveTmplCityOrderRecommendMaster(SaveTmplCityOrderRecommendMasterRqt rqt);

    /**
     * 清除样板城市订单推荐师傅数据
     * @param globalOrderTraceId 全局订单id
     */
    void clearTmplCityOrderRecommendMaster(Long globalOrderTraceId);
}
