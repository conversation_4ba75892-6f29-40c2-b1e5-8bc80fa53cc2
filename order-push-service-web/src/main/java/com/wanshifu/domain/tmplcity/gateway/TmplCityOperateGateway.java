package com.wanshifu.domain.tmplcity.gateway;

import com.wanshifu.domain.tmplcity.bo.SaveTmplCityOrderRecommendMasterRqtBo;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/6/25 16:56
 */
public interface TmplCityOperateGateway {

    /**
     * 新增样板城市订单推荐师傅
     * @param rqtBo SaveTmplCityOrderRecommendMasterRqtBo
     */
    void saveTmplCityOrderRecommendMaster(SaveTmplCityOrderRecommendMasterRqtBo rqtBo);

    /**
     * 根据全局订单id删除样板城市订单推荐师傅数据
     * @param globalOrderTraceId 全局订单id
     */
    void deleteTmplCityOrderRecommendMasterByGlobalOrderTraceId(Long globalOrderTraceId);


    /**
     * 根据全局订单id删除样板城市订单推荐师傅数据
     * @param globalOrderTraceId 全局订单id
     */
    void updateIsPush(Long globalOrderTraceId, List<Long> masterId);
}
