package com.wanshifu.domain.tmplcity.listener.mq.message;

import com.wanshifu.order.push.request.push.MasterAddressInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 样板城市订单推荐师傅message
 * @date 2024/6/26 11:03
 */
@Data
public class RecommendMatchResultMessage {

    @NotNull
    @Min(1L)
    private Long orderId;

    /**
     * 全局id
     */
    private Long globalOrderTraceId;

    /**
     * 推送师傅信息
     */
    @Valid
    @NotEmpty
    private List<MasterAddressInfo> masterAddressInfoList;

}
