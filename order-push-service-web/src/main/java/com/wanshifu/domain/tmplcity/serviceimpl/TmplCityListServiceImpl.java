package com.wanshifu.domain.tmplcity.serviceimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanshifu.domain.tmplcity.TmplCityListService;
import com.wanshifu.domain.tmplcity.bo.*;
import com.wanshifu.domain.tmplcity.gateway.TmplCityListGateway;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderPushLogRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqtV2;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderPushLogResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterRespV2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 样板城市查询接口实现
 * @date 2024/6/25 14:26
 */
@Service
public class TmplCityListServiceImpl implements TmplCityListService {

    @Resource
    private TmplCityListGateway tmplCityListGateway;

    @Override
    public SimplePageInfo<ListTmplCityOrderRecommendMasterResp> orderRecommendMasterList(ListTmplCityOrderRecommendMasterRqt rqt) {
        if (Objects.isNull(rqt) || Objects.isNull(rqt.getGlobalOrderTraceId())) {
            return new SimplePageInfo<>();
        }

        ListTmplCityOrderRecommendMasterRqtBo rqtBo = BeanUtil.toBean(rqt, ListTmplCityOrderRecommendMasterRqtBo.class);

        //分页查询推荐师傅数据
        SimplePageInfo<ListTmplCityOrderRecommendMasterRespBo> simplePageInfo = tmplCityListGateway.orderRecommendMasterList(rqtBo);
        if (Objects.isNull(simplePageInfo) || CollectionUtils.isEmpty(simplePageInfo.getList())) {
            return new SimplePageInfo<>();
        }

        List<ListTmplCityOrderRecommendMasterResp> listTmplCityOrderRecommendMasterRespList = simplePageInfo.getList().stream()
                .map(tmplCityOrderRecommendMasterRespBo -> BeanUtil.toBean(tmplCityOrderRecommendMasterRespBo, ListTmplCityOrderRecommendMasterResp.class))
                .collect(Collectors.toList());

        SimplePageInfo<ListTmplCityOrderRecommendMasterResp> respSimplePageInfo = new SimplePageInfo<>(listTmplCityOrderRecommendMasterRespList);
        respSimplePageInfo.setPages(simplePageInfo.getPages());
        respSimplePageInfo.setPageSize(simplePageInfo.getPageSize());
        respSimplePageInfo.setPageNum(simplePageInfo.getPageNum());
        respSimplePageInfo.setTotal(simplePageInfo.getTotal());
        return respSimplePageInfo;
    }

    @Override
    public List<ListTmplCityOrderRecommendMasterRespV2> orderRecommendMasterListV2(ListTmplCityOrderRecommendMasterRqtV2 rqt) {
        ListTmplCityOrderRecommendMasterRqtBoV2 rqtBo = BeanUtil.toBean(rqt, ListTmplCityOrderRecommendMasterRqtBoV2.class);
        List<ListTmplCityOrderRecommendMasterRespBoV2> respBoList = tmplCityListGateway.orderRecommendMasterListV2(rqtBo);
        if (CollectionUtil.isEmpty(respBoList)) {
            return new ArrayList<>();
        }
        return respBoList.stream()
                .map(respBo -> BeanUtil.toBean(respBo, ListTmplCityOrderRecommendMasterRespV2.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<ListTmplCityOrderPushLogResp> orderPushLogList(ListTmplCityOrderPushLogRqt rqt) {
        if (Objects.isNull(rqt) || Objects.isNull(rqt.getOrderId())) {
            return new ArrayList<>();
        }

        ListTmplCityOrderPushLogRqtBo rqtBo = BeanUtil.toBean(rqt, ListTmplCityOrderPushLogRqtBo.class);
        List<ListTmplCityOrderPushLogRespBo> list = tmplCityListGateway.orderPushLogList(rqtBo);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(listTmplCityOrderPushLogRespBo -> BeanUtil.toBean(listTmplCityOrderPushLogRespBo, ListTmplCityOrderPushLogResp.class))
                .collect(Collectors.toList());
    }
}
