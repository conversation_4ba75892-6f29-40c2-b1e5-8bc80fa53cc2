package com.wanshifu.domain.tmplcity.listener.mq;


import cn.hutool.json.JSONUtil;
import com.wanshifu.domain.base.handler.AbstractCommonOrderGeneralTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.tmplcity.TmplCityOperateService;
import com.wanshifu.domain.tmplcity.listener.mq.message.MasterServeFinishMessage;
import com.wanshifu.infrastructure.repository.InterfereOrderPushRepository;
import com.wanshifu.infrastructure.repository.NoOfferFilterMasterRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 师傅服务完工消息消费
 * @date 2024/6/26 10:46
 */
@ConsumeTag(value = ConsumeTagEnum.MASTER_SERVE_FINISH, maxReconsumeTime = 16)
@Slf4j
public class MasterServeFinishMessageProcessor extends AbstractCommonOrderGeneralTopicMessageTag<MasterServeFinishMessage> {


    @Resource
    private TmplCityOperateService tmplCityOperateService;

    @Resource
    private NoOfferFilterMasterRepository noOfferFilterMasterRepository;

    @Override
    public void postHandler(MasterServeFinishMessage masterServeFinishMessage) {
        log.info("master_serveFinish consume,params:{}", JSONUtil.toJsonStr(masterServeFinishMessage));

        Long globalOrderTraceId = masterServeFinishMessage.getGlobalOrderTraceId();
        if (Objects.isNull(globalOrderTraceId) || globalOrderTraceId == 0L) {
            log.error("master_serveFinish consume error, globalOrderTraceId is null or 0L!");
            return;
        }

        //根据全局订单id清理样板城市订单推荐师傅数据
        tmplCityOperateService.clearTmplCityOrderRecommendMaster(globalOrderTraceId);

        //师傅服务完工清理无人报价推荐合适师傅数据
        noOfferFilterMasterRepository.deleteByGlobalOrderId(globalOrderTraceId);

    }
}
