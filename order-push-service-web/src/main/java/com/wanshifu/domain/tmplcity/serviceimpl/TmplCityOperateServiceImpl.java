package com.wanshifu.domain.tmplcity.serviceimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanshifu.domain.tmplcity.TmplCityOperateService;
import com.wanshifu.domain.tmplcity.bo.SaveTmplCityOrderRecommendMasterRqtBo;
import com.wanshifu.domain.tmplcity.gateway.TmplCityOperateGateway;
import com.wanshifu.order.push.request.tmplcity.SaveTmplCityOrderRecommendMasterRqt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 16:48
 */
@Service
public class TmplCityOperateServiceImpl implements TmplCityOperateService {

    @Resource
    private TmplCityOperateGateway tmplCityOperateGateway;

    @Override
    public void saveTmplCityOrderRecommendMaster(SaveTmplCityOrderRecommendMasterRqt rqt) {
        if (Objects.isNull(rqt) || CollectionUtil.isEmpty(rqt.getRecommendMasterInfoList())) {
            return;
        }
        if (Objects.isNull(rqt.getGlobalOrderTraceId()) || rqt.getGlobalOrderTraceId() == 0L) {
            return;
        }

        tmplCityOperateGateway.saveTmplCityOrderRecommendMaster(BeanUtil.toBean(rqt, SaveTmplCityOrderRecommendMasterRqtBo.class));
    }

    @Override
    public void clearTmplCityOrderRecommendMaster(Long globalOrderTraceId) {
        if (Objects.isNull(globalOrderTraceId) || globalOrderTraceId == 0L) {
            return;
        }
        tmplCityOperateGateway.deleteTmplCityOrderRecommendMasterByGlobalOrderTraceId(globalOrderTraceId);
    }
}
