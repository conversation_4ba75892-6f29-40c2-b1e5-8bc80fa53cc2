package com.wanshifu.domain.tmplcity.bo;

import com.wanshifu.order.push.enums.TmplCityOrderPushLogEventEnum;
import com.wanshifu.order.push.enums.TmplCityOrderPushLogOperatorTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/29 18:00
 */
@Data
public class ListTmplCityOrderPushLogRespBo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * {@link TmplCityOrderPushLogEventEnum}
     * 操作事件，push_main_and_reserve:推送主力和储备师傅，push_normal:推送众包师傅
     */
    private String pushEvent;

    /**
     * {@link TmplCityOrderPushLogOperatorTypeEnum}
     * 操作人类型，system:系统
     */
    private String operatorType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 主力师傅ids,以英文逗号分隔
     */
    private String mainMasterIds;

    /**
     * 储备师傅ids,以英文逗号分隔
     */
    private String reserveMasterIds;

    /**
     * 众包师傅ids,以英文逗号分隔
     */
    private String normalMasterIds;

    /**
     * 操作时间
     */
    private Date createTime;
}
