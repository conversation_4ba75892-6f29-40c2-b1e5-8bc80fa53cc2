package com.wanshifu.domain.tmplcity;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderPushLogRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqtV2;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderPushLogResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterRespV2;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25 14:25
 */
public interface TmplCityListService {

    /**
     * 分页获取家庭样板城市订单推荐师傅列表
     * @param rqt ListTmplCityOrderRecommendMasterRqt
     * @return SimplePageInfo<ListTmplCityOrderRecommendMasterResp>
     */
    SimplePageInfo<ListTmplCityOrderRecommendMasterResp> orderRecommendMasterList(ListTmplCityOrderRecommendMasterRqt rqt);

    /**
     * 获取家庭样板城市订单推荐师傅列表，按sort倒序，限制返回1000条
     * @param rqt ListTmplCityOrderRecommendMasterRqtV2
     * @return List<ListTmplCityOrderRecommendMasterRespV2>
     */
    List<ListTmplCityOrderRecommendMasterRespV2> orderRecommendMasterListV2(ListTmplCityOrderRecommendMasterRqtV2 rqt);

    /**
     * 获取家庭样板城市订单推单记录列表
     * @param rqt
     * @return
     */
    List<ListTmplCityOrderPushLogResp> orderPushLogList(ListTmplCityOrderPushLogRqt rqt);
}
