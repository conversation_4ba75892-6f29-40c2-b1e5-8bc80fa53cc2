package com.wanshifu.domain.tmplcity.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/3 14:35
 */
@Data
public class ListTmplCityOrderRecommendMasterRqtBoV2 {

    /**
     * 全局订单id
     */
    private Long globalOrderTraceId;

    /**
     * 返回数量限制，1~1000
     */
    private Integer returnLimit;

    /**
     * 排序类型，1: 降序，2: 升序, 默认按评分降序
     */
    private Integer sortType;

    /**
     * 是否已推單，1：已推單，0：未推單
     */
    private Integer isPush;

    /**
     * 师傅来源类型，tob: B端师傅，toc: C端师傅
     */
    private String masterSourceType;
}
