package com.wanshifu.domain.tmplcity.listener.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.wanshifu.domain.base.handler.AbstractOfferPlatformTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.tmplcity.TmplCityOperateService;
import com.wanshifu.domain.tmplcity.listener.mq.message.OrderCloseMessage;
import com.wanshifu.infrastructure.repository.InterfereOrderPushRepository;
import com.wanshifu.infrastructure.repository.NoOfferFilterMasterRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 订单关单消息消费
 * @date 2024/6/26 11:21
 */
@ConsumeTag(value = ConsumeTagEnum.ORDER_CLOSE, maxReconsumeTime = 16)
@Slf4j
public class OrderCloseMessageProcessor extends AbstractOfferPlatformTopicMessageTag<OrderCloseMessage> {

    @Resource
    private TmplCityOperateService tmplCityOperateService;

    @Resource
    private NoOfferFilterMasterRepository noOfferFilterMasterRepository;

    @Resource
    private InterfereOrderPushRepository interfereOrderPushRepository;

    /**
     * 干预推单记录批次删除开关
     */
    @Value("${interfere.orderPush.batchDelete.switch:on}")
    private String interfereOrderPushBatchDeleteSwitch;


    /**
     * 干预推单记录批次删除大小
     */
    @Value("${interfere.orderPush.batchDelete.size:100}")
    private int interfereOrderPushBatchDeleteSize;


    @Override
    public void postHandler(OrderCloseMessage orderCloseMessage) {
        log.info("order_close consume, params:{}", JSONUtil.toJsonStr(orderCloseMessage));

        List<Long> globalOrderTraceIdList = orderCloseMessage.getGlobalOrderTraceIdList();
        if (CollectionUtil.isEmpty(globalOrderTraceIdList)) {
            log.error("order_close consume error, globalOrderTraceIdList is empty!");
            return;
        }

        //根据全局订单id清理样板城市订单推荐师傅数据
        globalOrderTraceIdList.forEach(globalOrderTraceId -> tmplCityOperateService.clearTmplCityOrderRecommendMaster(globalOrderTraceId));

        //订单关单清理无人报价推荐合适师傅数据
        noOfferFilterMasterRepository.deleteByGlobalOrderIds(globalOrderTraceIdList);

        //删除平台干预推单记录
        globalOrderTraceIdList.forEach(globalOrderTraceId -> {
            if("on".equals(interfereOrderPushBatchDeleteSwitch)){
                interfereOrderPushRepository.deleteByGlobalOrderTraceIdBatch(globalOrderTraceId,interfereOrderPushBatchDeleteSize);
            }else{
                interfereOrderPushRepository.deleteByGlobalOrderTraceId(globalOrderTraceId);
            }
        });



    }
}
