package com.wanshifu.domain.tmplcity.controller;

import com.wanshifu.domain.tmplcity.TmplCityOperateService;
import com.wanshifu.order.push.request.tmplcity.SaveTmplCityOrderRecommendMasterRqt;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 17:14
 */
@RestController
@RequestMapping("/tmplCity/operate")
public class TmplCityOperateController {

    @Resource
    private TmplCityOperateService tmplCityOperateService;


    @PostMapping("/saveTmplCityOrderRecommendMaster")
    public void saveTmplCityOrderRecommendMaster(@Valid @RequestBody SaveTmplCityOrderRecommendMasterRqt rqt) {
        tmplCityOperateService.saveTmplCityOrderRecommendMaster(rqt);
    }
}
