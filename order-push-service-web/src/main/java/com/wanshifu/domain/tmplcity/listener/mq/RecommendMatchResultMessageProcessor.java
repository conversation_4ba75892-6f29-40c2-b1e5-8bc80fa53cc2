package com.wanshifu.domain.tmplcity.listener.mq;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.wanshifu.domain.base.handler.AbstractMatchMasterTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.tmplcity.TmplCityOperateService;
import com.wanshifu.domain.tmplcity.listener.mq.message.RecommendMatchResultMessage;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.push.request.push.MasterAddressInfo;
import com.wanshifu.order.push.request.tmplcity.SaveTmplCityOrderRecommendMasterRqt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 推单的推荐师傅通知,家庭样板城市项目后台调度手动指派师傅功能新加
 * @date 2024/6/25 11:16
 */
@ConsumeTag(value = ConsumeTagEnum.RECOMMEND_MATCH_RESULT, maxReconsumeTime = 6)
@Slf4j
public class RecommendMatchResultMessageProcessor extends AbstractMatchMasterTopicMessageTag<RecommendMatchResultMessage> {

    @Resource
    private TmplCityOperateService tmplCityOperateService;

    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    @Resource
    private CommonAddressService commonAddressService;

    /**
     * 样板城市订单推单推荐师傅数量
     */
    @Value("${tmplCity.order.recommend.master.count:100}")
    private int tmplCityOrderRecommendMasterCount;

    @Override
    public void postHandler(RecommendMatchResultMessage recommendMatchResultMessage) {
        log.info("tmplCity recommendMatchResult,params:{}", JSONUtil.toJsonStr(recommendMatchResultMessage));
        Long globalOrderTraceId = recommendMatchResultMessage.getGlobalOrderTraceId();
        Long orderId = recommendMatchResultMessage.getOrderId();
        List<MasterAddressInfo> masterAddressInfoList = recommendMatchResultMessage.getMasterAddressInfoList();
        if (Objects.isNull(globalOrderTraceId) || globalOrderTraceId == 0L) {
            log.error("tmplCity recommendMatchResult error,globalOrderTraceId is null or 0L!");
            return;
        }
        if (CollectionUtil.isEmpty(masterAddressInfoList)) {
            log.error("tmplCity recommendMatchResult error, recommendMaster is empty!");
            return;
        }
        if (masterAddressInfoList.size() > tmplCityOrderRecommendMasterCount) {
            log.error("tmplCity recommendMatchResult error, recommendMaster over {}!", tmplCityOrderRecommendMasterCount);
            throw new BusException("推荐师傅数量超过数量限制！");
        }
        SaveTmplCityOrderRecommendMasterRqt saveTmplCityOrderRecommendMasterRqt = new SaveTmplCityOrderRecommendMasterRqt();
        saveTmplCityOrderRecommendMasterRqt.setGlobalOrderTraceId(globalOrderTraceId);
        saveTmplCityOrderRecommendMasterRqt.setOrderId(orderId);
        List<SaveTmplCityOrderRecommendMasterRqt.RecommendMasterInfo> recommendMasterInfoList = masterAddressInfoList.stream()
                .map(masterAddressInfo -> BeanUtil.toBean(masterAddressInfo, SaveTmplCityOrderRecommendMasterRqt.RecommendMasterInfo.class))
                .collect(Collectors.toList());
        saveTmplCityOrderRecommendMasterRqt.setRecommendMasterInfoList(recommendMasterInfoList);

        OrderBaseComposite orderBaseComposite = commonOrderOfferService.getOrderBaseComposite(orderId);
        Long thirdDivisionId = orderBaseComposite.getOrderBase().getThirdDivisionId();

        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(thirdDivisionId,ConsumeTagEnum.RECOMMEND_MATCH_RESULT.getValue(),
                JSONUtil.toJsonStr(recommendMatchResultMessage));

        saveTmplCityOrderRecommendMasterRqt.setProvinceNextId(provinceNextId);

        tmplCityOperateService.saveTmplCityOrderRecommendMaster(saveTmplCityOrderRecommendMasterRqt);
    }
}
