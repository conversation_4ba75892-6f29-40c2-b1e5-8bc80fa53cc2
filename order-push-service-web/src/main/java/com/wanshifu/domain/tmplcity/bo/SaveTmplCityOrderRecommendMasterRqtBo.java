package com.wanshifu.domain.tmplcity.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 16:55
 */
@Data
public class SaveTmplCityOrderRecommendMasterRqtBo {

    /**
     * 全局订单id
     */
    private Long globalOrderTraceId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 推荐师傅列表
     */
    private List<RecommendMasterInfo> recommendMasterInfoList;


    private List<Long> provinceNextId;

    @Data
    public static class RecommendMasterInfo {

        /**
         * 师傅id
         */
        private Long masterId;

        /**
         * 该订单师傅排序值
         */
        private Integer sort;

        /**
         * 排序分值
         */
        private BigDecimal score;

        /**
         * 师傅来源类型，tob: B端师傅，toc: C端师傅
         */
        private String masterSourceType;
    }
}
