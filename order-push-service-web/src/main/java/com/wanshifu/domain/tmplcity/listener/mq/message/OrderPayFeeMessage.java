package com.wanshifu.domain.tmplcity.listener.mq.message;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName OrderPayFee
 * @Description 订单打款请求参数
 * <AUTHOR>
 * @Date 2019/4/15 16:37
 * @Version 1.0
 **/
@Data
public class OrderPayFeeMessage {
    /**
     *第三方订单ID
     */
    @NotNull
    private Long globalOrderTraceId;


    /**
     *账户ID
     */
    @NotNull
    private Long accountId;

    /**
     * 账户类型
     */
    @NotEmpty
    private String accountType;

    /**
     * 下单方订单id
     */
    private Long thirdOrderId;

    /**
     *
     */
    @ValueIn("1,2")
    private Integer acceptanceType;

    /**
     * 打款状态
     */
    @NotNull
    private Boolean startPayFeeStatus;
}
