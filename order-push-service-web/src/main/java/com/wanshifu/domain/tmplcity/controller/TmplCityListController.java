package com.wanshifu.domain.tmplcity.controller;

import com.wanshifu.domain.tmplcity.TmplCityListService;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.order.push.api.TmplCityListApi;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderPushLogRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqtV2;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderPushLogResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterRespV2;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 家庭样板城市查询controller
 * @date 2024/6/25 13:48
 */
@RestController
@RequestMapping("/tmplCity/list")
public class TmplCityListController implements TmplCityListApi {


    @Resource
    private TmplCityListService tmplCityListService;


    /**
     * 分页获取家庭样板城市订单推荐师傅列表
     * @param rqt ListTmplCityOrderRecommendMasterRqt
     * @return SimplePageInfo<ListTmplCityOrderRecommendMasterResp>
     */
    @PostMapping("/orderRecommendMasterList")
    @Override
    public SimplePageInfo<ListTmplCityOrderRecommendMasterResp> orderRecommendMasterList(@Valid @RequestBody ListTmplCityOrderRecommendMasterRqt rqt) {
        return tmplCityListService.orderRecommendMasterList(rqt);
    }

    /**
     * 获取家庭样板城市订单推荐师傅列表，按score倒序，限制返回1000条
     * @param rqt ListTmplCityOrderRecommendMasterRqtV2
     * @return List<ListTmplCityOrderRecommendMasterRespV2>
     */
    @PostMapping("/orderRecommendMasterListV2")
    @Override
    public List<ListTmplCityOrderRecommendMasterRespV2> orderRecommendMasterListV2(@Valid @RequestBody ListTmplCityOrderRecommendMasterRqtV2 rqt) {
        return tmplCityListService.orderRecommendMasterListV2(rqt);
    }

    /**
     * 查询家庭样板城市订单推单记录日志
     * @param rqt ListTmplCityOrderPushLogRqt
     * @return List<ListTmplCityOrderPushLogResp>
     */
    @PostMapping("/orderPushLogList")
    @Override
    public List<ListTmplCityOrderPushLogResp> orderPushLogList(@Valid @RequestBody ListTmplCityOrderPushLogRqt rqt) {
        return tmplCityListService.orderPushLogList(rqt);
    }

}
