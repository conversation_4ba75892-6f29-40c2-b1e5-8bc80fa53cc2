package com.wanshifu.domain.tmplcity.gateway;

import com.wanshifu.domain.tmplcity.bo.*;
import com.wanshifu.framework.core.page.SimplePageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25 14:32
 */
public interface TmplCityListGateway {

    /**
     * 分页获取家庭样板城市订单推荐师傅列表
     * @param rqt ListTmplCityOrderRecommendMasterRqtBo
     * @return SimplePageInfo<ListTmplCityOrderRecommendMasterRespBo>
     */
    SimplePageInfo<ListTmplCityOrderRecommendMasterRespBo> orderRecommendMasterList(ListTmplCityOrderRecommendMasterRqtBo rqt);

    /**
     * 获取家庭样板城市订单推荐师傅列表
     * @param rqt ListTmplCityOrderRecommendMasterRqtBoV2
     * @return List<ListTmplCityOrderRecommendMasterRespBoV2>
     */
    List<ListTmplCityOrderRecommendMasterRespBoV2> orderRecommendMasterListV2(ListTmplCityOrderRecommendMasterRqtBoV2 rqt);


    /**
     * 获取家庭样板城市订单推单记录列表
     * @param rqt
     * @return
     */
    List<ListTmplCityOrderPushLogRespBo> orderPushLogList(ListTmplCityOrderPushLogRqtBo rqt);

}
