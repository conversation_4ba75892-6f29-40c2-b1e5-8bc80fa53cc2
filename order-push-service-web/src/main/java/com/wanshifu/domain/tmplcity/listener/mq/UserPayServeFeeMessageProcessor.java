package com.wanshifu.domain.tmplcity.listener.mq;


import cn.hutool.json.JSONUtil;
import com.wanshifu.domain.base.handler.AbstractCommonOrderGeneralTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.tmplcity.listener.mq.message.OrderPayFeeMessage;
import com.wanshifu.infrastructure.repository.InterfereOrderPushRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 用户支付服务费用
 * @date 2024/12/15 15:55
 */
@ConsumeTag(value = ConsumeTagEnum.USER_PAY_SERVE_FEE, maxReconsumeTime = 16)
@Slf4j
public class UserPayServeFeeMessageProcessor extends AbstractCommonOrderGeneralTopicMessageTag<OrderPayFeeMessage> {


    @Resource
    private InterfereOrderPushRepository interfereOrderPushRepository;

    /**
     * 干预推单记录批次删除开关
     */
    @Value("${interfere.orderPush.batchDelete.switch:on}")
    private String interfereOrderPushBatchDeleteSwitch;


    /**
     * 干预推单记录批次删除大小
     */
    @Value("${interfere.orderPush.batchDelete.size:100}")
    private int interfereOrderPushBatchDeleteSize;

    @Override
    public void postHandler(OrderPayFeeMessage orderPayFeeMessage) {
        log.info("user_payServeFee consume,params:{}", JSONUtil.toJsonStr(orderPayFeeMessage));

        Long globalOrderTraceId = orderPayFeeMessage.getGlobalOrderTraceId();
        if (Objects.isNull(globalOrderTraceId) || globalOrderTraceId == 0L) {
            log.error("master_serveFinish consume error, globalOrderTraceId is null or 0L!");
            return;
        }


        //删除平台干预推单记录
        if("on".equals(interfereOrderPushBatchDeleteSwitch)){
            interfereOrderPushRepository.deleteByGlobalOrderTraceIdBatch(globalOrderTraceId,interfereOrderPushBatchDeleteSize);
        }else{
            interfereOrderPushRepository.deleteByGlobalOrderTraceId(globalOrderTraceId);
        }

    }
}
