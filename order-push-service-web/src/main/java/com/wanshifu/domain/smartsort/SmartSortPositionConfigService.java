package com.wanshifu.domain.smartsort;

import com.wanshifu.infrastructure.SmartSortPositionConfig;
import com.wanshifu.order.push.request.push.CreateSmartSortPositionConfigRqt;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 17:54
 */
public interface SmartSortPositionConfigService {

    void insertOrUpdate(CreateSmartSortPositionConfigRqt createRqt);

    SmartSortPositionConfig selectByCityId(Integer cityId);

    void deleteConfig(List<Integer> deleteCityIds);
}
