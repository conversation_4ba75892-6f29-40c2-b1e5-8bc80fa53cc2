package com.wanshifu.domain.smartsort.controller;

import com.wanshifu.domain.smartsort.SmartSortPositionConfigService;
import com.wanshifu.order.push.api.SmartSortPositionConfigApi;
import com.wanshifu.order.push.request.push.CreateSmartSortPositionConfigRqt;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/26 9:59
 */
@RestController
@RequestMapping("/smartSort/positionConfig")
public class SmartSortPositionConfigController implements SmartSortPositionConfigApi {

    @Resource
    private SmartSortPositionConfigService smartSortPositionConfigService;


    @Override
    @PostMapping("/insertOrUpdateConfig")
    public void insertOrUpdateConfig(@Valid @RequestBody CreateSmartSortPositionConfigRqt createRqt) {
        smartSortPositionConfigService.insertOrUpdate(createRqt);
    }

    @Override
    @PostMapping("/deleteConfig")
    public void deleteConfig(@RequestBody List<Integer> deleteCityIds) {
        smartSortPositionConfigService.deleteConfig(deleteCityIds);
    }
}
