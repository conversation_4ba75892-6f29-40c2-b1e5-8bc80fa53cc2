package com.wanshifu.domain.smartsort.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wanshifu.domain.base.model.RedisKeyConstant;
import com.wanshifu.domain.smartsort.SmartSortPositionConfigService;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.infrastructure.SmartSortPositionConfig;
import com.wanshifu.infrastructure.repository.SmartSortPositionConfigRepository;
import com.wanshifu.order.push.request.push.CreateSmartSortPositionConfigRqt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/25 17:59
 */
@Service
public class SmartSortPositionConfigServiceImpl implements SmartSortPositionConfigService {

    @Resource
    private SmartSortPositionConfigRepository smartSortPositionConfigRepository;

    @Autowired
    private RedisHelper redisHelper;

    /**
     * 智能排序干预规则缓存天数，默认30天
     */
    @Value("${smartExposureCity.redisDays:30}")
    private int smartExposureCityRedisDays;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertOrUpdate(CreateSmartSortPositionConfigRqt createRqt) {
        if (Objects.isNull(createRqt)
                || (CollectionUtil.isEmpty(createRqt.getInsertOrUpdateCityIds()) && CollectionUtil.isEmpty(createRqt.getDeleteCityIds())
                || Objects.isNull(createRqt.getPositionType()))) {
            return;
        }
        if (createRqt.getPositionType() == 1) {
            //固定位置暂不支持
            return;
        }
        if (createRqt.getPositionType() == 2) {
            if (Objects.isNull(createRqt.getDynamicEveryFewSize())
                    || Objects.isNull(createRqt.getDynamicPositionIndex())) {
                throw new BusException("智能排序干预位置，动态位置参数缺失！");
            }
            if (createRqt.getDynamicPositionIndex() > createRqt.getDynamicEveryFewSize()) {
                throw new BusException("智能排序干预位置,动态位置参数有误！");
            }
        }
        //需要清除缓存的城市key
        Set<Integer> delRedisKey = Sets.newHashSet();

        //先删除
        List<Integer> deleteCityIds = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(createRqt.getDeleteCityIds())) {
            deleteCityIds.addAll(createRqt.getDeleteCityIds());
            delRedisKey.addAll(createRqt.getDeleteCityIds());
        }
        if (CollectionUtil.isNotEmpty(createRqt.getInsertOrUpdateCityIds())) {
            deleteCityIds.addAll(createRqt.getInsertOrUpdateCityIds());
            delRedisKey.addAll(createRqt.getInsertOrUpdateCityIds());
        }
        smartSortPositionConfigRepository.deleteByCityIds(deleteCityIds);
        //再新增
        if (CollectionUtil.isNotEmpty(createRqt.getInsertOrUpdateCityIds())) {
            JSONObject positionConfigRule = new JSONObject();
            positionConfigRule.put("dynamicEveryFewSize", createRqt.getDynamicEveryFewSize());
            positionConfigRule.put("dynamicPositionIndex", createRqt.getDynamicPositionIndex());

            List<SmartSortPositionConfig> smartSortPositionConfigList = Lists.newArrayList();

            Date now = new Date();
            for (Integer cityId : createRqt.getInsertOrUpdateCityIds()) {
                SmartSortPositionConfig config = new SmartSortPositionConfig();
                config.setCityId(cityId);
                config.setPositionType(createRqt.getPositionType());
                config.setPositionConfigRule(JSONUtil.toJsonStr(positionConfigRule));

                config.setCreateTime(now);
                config.setUpdateTime(now);

                smartSortPositionConfigList.add(config);
            }
            smartSortPositionConfigRepository.insertSmartSortPositionConfigList(smartSortPositionConfigList);
        }

        if (CollectionUtil.isNotEmpty(delRedisKey)) {
            //清除缓存
            for (Integer key : delRedisKey) {
                redisHelper.del(RedisKeyConstant.SMART_EXPOSURE_CITY_ID_KEY.concat(key.toString()));
            }
        }

    }

    @Override
    public SmartSortPositionConfig selectByCityId(Integer cityId) {
        if (Objects.isNull(cityId) || cityId == 0) {
            return null;
        }
        String res = redisHelper.get(RedisKeyConstant.SMART_EXPOSURE_CITY_ID_KEY.concat(cityId.toString()));
        if (Strings.isNullOrEmpty(res)) {
            //查具体城市
            SmartSortPositionConfig smartSortPositionConfig = smartSortPositionConfigRepository.selectByCityId(cityId);
            if (Objects.isNull(smartSortPositionConfig)) {
                //查全国
                smartSortPositionConfig = smartSortPositionConfigRepository.selectByCityId(0);
                if (Objects.isNull(smartSortPositionConfig)) {
                    return null;
                } else {
                    return smartSortPositionConfig;
                }

            } else {
                //默认缓存30天,并控制在0点~6点的时间段失效
                int expireSeconds = getRandomExpireSeconds();
                redisHelper.set(RedisKeyConstant.SMART_EXPOSURE_CITY_ID_KEY.concat(cityId.toString()), JSONUtil.toJsonStr(smartSortPositionConfig), expireSeconds);
                return smartSortPositionConfig;
            }
        } else {
            return JSONObject.parseObject(res, SmartSortPositionConfig.class);
        }
    }

    @Override
    public void deleteConfig(List<Integer> deleteCityIds) {
        if (CollectionUtil.isEmpty(deleteCityIds)) {
            return;
        }
        smartSortPositionConfigRepository.deleteByCityIds(deleteCityIds);
        //清除缓存
        for (Integer key : deleteCityIds) {
            redisHelper.del(RedisKeyConstant.SMART_EXPOSURE_CITY_ID_KEY.concat(key.toString()));
        }
    }

    /**
     * 到第{provinceNextIdRedisDays}天的0点~6点时间段的随机秒数过期
     * @return 缓存过期秒数
     */
    private int getRandomExpireSeconds() {
        // 获取当前时间
        Instant now = Instant.now();
        LocalDateTime nowDateTime = LocalDateTime.ofInstant(now, ZoneId.systemDefault());

        // 计算当前时间到当天24点的时间差（秒）
        LocalDateTime endOfDay = nowDateTime.withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        Duration durationToEndOfDay = Duration.between(nowDateTime.atZone(ZoneId.systemDefault()).toInstant(), endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        long secondsToEndOfDay = durationToEndOfDay.getSeconds();

        // 计算指定天数对应的秒数
        long daysInSeconds = (smartExposureCityRedisDays - 1) * 24 * 60 * 60L;

        // 生成0点到6点之间的随机秒数
        Random random = new Random();
        int randomSeconds = random.nextInt(6 * 60 * 60);

        // 总秒数 = 当前时间到当天24点的秒数 + 指定天数对应的秒数 + 0到6点之间的随机秒数
        long totalSeconds = secondsToEndOfDay + daysInSeconds + randomSeconds;

        // 检查是否溢出（由于我们仅计算了几天的秒数，所以这里几乎不会溢出）
        if (totalSeconds > Integer.MAX_VALUE) {
            throw new ArithmeticException("Expiration time exceeds Integer.MAX_VALUE");
        }
        return (int) totalSeconds;
    }
}
