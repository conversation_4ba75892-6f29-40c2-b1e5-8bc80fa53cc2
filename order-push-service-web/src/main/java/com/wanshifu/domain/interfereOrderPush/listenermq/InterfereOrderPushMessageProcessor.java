package com.wanshifu.domain.interfereOrderPush.listenermq;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.handler.AbstractMatchMasterTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.interfereOrderPush.listenermq.message.InterfereOrderPushMessage;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.tmplcity.TmplCityOperateService;
import com.wanshifu.domain.tmplcity.listener.mq.message.RecommendMatchResultMessage;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.infrastructure.InterfereOrderPush;
import com.wanshifu.infrastructure.repository.InterfereOrderPushRepository;
import com.wanshifu.order.offer.domains.api.response.SimpleOrderGrab;
import com.wanshifu.order.offer.domains.enums.OrderStatus;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.push.request.push.MasterAddressInfo;
import com.wanshifu.order.push.request.tmplcity.SaveTmplCityOrderRecommendMasterRqt;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 平台干预订单推荐列表
 * @date 2024/6/25 11:16
 */
@ConsumeTag(value = ConsumeTagEnum.INTERFERE_ORDER_PUSH, maxReconsumeTime = 6)
@Slf4j
public class InterfereOrderPushMessageProcessor extends AbstractMatchMasterTopicMessageTag<InterfereOrderPushMessage> {

    @Resource
    private InterfereOrderPushRepository interfereOrderPushRepository;

    /**
     * 干预推单记录批次保存大小
     */
    @Value("${interfere.orderPush.batchSize:50}")
    private int interfereOrderPushBatchSize;


    /**
     * 干预推单记录批次删除开关
     */
    @Value("${interfere.orderPush.batchDelete.switch:on}")
    private String interfereOrderPushBatchDeleteSwitch;


    /**
     * 干预推单记录批次删除大小
     */
    @Value("${interfere.orderPush.batchDelete.size:100}")
    private int interfereOrderPushBatchDeleteSize;


    @Resource
    private CommonOrderOfferService commonOrderOfferService;



    @Override
    public void postHandler(InterfereOrderPushMessage interfereOrderPushMessage) {



        log.info("interfereOrderPushMessage,params:{}", JSONUtil.toJsonStr(interfereOrderPushMessage));
        Long globalOrderTraceId = interfereOrderPushMessage.getGlobalOrderTraceId();
        Long orderId = interfereOrderPushMessage.getOrderId();
        List<InterfereOrderPushMessage.PushMaster> pushMasterList = interfereOrderPushMessage.getMasterList();
        if (Objects.isNull(globalOrderTraceId) || globalOrderTraceId == 0L) {
            log.error("interfereOrderPushMessage error,globalOrderTraceId is null or 0L!");
            return;
        }
        if (CollectionUtil.isEmpty(pushMasterList)) {
            log.error("interfereOrderPushMessage error, pushMasterList is empty!");
            return;
        }


        List<InterfereOrderPush> interfereOrderPushList = new ArrayList<>();
        pushMasterList.forEach(pushMaster -> {
            InterfereOrderPush interfereOrderPush = new InterfereOrderPush();
            interfereOrderPush.setGlobalOrderTraceId(globalOrderTraceId);
            interfereOrderPush.setOrderId(orderId);
            interfereOrderPush.setMasterId(pushMaster.getMasterId());
            interfereOrderPush.setScore(pushMaster.getScore());
            interfereOrderPush.setCreateTime(new Date());
            interfereOrderPush.setUpdateTime(new Date());
            interfereOrderPushList.add(interfereOrderPush);
        });


        if("on".equals(interfereOrderPushBatchDeleteSwitch)){
            interfereOrderPushRepository.deleteByGlobalOrderTraceIdBatch(globalOrderTraceId,interfereOrderPushBatchDeleteSize);
        }else{
            interfereOrderPushRepository.deleteByGlobalOrderTraceId(globalOrderTraceId);
        }


        SimpleOrderGrab simpleOrderGrab = commonOrderOfferService.getSimpleOrderGrabByGlobalId(globalOrderTraceId);

        if(Objects.isNull(simpleOrderGrab) || Objects.isNull(simpleOrderGrab.getOrderBase())){
            return ;
        }

        if (OrderStatus.TRADING.code.equals(simpleOrderGrab.getOrderBase().getOrderStatus())) {
            List<List<InterfereOrderPush>> interfereOrderPushLists = Lists.partition(interfereOrderPushList, interfereOrderPushBatchSize);
            interfereOrderPushLists.forEach(interfereOrderPusBatch -> interfereOrderPushRepository.insertList(interfereOrderPusBatch));
        }

    }
}
