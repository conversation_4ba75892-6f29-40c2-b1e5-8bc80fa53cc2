package com.wanshifu.domain.interfereOrderPush.gatewayimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanshifu.domain.interfereOrderPush.gateway.InterfereOrderPushListGateway;
import com.wanshifu.domain.tmplcity.bo.*;
import com.wanshifu.domain.tmplcity.gateway.TmplCityListGateway;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.infrastructure.InterfereOrderPush;
import com.wanshifu.infrastructure.TmplCityOrderPushLog;
import com.wanshifu.infrastructure.TmplCityOrderRecommendMaster;
import com.wanshifu.infrastructure.provider.MapperProviderSupport;
import com.wanshifu.infrastructure.repository.InterfereOrderPushRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 14:33
 */
@Service
public class InterfereOrderPushListGatewayImpl extends MapperProviderSupport implements InterfereOrderPushListGateway {

    @Resource
    private InterfereOrderPushRepository interfereOrderPushRepository;

    @Override
    public List<InterfereOrderPush> getInterfereOrderPushList(ListTmplCityOrderRecommendMasterRqtBo rqt) {
        return interfereOrderPushRepository.selectByGlobalOrderTraceId(rqt.getGlobalOrderTraceId(),200);
    }


}
