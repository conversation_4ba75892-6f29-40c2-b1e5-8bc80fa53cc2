package com.wanshifu.domain.interfereOrderPush.gateway;

import com.wanshifu.domain.tmplcity.bo.*;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.infrastructure.InterfereOrderPush;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25 14:32
 */
public interface InterfereOrderPushListGateway {

    /**
     * 分页获取家庭样板城市订单推荐师傅列表
     * @param rqt ListTmplCityOrderRecommendMasterRqtBo
     * @return SimplePageInfo<ListTmplCityOrderRecommendMasterRespBo>
     */
    List<InterfereOrderPush> getInterfereOrderPushList(ListTmplCityOrderRecommendMasterRqtBo rqt);



}
