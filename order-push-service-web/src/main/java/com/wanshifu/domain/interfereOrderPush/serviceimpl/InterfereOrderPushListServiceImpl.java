package com.wanshifu.domain.interfereOrderPush.serviceimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanshifu.domain.interfereOrderPush.InterfereOrderPushListService;
import com.wanshifu.domain.interfereOrderPush.gateway.InterfereOrderPushListGateway;
import com.wanshifu.domain.tmplcity.TmplCityListService;
import com.wanshifu.domain.tmplcity.bo.*;
import com.wanshifu.domain.tmplcity.gateway.TmplCityListGateway;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.InterfereOrderPush;
import com.wanshifu.order.push.request.GetInterfereOrderPushListRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderPushLogRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqtV2;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderPushLogResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterRespV2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 样板城市查询接口实现
 * @date 2024/6/25 14:26
 */
@Service
public class InterfereOrderPushListServiceImpl implements InterfereOrderPushListService {

    @Resource
    private InterfereOrderPushListGateway interfereOrderPushListGateway;

    @Override
    public List<InterfereOrderPush> getInterfereOrderPushList(GetInterfereOrderPushListRqt rqt) {

        if (Objects.isNull(rqt) || Objects.isNull(rqt.getGlobalOrderTraceId())) {
            return null;
        }

        ListTmplCityOrderRecommendMasterRqtBo rqtBo = BeanUtil.toBean(rqt, ListTmplCityOrderRecommendMasterRqtBo.class);

        //分页查询推荐师傅数据
        List<InterfereOrderPush> interfereOrderPushList = interfereOrderPushListGateway.getInterfereOrderPushList(rqtBo);
        if (CollectionUtils.isEmpty(interfereOrderPushList)) {
            return null;
        }

       return interfereOrderPushList;
    }


}
