package com.wanshifu.domain.interfereOrderPush;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.infrastructure.InterfereOrderPush;
import com.wanshifu.order.push.request.GetInterfereOrderPushListRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderPushLogRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqt;
import com.wanshifu.order.push.request.tmplcity.ListTmplCityOrderRecommendMasterRqtV2;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderPushLogResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterResp;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterRespV2;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25 14:25
 */
public interface InterfereOrderPushListService {

    /**
     * 分页获取家庭样板城市订单推荐师傅列表
     * @param rqt ListTmplCityOrderRecommendMasterRqt
     * @return SimplePageInfo<ListTmplCityOrderRecommendMasterResp>
     */
    List<InterfereOrderPush> getInterfereOrderPushList(GetInterfereOrderPushListRqt rqt);


}
