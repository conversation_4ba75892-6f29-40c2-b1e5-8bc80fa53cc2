package com.wanshifu.domain.interfereOrderPush.listenermq.message;

import com.wanshifu.order.push.request.push.MasterAddressInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 样板城市订单推荐师傅message
 * @date 2024/6/26 11:03
 */
@Data
public class InterfereOrderPushMessage {

    @NotNull
    @Min(1L)
    private Long orderId;

    /**
     * 全局id
     */
    private Long globalOrderTraceId;

    /**
     * 推送师傅信息
     */
    @Valid
    @NotEmpty
    private List<PushMaster> masterList;


    @Data
    public static class PushMaster {
        /**
         * 师傅id
         */
        private Long masterId;


        /**
         * 推单评分
         */
        private BigDecimal score;


    }

}
