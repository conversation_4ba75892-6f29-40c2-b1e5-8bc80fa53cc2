package com.wanshifu.domain.push.gateway;

import com.wanshifu.domain.special.model.ModifyOrderResetMasterDistance;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.order.offer.domains.api.response.offer.PushingOrderCompositeResp;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.push.request.push.BatchUpdatePushDistanceRqt;
import org.springframework.scheduling.annotation.Async;
import com.wanshifu.order.push.request.push.MasterAddressInfo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;


public interface OrderPushManagerGateway {

    /**
     * 批量查询师傅订单的推单信息
     *
     * @param provinceNextIds 省下级地址id
     * @param masterId 师傅id
     * @param orderIdIdList 订单idList
     * @return
     */
    public List<OrderPush> selectByMasterIdAndOrderIds(List<Long> provinceNextIds, Long masterId, List<Long> orderIdIdList);

    /**
     * 查询订单推单的师傅集合
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    public List<Long> selectInviteMasterByOrderId(List<Long> provinceNextId, Long orderId);

    /**
     * 查询师傅推单记录
     *
     * @param provinceNextId 省下级地址id
     * @param masterId 师傅id
     * @return
     */
    List<OrderPush> selectPushOrderByMasterId(List<Long> provinceNextId, Long masterId);

    /**
     * 查询订单关联师傅推单记录
     *
     * @param provinceNextId 省下级地址id
     * @param orderId
     * @param masterIds
     * @return
     */
    List<OrderPush> queryOrderToMasterIdsPushInfo(List<Long> provinceNextId, Long orderId, List<Long> masterIds);

    /**
     * 师傅拉取报价列表
     * @param provinceNextIds 上下级地址id
     * @param masterId
     * @param orderIdList
     */
    void masterPullOrderList(List<Long> provinceNextIds, Long masterId, List<Long> orderIdList);

    /**
     * 更新样板城市推单弹窗读取时间
     *
     * @param orderPushList
     */
    void updateTmplCityTime(List<OrderPush> orderPushList);


    /**
     * 修改师傅服务地区推单
     *
     * @param provinceNextId 省下级地址id
     * @param masterId 师傅id
     * @param removeServeThirdDivisionIdList
     * @param removeServeFourthDivisionIdList
     * @param orderPushList
     * @param orderBaseMap
     * @return
     */
    List<Long> modifyMasterServeRegions(List<Long> provinceNextId, Long masterId,
                                        List<Long> removeServeThirdDivisionIdList,
                                        List<Long> removeServeFourthDivisionIdList,
                                        List<OrderPush> orderPushList, Map<Long, OrderBase> orderBaseMap);

    /**
     * 移除技能
     *
     * @param provinceNextId 省下级地址id
     * @param masterId 师傅id
     * @param removeTechnologyIds
     * @param selectedTechnologyIds
     * @return
     */
    List<Long> removeTechnologys(List<Long> provinceNextId, Long masterId,
                                 List<Long> removeTechnologyIds,
                                 Map<Long, OrderBase> orderBaseMap,
                                 List<OrderPush> orderPushList,
                                 String selectedTechnologyIds);

    /**
     * 异步直接删除push表
     * @param provinceNextId 省下级地址id
     * @param orderId
     * @param masterId
     */
    @Async
    void clearOrderPush(Long provinceNextId, Long orderId, Long masterId);

    /**
     * 分批软删除推单数据 - 订单
     *
     * @param provinceNextId 省下级地址id
     * @param orderId
     * @param appendNote
     * @return
     */
    int softDeleteOrderPush(List<Long> provinceNextId, Long orderId, String appendNote);

    /**
     * 分批物理删除推单数据
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    int deleteOrderPushBatch(List<Long> provinceNextId, Long orderId);

    /**
     * 物理删除
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterIds
     * @return
     */
    int deleteOrderPushByOrderIdAndMasterIds(List<Long> provinceNextId, Long orderId, Set<Long> masterIds);


    /**
     * 异步推单
     *
     * @param provinceNextId 省下级地址id
     * @param pushMold new_model:样板城市推单 new_model_single:样板城市推单（只推主力师傅）
     * @param pushOrderComposite
     * @param masterAddressInfoList
     * @param pushTime
     * @param pushDivisionLevel
     * @param pushFrom
     * @param techniqueTypeIds
     * @param agentOrderFlag
     * @param pushFlag
     * @param accordingDistancePushFlag
     * @param masterSourceType 师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    void pushAsync(List<Long> provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, List<MasterAddressInfo> masterAddressInfoList, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds,
                   Integer agentOrderFlag, Integer pushFlag, Integer accordingDistancePushFlag, Integer exclusiveFlag, String masterSourceType);

    /**
     * 同步推单
     *
     * @param provinceNextId 省下级地址id
     * @param pushMold new_model:样板城市推单 new_model_single:样板城市推单（只推主力师傅）
     * @param pushOrderComposite
     * @param masterAddressInfoList
     * @param pushTime
     * @param pushDivisionLevel
     * @param pushFrom
     * @param techniqueTypeIds
     * @param agentOrderFlag
     * @param pushFlag
     * @param accordingDistancePushFlag
     * @param masterSourceType 师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    void pushSync(List<Long> provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, List<MasterAddressInfo> masterAddressInfoList, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag,
                  Integer pushFlag, Integer accordingDistancePushFlag, Integer exclusiveFlag, String masterSourceType);


    /**
     * 推单处理
     *
     * @param provinceNextId
     * @param pushMold
     * @param pushOrderComposite
     * @param masterAddressInfoList
     * @param pushTime
     * @param pushDivisionLevel
     * @param pushFrom
     * @param techniqueTypeIds
     * @param agentOrderFlag
     * @param pushFlag
     * @param accordingDistancePushFlag
     * @param exclusiveFlag
     * @param masterSourceType 师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    void pushOrderHandle(List<Long> provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, List<MasterAddressInfo> masterAddressInfoList, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag, Integer pushFlag,
                         Integer accordingDistancePushFlag, Integer exclusiveFlag, String masterSourceType);


    void clearOrderPushByOrderIdV2(List<Long> provinceNextId, Long orderId, String appendNote, Boolean isNeedAsyncDelete);

    void clearOrderPushByOrderIdForGrabDefinite(List<Long> provinceNextId, Long orderId);


    void clearTechniqueVerifyOrderForGrabDefinite(List<Long> provinceNextId, Long masterId,Long categoryId);


        /**
         * 师傅报价
         *
         * @param provinceNextId 省下级地址id
         * @param orderId 订单id
         * @param masterId 师傅id
         * @param offerTime 报价时间
         * @param appendNote 报价备注
         * @return
         */
    void offerPrice(List<Long> provinceNextId, Long orderId, Long masterId, Date offerTime, String appendNote);

    /**
     * 取消报价
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     */
    void cancelOfferPrice(List<Long> provinceNextId, Long orderId, Long masterId);

    /**
     * 最后一个师傅报价
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param appendNote 备注
     */
    void lastOfferPrice(List<Long> provinceNextId, Long orderId, String appendNote);

    /**
     * 清除信息订单推送记录
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     */
    void deleteInfoOrderBaseOrderPush(List<Long> provinceNextId, Long orderId, Long masterId);

    /**
     * 物理删除信息推单记录
     * @param provinceNextId
     * @param orderId
     * @param closeTime
     */
    void deleteInfoOrderPush(List<Long> provinceNextId, Long orderId, Date closeTime);

    /**
     * 师傅不感兴趣订单
     *
     * @param provinceNextId 省下级地址id
     * @param masterId 师傅id
     * @param orderId 订单id
     */
    void masterDisinterestOrder(List<Long> provinceNextId, Long orderId, Long masterId);


    int deleteOrderPush(List<Long> provinceNextId, Long masterId,Long categoryId);

    void updateFullTimeExclusiveOrderFlag(List<Long> provinceNextId, Long masterId);

    }
