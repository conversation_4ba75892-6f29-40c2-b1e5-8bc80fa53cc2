package com.wanshifu.domain.push.listener.mq;

import cn.hutool.json.JSONUtil;
import com.wanshifu.domain.base.handler.AbstractNormalBusinessTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.push.gateway.OrderPushOperateGateway;
import com.wanshifu.domain.push.model.OrderPushClear;
import com.wanshifu.domain.sdk.address.CommonAddressService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-08-25 18:52
 * @Description   手动删除订单推送记录 （自产自销）
 * @Version v1
 **/

@ConsumeTag(value = ConsumeTagEnum.ORDER_PUSH_CLEAR, isSupportLog = false , maxReconsumeTime = 16)
public class OrderPushClearMessageProcessor extends AbstractNormalBusinessTopicMessageTag<OrderPushClear> {

    @Resource
    protected OrderPushOperateGateway orderPushOperateGateway;

    @Resource
    private CommonAddressService commonAddressService;

    @Override
    public void postHandler(OrderPushClear orderPushClear) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(orderPushClear.getProvinceNextId(),
                "wanshifu_order_push_service_normal_business_order_topic.orderPushClear",
                JSONUtil.toJsonStr(orderPushClear));
        orderPushClear.setProvinceNextIdList(provinceNextId);
        orderPushOperateGateway.orderPushClear(orderPushClear);
    }
}
