package com.wanshifu.domain.push.ability;

import com.wanshifu.domain.push.OrderPushOperateService;
import com.wanshifu.domain.push.model.OrderPushRqt;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023-09-22 09:53
 * @Description
 * @Version v1
 **/
@Slf4j
public abstract class OrderPushResultHandlerSupport implements OrderPushResultHandler<OrderPushRqt> {

    @Resource
    protected OrderPushOperateService orderPushOperateService;

    @Override
    public void postProcessor(OrderPushRqt orderPushRqt) {
        orderPushOperateService.pushedToMaster(orderPushRqt);
    }

}
