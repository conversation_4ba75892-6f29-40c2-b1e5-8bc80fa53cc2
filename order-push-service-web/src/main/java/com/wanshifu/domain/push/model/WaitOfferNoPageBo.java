package com.wanshifu.domain.push.model;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Builder
@Data
public class WaitOfferNoPageBo {

    /**
     * 师傅ID
     */
    private Long masterId;

    /**
     * 随机取竞争少的订单ID
     */
    private List<Long> excludeOrderIds;

    /**
     * 当前时间
     */
    private Date currentDateTime;

    /**
     * 订单区域id集合
     */
    private List<Long> thirdDivisionIds;

    /**
     * 省下级地址id
     */
    private List<Long> provinceNextId;

    /**
     * 样板城市订单标识
     * 0：非样板城市订单，1：样板城市订单，2：样板城市订单转推普通师傅，默认0
     */
    private List<Integer> tmplCityFlag;

    private Integer exclusiveAppointType;
}
