package com.wanshifu.domain.push.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/31 14:55
 */
@Data
public class OrderRemoveTagDTO {

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 标签维度
     * master: 师傅
     * order: 订单
     */
    private String dimension;

    /**
     * 师傅维度的id
     * dimension = master 必传
     */
    private List<Long> masterIds;
    /**
     * 标签类型  限制30长度-例如: high_unit_price:高客单价
     * TODO 注意:标签为通用类型 不可重复
     * 目前已存在标签:
     * exclusive:专属标签
     * agent: 代理商标签
     * merchant_invite: 商家邀请
     * quick_assignment： 秒指派标签
     * master_shop:师傅店铺
     * agreement_master:协议师傅
     */
    private String tagType;

    private String tagName;

    /**
     * 标签值。非必传 用于业务扩展
     */
    private Integer tagValue;

    private Long globalOrderTraceId;
}
