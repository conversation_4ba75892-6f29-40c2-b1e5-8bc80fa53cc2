package com.wanshifu.domain.push.model;

import com.wanshifu.order.push.request.BaseWaitOfferCount;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-11-28 17:37
 * @Description
 * @Version v1
 **/
@Data
public class WaitOfferCountGateway extends BaseWaitOfferCount {

    /**
     * 推单标识 0：正常推单 1：附近推单
     */
    private Integer pushFlag;


    /**
     * 菜单类别 1:指派专区
     */
    private Integer menuCategory;

    /**
     * 当前时间
     */
    private Date currentDateTime ;


    private Integer mold;

    /**
     * 表名
     */
    private String tableName = "order_push";

    /**
     * 必接订单标记
     */
    private Integer mustOrderFlag;


    /**
     * 专属好单标记
     */
    private Integer exclusiveGoodOrderFlag;

    /**
     * 城市id
     */
    private List<Long> provinceNextIdList;

    /**
     * pushFlag为0时展示长尾单的一级服务id
     *
     */
    private List<Long> displayLongTailOrderLevel1ServeIds;



}
