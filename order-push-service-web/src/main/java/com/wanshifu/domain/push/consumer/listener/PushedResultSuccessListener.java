package com.wanshifu.domain.push.consumer.listener;

import com.alibaba.fastjson.JSON;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.domain.base.model.PushProducerTagEnum;
import com.wanshifu.domain.base.tools.TopicHelper;
import com.wanshifu.domain.push.model.OrderPushCompositeRqt;
import com.wanshifu.domain.push.model.PushedResultNotices;
import com.wanshifu.domain.push.model.enums.BusinessLineIdEnum;
import com.wanshifu.domain.push.model.event.PushedResultSuccessEvent;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.tmplcity.gateway.TmplCityOperateGateway;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.offer.PushingOrderCompositeResp;
import com.wanshifu.order.push.enums.PushScenarioType;
import com.wanshifu.order.push.request.push.ExclusiveOrderLabel;
import com.wanshifu.order.push.request.push.MasterAddressInfo;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-09-21 14:22
 * @Description 推单结果处理成功事件
 * @Version v1
 **/
@Component
public class PushedResultSuccessListener {

    @Resource
    private MessageSenderService mqSendGateway;

    @Resource
    private TmplCityOperateGateway tmplCityOperateGateway;


    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    //推单结果通知
    @Async
    @EventListener(PushedResultSuccessEvent.class)
    public void pushedResultNotice (PushedResultSuccessEvent pushedResultSuccessEvent){
        PushScenarioType pushScenarioType = pushedResultSuccessEvent.getPushScenarioType();
        OrderPushCompositeRqt orderPushRqt = pushedResultSuccessEvent.getOrderPushRqt();
        switch (pushScenarioType){
            case DIRECT_ASSIGNMENT_PUSH:
            case DIRECT_PUSH :
            case SMART_PUSH:
                // 信息订单
                PushedResultNotices pushedResultNotices = new PushedResultNotices();
                String recruitTagName = this.getRecruitTagName(orderPushRqt.getExclusiveOrderLabel());
                Integer exclusiveFlag =  Objects.nonNull(orderPushRqt.getExclusiveOrderLabel()) ? orderPushRqt.getExclusiveOrderLabel().getExclusiveFlag() : null ;
                pushedResultNotices.setBusinessLineId(pushedResultSuccessEvent.getBusinessLineId());
                pushedResultNotices.setPushMasterIds(pushedResultSuccessEvent.getPushMasterIds());
                pushedResultNotices.setMasterInfoList(buildMasterInfos(pushedResultSuccessEvent.getPushMasterIds(),orderPushRqt));
                pushedResultNotices.setPushMode(pushedResultSuccessEvent.getPushMode());
                pushedResultNotices.setPushScenarioType(pushScenarioType);
                pushedResultNotices.setPushDivisionLevel(Optional.ofNullable( orderPushRqt.getPushDivisionLevel() ).orElse(3)  );
                pushedResultNotices.setFirstPush(orderPushRqt.getFirstPush());
                pushedResultNotices.setRecruitId(orderPushRqt.getRecruitId());
                pushedResultNotices.setHasPrice(Optional.ofNullable( orderPushRqt.isHasPrice()).orElse(false) );
                pushedResultNotices.setTeamMasterOrderPush(Optional.ofNullable( orderPushRqt.getTeamMasterOrderPush()).orElse(0) );
                pushedResultNotices.setPackageId(orderPushRqt.getPackageId());
                pushedResultNotices.setGoodsAttribute(orderPushRqt.getGoodsAttribute());
                pushedResultNotices.setOrderPackageAttributeRange(orderPushRqt.getOrderPackageAttributeRange());
                pushedResultNotices.setPushFlag(orderPushRqt.getPushFlag());
                pushedResultNotices.setAgentMasterList(orderPushRqt.getAgentMasterList());
                pushedResultNotices.setAgentPushNoHiredRePushTime(orderPushRqt.getAgentPushNoHiredRePushTime());
                pushedResultNotices.setRecruitTagName(recruitTagName);
                pushedResultNotices.setExclusiveOrderFlag(exclusiveFlag);
                pushedResultNotices.setPushModeType(orderPushRqt.getPushModeType());
                pushedResultNotices.setFirstTimeValidPush(orderPushRqt.getFirstTimeValidPush());
                pushedResultNotices.setMasterSourceType(orderPushRqt.getMasterSourceType());
                pushedResultNotices.setOrderId(orderPushRqt.getOrderId());
                pushedResultNotices.setMatchSceneCode(orderPushRqt.getMatchSceneCode());
                if (BusinessLineIdEnum.THREE.id == pushedResultSuccessEvent.getBusinessLineId()) {
                    pushedResultNotices.setInfoOrderBase(pushedResultSuccessEvent.getInfoOrderBase());
                    pushedResultNotices.setGlobalOrderTraceId(pushedResultSuccessEvent.getInfoOrderBase().getGlobalOrderTraceId());
                } else {
                    PushingOrderCompositeResp orderComposite = orderPushRqt.getOrderComposite();
                    pushedResultNotices.setOrderBaseComposite(orderComposite.getOrderBaseComposite());
                    pushedResultNotices.setGlobalOrderTraceId(orderComposite.getOrderBaseComposite().getOrderBase().getGlobalOrderTraceId());
                }
                mqSendGateway.sendSyncMessage(TopicHelper.ORDER_PUSH_COMMON_TOPIC, PushProducerTagEnum.ORDER_PUSHED_RESULT_NOTICE.tag, JSON.toJSONString(pushedResultNotices), true);
                this.updateTmplCityOrderPushStatus(pushedResultSuccessEvent.getBusinessLineId(),pushedResultSuccessEvent.getOrderPushRqt().getOrderId(),pushedResultSuccessEvent.getOrderPushRqt().getGlobalOrderTraceId(),
                        new ArrayList<>(pushedResultSuccessEvent.getPushMasterIds()));
                break;
                //TODO 目前后台推单、再次推单，不需要出发推单结果通知
            case AGAIN_PUSH:
            case MANUAL_PUSH:
                this.updateTmplCityOrderPushStatus(pushedResultSuccessEvent.getBusinessLineId(),pushedResultSuccessEvent.getOrderPushRqt().getOrderId(),pushedResultSuccessEvent.getOrderPushRqt().getGlobalOrderTraceId(),
                        new ArrayList<>(pushedResultSuccessEvent.getPushMasterIds()));
                break;
            default:
                break;
        }

    }

    private int updateTmplCityOrderPushStatus(Integer businessLineId,Long orderId,Long globalOrderTraceId,List<Long> masterIdList){
        if(businessLineId == BusinessLineIdEnum.TWO.id){

            OrderExclusiveTagResp newModelTagResp = commonOrderOfferService.getOrderExclusiveTag(orderId, null, "new_model");
            if(Objects.isNull(newModelTagResp)){
                return 0;
            }

            //更新样板推荐师傅推单状态
            tmplCityOperateGateway.updateIsPush(globalOrderTraceId,masterIdList);
        }
        return 1;
    }

    private String getRecruitTagName(ExclusiveOrderLabel exclusiveOrderLabel) {
        String recruitTagName = "";
        //更新专属标签
        if (Objects.nonNull(exclusiveOrderLabel)) {
            recruitTagName = exclusiveOrderLabel.getRecruitTagName();
        }
        return recruitTagName;
    }

    private List<PushedResultNotices.MasterInfo> buildMasterInfos(Set<Long> pushMasterIds,OrderPushCompositeRqt orderPushRqt){
        if (CollectionUtils.isEmpty(pushMasterIds)) {
            return new ArrayList<>();
        }
        List<MasterAddressInfo> masterAddressInfoList = orderPushRqt.getMasterAddressInfoList();
        Map<Long, MasterAddressInfo> masterAddressInfoMap = masterAddressInfoList.stream().collect(Collectors.toMap(MasterAddressInfo::getMasterId, Function.identity(), (x1, x2) -> x2));
        List<PushedResultNotices.MasterInfo> masterInfoList = new ArrayList<>();
        for (Long masterId: pushMasterIds) {
            MasterAddressInfo masterAddressInfo = masterAddressInfoMap.get(masterId);
            if (Objects.nonNull(masterAddressInfo)) {
                PushedResultNotices.MasterInfo masterInfo = new PushedResultNotices.MasterInfo();
                masterInfo.setMasterId(masterAddressInfo.getMasterId());
                masterInfo.setRecruitId(masterAddressInfo.getRecruitId());
                masterInfo.setTagName(masterAddressInfo.getTagName());
                masterInfo.setTagExpand(masterAddressInfo.getTagExpand());
                masterInfo.setAccordingTechnologyPushFlag(masterAddressInfo.getAccordingTechnologyPushFlag());
                masterInfo.setAutoOfferSort(masterAddressInfo.getAutoOfferSort());
                masterInfo.setAutoPrice(masterAddressInfo.getAutoPrice());
                masterInfo.setShuntFlag(Objects.nonNull(masterAddressInfo.getShuntFlag()) ? masterAddressInfo.getShuntFlag() : 0);
                masterInfoList.add(masterInfo);
            }
        }

        return masterInfoList;
    }
}
