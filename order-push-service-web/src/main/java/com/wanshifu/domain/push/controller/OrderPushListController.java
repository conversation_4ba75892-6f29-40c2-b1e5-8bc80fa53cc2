package com.wanshifu.domain.push.controller;

import com.wanshifu.domain.push.OrderPushListService;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.order.push.api.OrderPushListApi;
import com.wanshifu.order.push.request.*;
import com.wanshifu.order.push.request.push.MasterAppletListOrderPushRqt;
import com.wanshifu.order.push.request.push.TmplCityOrderPushRqt;
import com.wanshifu.order.push.response.*;
import com.wanshifu.order.push.response.push.MasterAppletListOrderPushResp;
import com.wanshifu.order.push.response.push.TmplCityOrderPushResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import com.google.common.util.concurrent.RateLimiter;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 14:23
 */
@RestController
@RequestMapping("/orderPush/list")
@Slf4j
public class OrderPushListController implements OrderPushListApi {


    @Resource
    private OrderPushListService orderPushListService;

    /**
     * 流控访问限制
     * 针对师傅小程序游客模式简单做一个流控，避免恶意刷数据攻击
     */
    private RateLimiter rateLimiter = RateLimiter.create(8);

    /**
     * 师傅待报价订单列表(特殊列表)
     * @param rqt WaitOfferSpecialListRqt
     * @return WaitOfferV2Resp
     * <AUTHOR>
     */
    @PostMapping("waitOfferSpecialList")
    @Override
    public SimplePageInfo<WaitOfferV2Resp> waitOfferSpecialList(@Valid @RequestBody WaitOfferSpecialListRqt rqt) {
        return orderPushListService.waitOfferSpecialList(rqt);
    }

    /**
     * 师傅待报价订单列表V2
     * @param waitOfferRqt WaitOfferV2Rqt
     * @return SimplePageInfo<WaitOfferResp>
     * <AUTHOR>
     */
    @PostMapping("waitOfferV2")
    @Override
    public SimplePageInfo<WaitOfferV2Resp> waitOfferV2(@Valid @RequestBody WaitOfferV2Rqt waitOfferRqt) {
        return orderPushListService.waitOfferV2(waitOfferRqt);
    }

    /**
     * 家庭模版城市订单推单列表
     * @param rqt TmplCityOrderPushRqt
     * @return SimplePageInfo<TmplCityOrderPushResp>
     */
    @PostMapping("/tmplCityOrderPushList")
    @Override
    public SimplePageInfo<TmplCityOrderPushResp> tmplCityOrderPushList(@Valid @RequestBody TmplCityOrderPushRqt rqt) {
        return orderPushListService.tmplCityOrderPushList(rqt);
    }

    /**
     * 师傅待报价订单列表不分页
     * @param waitOfferNoPageRqt WaitOfferNoPageRqt
     * @return SimplePageInfo<WaitOfferResp>
     * <AUTHOR>
     */
    @PostMapping("waitOfferNoPage")
    @Override
    public List<WaitOfferNoPageResp> waitOfferNoPage(@RequestBody @Valid WaitOfferNoPageRqt waitOfferNoPageRqt) {
        return orderPushListService.waitOfferNoPage(waitOfferNoPageRqt);
    }

    /**
     * IOC(智能运营)活动待报价-筛选数据
     *
     * @param rqt IocWaitOfferFilterRqt
     * @return List<IocWaitOfferFilterResp>
     */
    @PostMapping("getIocWaitOfferList")
    @Override
    public List<IocWaitOfferFilterResp> getIocWaitOfferList(@Valid @RequestBody IocWaitOfferFilterRqt rqt) {
        return orderPushListService.getIocWaitOfferList(rqt);
    }



    /**
     * 批量获取订单待报价信息（最大10个订单）
     * @param batchGetOrderWaitOfferInfoReq BatchGetOrderWaitOfferInfoReq
     * @return List<WaitOfferV2Resp>
     */
    @PostMapping("batchGetOrderWaitOfferInfo")
    @Override
    public List<WaitOfferV2Resp> batchGetOrderWaitOfferInfo(@RequestBody @Validated BatchGetOrderWaitOfferInfoReq batchGetOrderWaitOfferInfoReq) {
        return orderPushListService.batchGetOrderWaitOfferInfo(batchGetOrderWaitOfferInfoReq);
    }


    /**
     * 获取订单推单数据（master-notice-service 调用）
     *
     * @param provinceNextId 省下级地址id
     * @param orderId orderId
     * @return List<BatchOrderPushResp>
     */
    @PostMapping("getOrderPushForNotice")
    @Override
    public List<BatchOrderPushResp> getOrderPushForNotice(@RequestParam("orderId") Long orderId, @RequestParam("provinceNextId") Long provinceNextId) {
        return orderPushListService.getOrderPushForNotice(orderId, provinceNextId);
    }


    /**
     * 获取推单列表（master-order-distribute-service） 调用
     *
     * @param provinceNextId 省下级地址id
     * @param orderId orderId 订单id
     * @return List<BatchOrderPushResp>
     */
    @Override
    @PostMapping("getOrderPushList")
    public List<BatchOrderPushResp> getOrderPushList(@RequestParam("orderId") Long orderId, @RequestParam("provinceNextId") Long provinceNextId) {
        return orderPushListService.getOrderPushList(orderId, provinceNextId);
    }

    /**
     * 师傅小程序游客模式获取推单列表
     * 返回20条
     * @param masterAppletListOrderPushRqt masterAppletListOrderPushRqt
     * @return List<MasterAppletListOrderPushResp>
     */
    @Override
    @PostMapping("unLoginGetOrderPushList")
    public List<MasterAppletListOrderPushResp> unLoginGetOrderPushList(@RequestBody @Valid MasterAppletListOrderPushRqt masterAppletListOrderPushRqt) {
        if (!rateLimiter.tryAcquire()) {
            log.warn("rateLimiter activation!");
            throw new BusException("访问频繁,请稍后重试！");
        }
        return orderPushListService.unLoginGetOrderPushList(masterAppletListOrderPushRqt);
    }


    @Override
    @PostMapping("getMasterOrderPushList")
    public List<BatchOrderPushResp> getMasterOrderPushList(@RequestBody @Valid WaitOfferNoPageRqt waitOfferNoPageRqt){
        return orderPushListService.getMasterOrderPushList(waitOfferNoPageRqt);
    }


    @Override
    @PostMapping("getMasterOrderPushByAccount")
    public List<BatchOrderPushResp> getMasterOrderPushByAccount(@RequestBody @Valid GetMasterOrderPushByAccountRqt rqt){
        return orderPushListService.getMasterOrderPushByAccount(rqt.getProvinceNextId(),rqt.getMasterId(),rqt.getAccountId(),rqt.getAccountType());
    }


    /**
     * 查询订单推送师傅列表(供AI通知师傅接单使用)
     *
     * @return List<BatchOrderPushResp>
     */
    @Override
    @PostMapping("getOrderPushForAiNotice")
    public List<BatchOrderPushResp> getOrderPushForAiNotice(@RequestBody @Valid GetOrderPushForAiNoticeRqt rqt) {
        return orderPushListService.getOrderPushForAiNotice(rqt);
    }

    /**
     * 查询订单推送师傅列表(供AI通知师傅接单使用)
     *
     * @return List<BatchOrderPushResp>
     */
    @Override
    @PostMapping("getFamilyOrderPushList")
    public List<BatchOrderPushResp> getFamilyOrderPushList(@RequestBody @Valid GetFamilyOrderPushListRqt rqt) {
        return orderPushListService.getFamilyOrderPushList(rqt);
    }


    /**
     * 查询订单推送师傅列表(供AI通知师傅接单使用)
     *
     * @return List<BatchOrderPushResp>
     */
    @Override
    @PostMapping("getPushMasterList")
    public List<PushMasterListResp> getPushMasterList(@RequestBody @Valid GetPushMasterListRqt rqt) {
        return orderPushListService.getPushMasterList(rqt);
    }


    /**
     * 查询订单推送师傅列表(供AI通知师傅接单使用)
     *
     * @return List<BatchOrderPushResp>
     */
    @Override
    @PostMapping("getPushedExperiencedMasterList")
    public List<BatchOrderPushResp> getPushedExperiencedMasterList(@RequestBody @Valid GetPushedExperiencedMasterListRqt rqt) {
        return orderPushListService.getPushedExperiencedMasterList(rqt);
    }

    /**
     * 网站订单详情获取附近推单师傅
     * @param rqt
     * @return
     */
    @PostMapping("getSiteOrderDetailMasterList")
    @Override
    public SimplePageInfo<SiteOrderDetailMasterListResp> getSiteOrderDetailMasterList(@RequestBody @Valid SiteOrderDetailMasterListReq rqt) {
        return orderPushListService.getSiteOrderDetailMasterList(rqt);
    }


    /**
     * 网站订单详情获取订单可推单的全部师傅数
     * @param rqt
     * @return
     */
    @PostMapping("getPushMasterCountBySiteOrderDetail")
    @Override
    public SiteOrderDetailPushMasterCountResp getPushMasterCountBySiteOrderDetail(@RequestBody @Valid SiteOrderDetailPushMasterCountReq rqt) {
        return orderPushListService.getPushMasterCountBySiteOrderDetail(rqt);
    }

}
