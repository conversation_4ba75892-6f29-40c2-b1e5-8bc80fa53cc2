package com.wanshifu.domain.push.listener.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.handler.AbstractMasterBusinessTopicMessageTag;
import com.wanshifu.domain.base.handler.AbstractMatchMasterTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.listener.mq.message.FilterMaster4NoOfferMessage;
import com.wanshifu.domain.push.listener.mq.message.MasterTimeTypeChangeMessage;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.enterprise.order.domain.po.OrderBase;
import com.wanshifu.infrastructure.NoOfferFilterMaster;
import com.wanshifu.infrastructure.repository.NoOfferFilterMasterRepository;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.domain.api.request.common.GetMasterInfoRqt;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoResp;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 9:20
 */
@ConsumeTag(value = ConsumeTagEnum.TAG_JON_FULL_TIME_SHARE_TIME_MASTER)
@Slf4j
public class MasterTimeTypeMessageProcessor extends AbstractMasterBusinessTopicMessageTag<MasterTimeTypeChangeMessage> {

    @Resource
    private CommonAddressService commonAddressService;

    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;

    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;

    @Override
    public void postHandler(MasterTimeTypeChangeMessage masterTimeTypeChangeMessage) {
        log.info("masterTimeTypeChangeMessage:{}", JSON.toJSONString(masterTimeTypeChangeMessage));

        Integer oldMasterTimeType = masterTimeTypeChangeMessage.getOldMasterTimeType();
        Integer masterTimeType = masterTimeTypeChangeMessage.getMasterTimeType();


        if(!(Objects.nonNull(oldMasterTimeType) && oldMasterTimeType == 0 && masterTimeType == 1)){
            return ;
        }

        Long masterId = masterTimeTypeChangeMessage.getMasterId();

        GetMasterInfoRqt rqt = new GetMasterInfoRqt();
        rqt.setMasterId(masterId);
        GetMasterInfoResp resp = commonQueryServiceApi.getMasterInfo(rqt);
        if(Objects.isNull(resp) || Objects.isNull(resp.getCityDivisionId()) || resp.getCityDivisionId() == 0L){
            return ;
        }

        Long cityDivisionId = resp.getCityDivisionId();

        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(cityDivisionId, null, null);

        orderPushManagerGateway.updateFullTimeExclusiveOrderFlag(provinceNextIds,masterId);
    }
}
