package com.wanshifu.domain.push.listener.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.domain.base.handler.AbstractMasterOrderApiCommonTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.base.model.PushProducerTagEnum;
import com.wanshifu.domain.base.tools.TopicHelper;
import com.wanshifu.domain.push.gateway.OrderPushOperateGateway;
import com.wanshifu.domain.push.listener.mq.message.NoOfferToCallMessage;
import com.wanshifu.domain.push.model.OrderPutTagDTO;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.infrastructure.NoOfferFilterMaster;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.repository.NoOfferFilterMasterRepository;
import com.wanshifu.infrastructure.repository.OrderPushRepository;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 14:40
 */
@ConsumeTag(value = ConsumeTagEnum.NO_OFFER_TO_CALL)
@Slf4j
public class NoOfferToCallMessageProcessor extends AbstractMasterOrderApiCommonTopicMessageTag<NoOfferToCallMessage> {

    @Resource
    private OrderPushOperateGateway orderPushOperateGateway;

    @Resource
    private CommonAddressService commonAddressService;

    @Resource
    private MessageSenderService mqSendGateway;

    @Resource
    private NoOfferFilterMasterRepository noOfferFilterMasterRepository;

    @Resource
    private OrderPushRepository orderPushRepository;


    @Override
    public void postHandler(NoOfferToCallMessage noOfferToCallMessage) {
        log.info("noOfferToCallMessage:{}", JSONUtil.toJsonStr(noOfferToCallMessage));

        if (Objects.isNull(noOfferToCallMessage)
                || Objects.isNull(noOfferToCallMessage.getMasterOrderId())
                || Objects.isNull(noOfferToCallMessage.getGlobalOrderTraceId())
                || CollectionUtil.isEmpty(noOfferToCallMessage.getMasterIds())) {
            return;
        }
        Long orderId = noOfferToCallMessage.getMasterOrderId();
        Long thirdDivisionId = noOfferToCallMessage.getThirdDivisionId();
        if (Objects.isNull(thirdDivisionId) || thirdDivisionId == 0L) {
            return;
        }

        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(thirdDivisionId,
                "master_order_api_common_offer_topic.no_offer_to_call",
                JSONUtil.toJsonStr(noOfferToCallMessage));


        List<Long> resultMasterIds = noOfferToCallMessage.getMasterIds();
        //标记进入合作分区
        orderPushOperateGateway.updateOrderPushMenuCategory(provinceNextId, orderId, resultMasterIds, 1);
        //标记已打上标签
        noOfferFilterMasterRepository.updatePutFlagTagByOrderIdAndMasterIds(orderId, resultMasterIds, 1);
        //订单师傅维度打上可电话联系按钮标签
        putOrderTag(noOfferToCallMessage.getGlobalOrderTraceId(), orderId, resultMasterIds);

    }

    /**
     * 悬赏订单打标签
     *
     * @param globalOrderTraceId
     * @param orderId
     */
    private void putOrderTag(Long globalOrderTraceId, Long orderId, List<Long> masterIdList) {
        OrderPutTagDTO rqt = new OrderPutTagDTO();
        rqt.setGlobalOrderTraceId(globalOrderTraceId);
        rqt.setOrderId(orderId);
        rqt.setDimension("master");
        rqt.setMasterIds(masterIdList);
        rqt.setTagType("order_can_contact");
        rqt.setTagName("可联系订单");
        rqt.setIsDynamicTag(0);
        mqSendGateway.sendSyncMessage(TopicHelper.ORDER_PUSH_NOTICE_TOPIC, PushProducerTagEnum.ORDER_TAG.tag, JSON.toJSONString(rqt), true);
    }
}
