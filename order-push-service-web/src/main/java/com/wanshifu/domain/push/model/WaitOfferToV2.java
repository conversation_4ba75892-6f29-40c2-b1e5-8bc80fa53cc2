package com.wanshifu.domain.push.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @title wshifu-master-order-service
 * @date 2021/5/14 10:22
 */
@Data
public class WaitOfferToV2 {

    /**
     * 师傅ID
     */
    private Long masterId;
    private Long fromAccount;
    /**
     * 需要过滤的订单
     */
    private Long orderId;
    /**
     * 不同排序规则查询不同的表
     */
    private String tableName;

//    /**
//     * 随机取竞争少的订单ID
//     */
//    private List<Long> excludeOrderIds;

    /**
     * 当前时间
     */
    private Date currentDateTime = new Date();

    /**
     * 发单时间
     */
    private Date intervalDate;

    /**
     * 报价人数
     */
    private Integer offerNumber;

    /**
     * 排序规则(newOrder:新单,arrival:已到货,pushDistance:距离,residentDistance: 常驻地距离)
     */
    private String sortQueryType;

    /**
     * 区域ID
     */
    private List<Number> divisionId;

    /**
     * 指派模式(2:发布任务,3:直接指派,4:一口价,5:预付款)
     */
    private List<Number> appointType;

    /**
     * 是否是意向单(1:是,0:不是)
     */
    private Integer isIntention;

    /**
     * 订单账号标签,1:商家(site/thirdpart),2:总包(accountType=enterprise),3:宜家(ikea),4:家庭(applet)
     */
    private List<Number> orderAccountLabel;

    /**
     * 推送来源,1:智能推单,2:ocs后台
     */
    private Integer pushFrom;

    /**
     * 休息开始时间pushFrom为2时必传   师傅休息中加急单查询
     */
    private Date restStartTime;

    /**
     * 技能类型集合,1:清洁/保养/治理/美缝,4:配送并安装,5:维修服务,6:家装施工,8:安装服务,10:测量服务,13:定制家具/门类/测量/安装,16:管道疏通,17:搬运服务,18:拆旧服务,19:房屋维修
     */
    private List<Number> techniqueTypeId;

    /**
     * 订单类目id
     */
    private List<Number> categoryId;

    /**
     * 是否到货,1:已到货,2:未到货 非必传
     */
    private List<Number> isArrived;

    /**
     * 推单标识 0：正常推单 1：附近推单
     */
    private Integer pushFlag;

    /**
     * 菜单类别 1:指派专区
     */
    private Integer menuCategory;


    /**
     * 指派专区竞争少标识(0:否;1:是)
     */
    private Integer lessContendFlag;

    /**
     * 推单数据省下级地址id
     * (当前业务传订单区域id或者师傅区域id就行，order-push-service负责计算省下级地址id)
     */
    private List<Long> provinceNextIds;

    /**
     * 样板城市订单标识
     */
    private List<Integer> tmplCityFlag;


    /**
     * 订单标签，1：未看订单，2：必接订单，3：暂无报价
     */
    private List<Integer> orderFlags;

    /**
     * 师傅类型，
     * toc:c端师傅
     * tob:b端师傅
     */
    private String masterSourceType;

    /**
     * pushFlag为0时展示长尾单的一级服务id
     *
     */
    private List<Long> displayLongTailOrderLevel1ServeIds;


}
