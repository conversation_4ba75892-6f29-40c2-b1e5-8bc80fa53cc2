package com.wanshifu.domain.push.listener.mq;

import cn.hutool.json.JSONUtil;
import com.wanshifu.domain.base.handler.AbstractPushPlatformTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.push.OrderPushOperateService;
import com.wanshifu.domain.push.model.PushLessContendOrderRqt;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.order.offer.domains.po.OrderBase;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2023-10-26 17:40
 * @Description 订单竞争偏少推单消息处理
 * @Version v1
 **/
@ConsumeTag(value = ConsumeTagEnum.PUSH_LESS_CONTEND)
public class LessContendPushMessageProcessor extends AbstractPushPlatformTopicMessageTag<PushLessContendOrderRqt> {

    @Resource
    private OrderPushOperateService orderPushOperateService;

    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    @Resource
    private CommonAddressService commonAddressService;

    @Resource
    private ApolloSwitchUtils apolloSwitchUtils;

    @Override
    public void postHandler(PushLessContendOrderRqt pushLessContendOrderRqt) {
        if (!apolloSwitchUtils.isOpenIterationSwitch()){
            //飞书预警，记录当前开关生效时间
//            FeiShuUtils.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_ORDER_CENTER_BUSINESS_URL,String.format("current time: %s ; 订单竞争偏少: iteration switch status off ", DateUtils.formatDateTime(new Date())));
            return;
        }
        //获取省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(pushLessContendOrderRqt.getProvinceNextId(), "wanshifu_order_push_notice_topic.less_contend", JSONUtil.toJsonStr(pushLessContendOrderRqt));
        pushLessContendOrderRqt.setProvinceNextIdList(provinceNextId);
        orderPushOperateService.pushLessContendOrder(pushLessContendOrderRqt);
    }

    @Override
    public Long getGlobalOrderTraceId(PushLessContendOrderRqt pushLessContendOrderRqt) {
        Long orderId = pushLessContendOrderRqt.getOrderId();
        OrderBase orderBaseInfo = commonOrderOfferService.getOrderBase(orderId, null);
        return Objects.nonNull(orderBaseInfo) ? orderBaseInfo.getGlobalOrderTraceId() : 0L;
    }

}
