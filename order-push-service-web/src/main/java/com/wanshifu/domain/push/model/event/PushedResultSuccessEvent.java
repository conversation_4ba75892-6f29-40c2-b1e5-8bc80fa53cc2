package com.wanshifu.domain.push.model.event;

import com.wanshifu.domain.push.model.OrderPushCompositeRqt;
import com.wanshifu.domain.push.model.OrderPushRqt;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.po.InfoOrderBase;
import com.wanshifu.order.push.domains.dto.OrderBaseCompositeDTO;
import com.wanshifu.order.push.enums.PushScenarioType;
import org.springframework.context.ApplicationEvent;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023-09-20 23:27
 * @Description 推单结果成功通知
 * @Version v1
 **/

public class PushedResultSuccessEvent extends ApplicationEvent {

    /**
     * 业务线
     */
    private Integer businessLineId;

    /**
     * 信息订单
     */
    private InfoOrderBase infoOrderBase;

    /**
     * 推单模式
     */
    private String pushMode;

    /**
     * 推单场景
     */
    private PushScenarioType pushScenarioType;

    /**
     * 推送的师傅
     */
    private Set<Long> pushMasterIds;

    /**
     * 大数据推单对象
     */
    private OrderPushCompositeRqt orderPushRqt;




    public PushedResultSuccessEvent(OrderPushCompositeRqt orderPushRqt) {
        super(orderPushRqt);
        this.orderPushRqt = orderPushRqt;
    }


    public String getPushMode() {
        return pushMode;
    }

    public void setPushMode(String pushMode) {
        this.pushMode = pushMode;
    }



    public Set<Long> getPushMasterIds() {
        return pushMasterIds;
    }

    public void setPushMasterIds(Set<Long> pushMasterIds) {
        this.pushMasterIds = pushMasterIds;
    }



    public PushScenarioType getPushScenarioType() {
        return pushScenarioType;
    }

    public void setPushScenarioType(PushScenarioType pushScenarioType) {
        this.pushScenarioType = pushScenarioType;
    }

    public Integer getBusinessLineId() {
        return businessLineId;
    }

    public void setBusinessLineId(Integer businessLineId) {
        this.businessLineId = businessLineId;
    }

    public InfoOrderBase getInfoOrderBase() {
        return infoOrderBase;
    }

    public void setInfoOrderBase(InfoOrderBase infoOrderBase) {
        this.infoOrderBase = infoOrderBase;
    }

    public OrderPushCompositeRqt getOrderPushRqt() {
        return orderPushRqt;
    }

    public void setOrderPushRqt(OrderPushCompositeRqt orderPushRqt) {
        this.orderPushRqt = orderPushRqt;
    }
}
