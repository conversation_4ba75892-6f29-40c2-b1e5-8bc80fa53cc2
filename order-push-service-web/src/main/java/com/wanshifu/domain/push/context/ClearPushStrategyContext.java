package com.wanshifu.domain.push.context;

import com.wanshifu.domain.push.handler.clearorderpush.OrderPushClearService;
import com.wanshifu.order.push.enums.OrderPushClearType;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/4 19:02
 */
public class ClearPushStrategyContext {


    private static Map<OrderPushClearType, OrderPushClearService> maps = new HashMap<>();

    public static OrderPushClearService getInstance(OrderPushClearType node) {
        if (Objects.isNull(node) || !maps.containsKey(node)){
            throw new IllegalArgumentException(String.format("参数违法,node:%s", node));

        }
        return maps.get(node);
    }

    public static void register(OrderPushClearType node, OrderPushClearService calculateCandidate) {
        Assert.notNull(node, "type can't be null");
        maps.put(node, calculateCandidate);
    }
}
