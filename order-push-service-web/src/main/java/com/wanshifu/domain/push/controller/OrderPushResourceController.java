package com.wanshifu.domain.push.controller;

import com.wanshifu.domain.push.OrderPushResourceService;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.order.push.api.OrderPushResourceApi;
import com.wanshifu.order.push.domains.dto.OrderPushDTO;
import com.wanshifu.order.push.request.*;
import com.wanshifu.order.push.request.push.NoOfferByOrderIdRqt;
import com.wanshifu.order.push.response.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 14:23
 */
@RestController
@RequestMapping("/orderPush/resource")
public class OrderPushResourceController implements OrderPushResourceApi {

    @Resource
    protected OrderPushResourceService orderPushResourceService;

    /**
     * 批量获取推单时间
     * @param getPushTimeRqt
     * @return
     */
    @Override
    @PostMapping("batchPushTime")
    public List<BatchGetPushTimeResp> batchPushTime(@RequestBody @Validated BatchGetPushTimeRqt getPushTimeRqt) {
        return orderPushResourceService.batchPushTime(getPushTimeRqt);
    }

    /**
     * 查询意向订单推单师傅集合(全局id)
     * 网站邀请师傅报价场景，返回10条
     * @param rqt
     */
    @Override
    @PostMapping("getInvitePushMasterIds")
    public List<Long> getInvitePushMasterIds(@RequestBody @Valid GetInvitePushMasterIdsRqt rqt) {
        return orderPushResourceService.getInvitePushMasterIds(rqt);
    }


    /**
     * 查询该师傅的在途的推单订单
     *
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("selectPushOrderByMasterId")
    public List<OrderPushDTO> selectPushOrderByMasterId(@RequestBody @Valid SelectPushOrderByMasterIdRqt rqt) {
        return orderPushResourceService.selectPushOrderByMasterId(rqt);
    }

    /**
     * 订单ID和师傅ID获取订单推送信息
     */
    @PostMapping("batchOrderPush")
    @Override
    public List<BatchOrderPushResp> batchOrderPush(@RequestBody @Valid BatchOrderPushRqt batchOrderPushRqt) {
        return orderPushResourceService.batchOrderPush(batchOrderPushRqt);
    }

    /**
     * 订单ID和师傅ID批量获取订单推送信息
     */
    @PostMapping("batchGetOrderPush")
    @Override
    public List<OrderPushDTO> batchGetOrderPush(@RequestBody @Valid BatchOrderPushRqt batchOrderPushRqt) {
        return orderPushResourceService.batchGetOrderPush(batchOrderPushRqt);
    }

    /**
     * 根据orderId查询订单推送信息
     *
     * @param orderId 订单id
     * @param provinceNextId 省下级地址id (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     * @return
     */
    @PostMapping("getOrderPushByOrderId")
    @Override
    public List<OrderPushDTO> getOrderPushByOrderId(@RequestParam(value = "orderId") Long orderId, @RequestParam(value = "provinceNextId", required = false) Long provinceNextId) {
        return orderPushResourceService.getOrderPushByOrderId(provinceNextId, orderId);
    }


    /**
     * 根据orderId查询订单推送未报价信息
     *
     * @param orderId 订单id
     * @param provinceNextId 省下级地址id (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     * @return
     */
    @PostMapping("getOrderPushNoOfferByOrderId")
    @Override
    public List<OrderPushDTO> getOrderPushNoOfferByOrderId(@RequestParam(value = "orderId") Long orderId, @RequestParam(value = "provinceNextId", required = false) Long provinceNextId) {
        return orderPushResourceService.getOrderPushNoOfferByOrderId(provinceNextId, orderId);
    }

    /**
     * 根据orderId查询订单推送未报价信息 分页接口
     * @param rqt
     * @return
     */
    @PostMapping("getOrderPushNoOfferByOrderIdV2")
    @Override
    public SimplePageInfo<OrderPushDTO> getOrderPushNoOfferByOrderIdV2(@Valid @RequestBody NoOfferByOrderIdRqt rqt) {
        return orderPushResourceService.getOrderPushNoOfferByOrderIdV2(rqt);
    }

    /**
     * 订单ID和师傅ID取订单推送信息
     * @param getOrderPushRqt
     * @return
     */
    @PostMapping("getOrderPush")
    @Override
    public OrderPushDTO getOrderPush(@RequestBody @Valid GetOrderPushRqt getOrderPushRqt) {
        return orderPushResourceService.getOrderPush(getOrderPushRqt);
    }

    /**
     * 根据orderIds + masterId 批量查询订单推送信息
     * @param batchGetOrderPushRqt
     * @return
     */
    @PostMapping("batchGetOrderPushByOrderIdsAndMasterId")
    @Override
    public List<OrderPushDTO> batchGetOrderPushByOrderIdsAndMasterId(@RequestBody @Valid BatchGetOrderPushRqt batchGetOrderPushRqt) {
        return orderPushResourceService.batchGetOrderPushByOrderIdsAndMasterId(batchGetOrderPushRqt);
    }

    @Override
    @PostMapping("getHandoffTag")
    public String getHandoffTag(@RequestParam(value = "orderId") Long orderId){
        return orderPushResourceService.getHandoffTag(orderId);
    }

    /**
     * 查询师傅待报价列表订单类目占比
     * @param rqt
     * @return
     */
    @PostMapping("getMasterOrderCategoryRate")
    @Override
    public List<GetMasterOrderCategoryRateResp> getMasterOrderCategoryRate(@RequestBody @Validated GetMasterOrderCategoryRateRqt rqt) {
        return orderPushResourceService.getMasterOrderCategoryRate(rqt);
    }

    /**
     * 查询订单待报价师傅id
     * <AUTHOR>
     **/
    @PostMapping("getWaitOfferMasterIdsByOrderId")
    @Override
    public List<Long> getWaitOfferMasterIdsByOrderId(@RequestBody @Validated GetWaitOfferMasterIdsByOrderIdRqt getWaitOfferMasterIdsByOrderIdRqt) {
        return orderPushResourceService.getWaitOfferMasterIdsByOrderId(getWaitOfferMasterIdsByOrderIdRqt);
    }

    /**
     *查询师傅待报价列表可筛选订单类目选择器
     * <AUTHOR>
     **/
    @PostMapping("getMasterCategorySelector")
    @Override
    public List<Long> getMasterCategorySelector(@RequestBody @Validated GetMasterCategorySelectorRqt rqt) {
        return orderPushResourceService.getMasterCategorySelector(rqt);
    }

    /**
     * 查询师傅推送后是否已读详情
     *
     * @param getUnreadRqt
     * @return
     */
    @PostMapping("getUnread")
    @Override
    public GetUnreadResp getUnread(@RequestBody @Validated GetUnreadRqt getUnreadRqt) {
        return orderPushResourceService.getUnread(getUnreadRqt);
    }

    /**
     * 批量获取订单已查看师傅数量 (最多20条)
     * @param rqt
     * @interface
     * <AUTHOR>
     **/
    @PostMapping("batchGetMasterViewNumber")
    @Override
    public List<GetMasterViewNumberResp> batchGetMasterViewNumber(@RequestBody @Validated BatchGetMasterViewNumberRqt rqt) {
        return orderPushResourceService.batchGetMasterViewNumber(rqt);
    }

    /**
     * 批量获取订单已查看师傅数量V2 (最多20条)
     * @param rqt BatchGetMasterViewNumberV2Rqt
     * @return List<GetMasterViewNumberResp>
     */
    @Override
    @PostMapping("batchGetMasterViewNumberV2")
    public List<GetMasterViewNumberResp> batchGetMasterViewNumberV2(@RequestBody @Validated BatchGetMasterViewNumberV2Rqt rqt) {
        return orderPushResourceService.batchGetMasterViewNumberV2(rqt);
    }

    /**
     * 统计分析待报价订单指标
     * @param analyticWaitOfferCountReq
     * @return
     */
    @PostMapping("analyticWaitOfferCount")
    @Override
    public WaitOfferCountResp analyticWaitOfferCount(@RequestBody @Validated WaitOfferCountReq analyticWaitOfferCountReq) {
        return orderPushResourceService.analyticWaitOfferCount(analyticWaitOfferCountReq);
    }

    /**
     * 获取当天是否有用户直接雇佣的订单
     *
     * arms统计到师傅app调用，但师傅侧反馈已无调用
     * @param getTodayUserHireOrderRqt
     * @return
     */
    @PostMapping("getTodayUserHireOrder")
    @Override
    public GetTodayUserHireOrderResp getTodayUserHireOrder(@RequestBody @Validated GetTodayUserHireOrderRqt getTodayUserHireOrderRqt) {
        return orderPushResourceService.getTodayUserHireOrder(getTodayUserHireOrderRqt);
    }


    /**
     * 查询订单被师傅查看人数
     * @param getOrderShowRqt
     * @return
     */
    @Override
    @PostMapping("getOrderShowNumOfPeople")
    public Integer getOrderShowNumOfPeople(@RequestBody @Validated GetOrderShowRqt getOrderShowRqt) {
        return orderPushResourceService.getOrderShowNumOfPeople(getOrderShowRqt);
    }

    /**
     * 平台干预列表
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("getInterfereOrderPushList")
    public List<GetInterfereOrderPushListResp> getInterfereOrderPushList(@RequestBody @Valid GetInterfereOrderPushListRqt rqt){
        return orderPushResourceService.getInterfereOrderPushList(rqt);
    }


    /**
     * 订单推送师傅统计
     * @param orderPushCountRqt
     * @return
     */
    @PostMapping("orderPushCount")
    @Override
    public OrderPushCountResp orderPushCount(@RequestBody @Validated OrderPushCountRqt orderPushCountRqt) {
        return orderPushResourceService.orderPushCount(orderPushCountRqt);
    }

}
