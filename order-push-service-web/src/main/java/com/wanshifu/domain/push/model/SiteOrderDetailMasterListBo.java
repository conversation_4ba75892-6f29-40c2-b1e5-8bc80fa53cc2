package com.wanshifu.domain.push.model;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.Lists;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/9 15:55
 */
@Data
public class SiteOrderDetailMasterListBo {

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 推单评分
     */
    private BigDecimal pushScore;

    /**
     * 推单距离(当时推单师傅和客户得距离),单位米
     */
    private Long pushDistance;

    /**
     * 二级服务是否服务好，1：服务好，0：默认否
     */
    private Integer serveLv2IsGoodService;

    /**
     * 三级服务完工单量
     */
    private Integer serveLv3FinishOrderCnt;

    /**
     * 师傅所在二级地址id(城市id)
     */
    private Long masterSecondDivisionId;

    /**
     * 师傅所在二级地址该师傅是否是省钱师傅，1：省钱师傅，0：默认否
     */
    private Integer masterSecondDivisionIdIsSaveMoneyMaster;

    /**
     * 师傅所在二级地址该师傅是否常报最低价，1：是，0：默认否，
     */
    private Integer masterSecondDivisionIdIsUsualOfferLowPrice;

    /**
     * 师傅所在二级地址该师傅是否常抢一口价单，1：是，0：默认否
     */
    private Integer masterSecondDivisionIdIsUsualGrabOrder;

    /**
     * 师傅所在二级地址该师傅最近90天报价最低单量
     */
    private Integer masterSecondDivisionIdLatest90DayLowOfferCnt;

    /**
     * 师傅所在二级地址该师傅最近90天报价最低单量排名
     */
    private Integer masterSecondDivisionIdRankByLatest90DayLowOfferCnt;

    /**
     * 师傅三级地址三级服务完工单量排名
     */
    private Integer masterThirdDivisionIdServeLv3IdRankByFinishOrderCnt;

    /**
     * 师傅与商家是否合作过，1：合作过，0：默认否
     */
    private Integer userIdIsCooperation;

    /**
     * 商家是否收藏师傅，1：已收藏，0：默认否
     */
    private Integer userIdIsCollectMaster;

    public static void main(String[] args) {
        /*SiteOrderDetailMasterListBo b1 = new SiteOrderDetailMasterListBo();
        b1.setServeLv3FinishOrderCnt(100);
        b1.setPushScore(new BigDecimal(4.5));

        SiteOrderDetailMasterListBo b2 = new SiteOrderDetailMasterListBo();
        b2.setServeLv3FinishOrderCnt(100);
        b2.setPushScore(new BigDecimal(4.0));

        SiteOrderDetailMasterListBo b3 = new SiteOrderDetailMasterListBo();
        b3.setServeLv3FinishOrderCnt(90);
        b3.setPushScore(new BigDecimal(5.0));

        SiteOrderDetailMasterListBo b4 = new SiteOrderDetailMasterListBo();
        b4.setServeLv3FinishOrderCnt(null);
        b4.setPushScore(new BigDecimal(4.8));

        SiteOrderDetailMasterListBo b5 = new SiteOrderDetailMasterListBo();
        b5.setServeLv3FinishOrderCnt(null);
        b5.setPushScore(new BigDecimal(3.0));

        List<SiteOrderDetailMasterListBo> boList = new ArrayList<>();
        boList.add(b3);
        boList.add(b5);
        boList.add(b1);
        boList.add(b4);
        boList.add(b2);
        boList.forEach(bo -> System.out.println(bo.getServeLv3FinishOrderCnt() + ":" + bo.getPushScore()+"\n"));

        System.out.println("-----------------------");
        boList.sort(
                Comparator.comparing(SiteOrderDetailMasterListBo::getServeLv3FinishOrderCnt,
                                Comparator.nullsLast(Comparator.reverseOrder())
                        )
                        // 完工量相同时，按评分倒序（评分不会为null）
                        .thenComparing(
                                SiteOrderDetailMasterListBo::getPushScore,
                                Comparator.reverseOrder()
                        )
        );
        boList.forEach(bo -> System.out.println(bo.getServeLv3FinishOrderCnt() + ":" + bo.getPushScore()+"\n"));*/


        List<String> collectMasterList = Lists.newArrayList("收藏师傅","收藏师傅","收藏师傅","收藏师傅","收藏师傅");
        List<String> cooperationMasterList = Lists.newArrayList("合作过师傅","合作过师傅");
        List<String> goodServiceMasterList = Lists.newArrayList();
        List<String> saveMoneyMasterList = Lists.newArrayList("省钱师傅","省钱师傅","省钱师傅","省钱师傅","省钱师傅","省钱师傅","省钱师傅","省钱师傅");
        List<String> normalMasterList = Lists.newArrayList("普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅","普通师傅");

        List<String> resultList = combineMasters(
                collectMasterList,
                cooperationMasterList,
                goodServiceMasterList,
                saveMoneyMasterList,
                normalMasterList
        );

        List<List<String>> res = ListUtil.split(resultList, 6);
        for (List<String> list : res) {
            for (String s : list) {
                System.out.println(s+"\n");
            }
            System.out.println("---------------------------");
        }
    }


    public static List<String> combineMasters(
            List<String> collectMasterList,
            List<String> cooperationMasterList,
            List<String> goodServiceMasterList,
            List<String> saveMoneyMasterList,
            List<String> normalMasterList) {

        // 使用队列以便高效移除元素
        Queue<String> collectQueue = new LinkedList<>(collectMasterList);
        Queue<String> coopQueue = new LinkedList<>(cooperationMasterList);
        Queue<String> serviceQueue = new LinkedList<>(goodServiceMasterList);
        Queue<String> moneyQueue = new LinkedList<>(saveMoneyMasterList);
        Queue<String> normalQueue = new LinkedList<>(normalMasterList);

        List<String> combinedList = new ArrayList<>();
        final int PAGE_SIZE = 6;
        int totalMasters = collectMasterList.size() + cooperationMasterList.size()
                + goodServiceMasterList.size() + saveMoneyMasterList.size()
                + normalMasterList.size();

        // 持续组合直到所有数据用完
        while (totalMasters > 0) {
            List<String> page = new ArrayList<>(PAGE_SIZE);

            // 步骤1: 按比例分配非普通标签
            addFromQueue(collectQueue, page, 1, PAGE_SIZE);  // 收藏师傅 (1)
            addFromQueue(coopQueue, page, 1, PAGE_SIZE);     // 合作师傅 (1)
            addFromQueue(serviceQueue, page, 2, PAGE_SIZE);  // 服务好师傅 (2)
            addFromQueue(moneyQueue, page, 2, PAGE_SIZE);    // 省钱师傅 (2)

            // 步骤2: 优先级补充 (非普通标签)
            while (page.size() < PAGE_SIZE &&
                    (!collectQueue.isEmpty() || !coopQueue.isEmpty() ||
                            !serviceQueue.isEmpty() || !moneyQueue.isEmpty())) {

                if (!collectQueue.isEmpty()) {
                    page.add(collectQueue.poll());
                } else if (!coopQueue.isEmpty()) {
                    page.add(coopQueue.poll());
                } else if (!serviceQueue.isEmpty()) {
                    page.add(serviceQueue.poll());
                } else if (!moneyQueue.isEmpty()) {
                    page.add(moneyQueue.poll());
                }
            }

            // 步骤3: 用普通标签补足
            while (page.size() < PAGE_SIZE && !normalQueue.isEmpty()) {
                page.add(normalQueue.poll());
            }

            combinedList.addAll(page);
            totalMasters -= page.size();
        }

        return combinedList;
    }

    // 辅助方法：从队列中添加指定数量的元素
    private static void addFromQueue(Queue<String> queue,
                                     List<String> page,
                                     int count, int pageSize) {
        for (int i = 0; i < count && page.size() < pageSize && !queue.isEmpty(); i++) {
            page.add(queue.poll());
        }
    }


}
