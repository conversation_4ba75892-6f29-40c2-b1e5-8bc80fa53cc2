package com.wanshifu.domain.push.model;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 推送竞争少订单.
 *
 * <AUTHOR>
 * @since 2023-10-19 17:15:32
 */
@Data
public class PushLessContendOrderRqt {

    /**
     * 订单id
     */
    @NotNull
    @Min(1)
    private Long orderId;

    /**
     * 师傅id列表
     */
    @NotNull
    @Size(min = 1, max = 100)
    private List<Long> masterIdList;

    /**
     * 省下级地址id
     * 当前上游传订单区域id，order-push-service计算出省下级地址id
     */
    private Long provinceNextId;

    private List<Long> provinceNextIdList;
}