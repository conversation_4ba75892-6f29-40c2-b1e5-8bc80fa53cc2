package com.wanshifu.domain.push.listener.mq.message;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/7 18:26
 */
@Data
public class FilterMaster4NoOfferMessage {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 全局订单id
     */
    private Long globalOrderId;

    /**
     * 筛选的师傅信息
     */
    private List<OrderPushMaster> masterList;

    @Data
    public static class OrderPushMaster {
        /**
         * 师傅id
         */
        private Long masterId;

        /**
         * 推单距离
         */
        private Long pushDistance;

        /**
         * 推单距离类型
         */
        private Integer pushDistanceType;

        /**
         * 师傅经纬度
         */
        private BigDecimal masterLongitude;

        /**
         * 师傅经纬度
         */
        private BigDecimal masterLatitude;

        /**
         * 是否技能相关
         */
        private Integer accordingTechnologyPushFlag = 1;

        private Integer recruitId;

        private String tagName;

        /**
         * 推送标记：main_master, reserve_master
         */
        private String pushTag;

        private BigDecimal cooperationPrice;

        private Integer sort;

        /**
         * 跨城推送
         */
        private Integer crossCityPush;

        private BigDecimal score;

        /**
         * 自动报价金额
         */
        private BigDecimal autoPrice;

        private Integer autoOfferSort;

    }
}
