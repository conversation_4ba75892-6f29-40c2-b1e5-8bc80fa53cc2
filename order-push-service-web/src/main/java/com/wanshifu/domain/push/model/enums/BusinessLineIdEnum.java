package com.wanshifu.domain.push.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 业务线ID枚举
 */
public enum BusinessLineIdEnum {

    /**
     * 1:成品业务线
     */
    ONE(1, "成品业务线"),
    /**
     * 2:家庭业务线
     */
    TWO(2, "家庭业务线"),

    /**
     * 3:创新业务
     */
    THREE(3, "创新业务线");

    public final int id;

    public final String cn;

    BusinessLineIdEnum(int id, String cn) {
        this.id = id;
        this.cn = cn;
    }

    private static final Map<Integer, BusinessLineIdEnum> MAPPING_MAP = new HashMap<>((int) (BusinessLineIdEnum.values().length / 0.75));

    static {
        for (BusinessLineIdEnum instance : values()) {
            MAPPING_MAP.put(instance.id, instance);
        }
    }

    public static BusinessLineIdEnum fromString(Integer code) {
        return MAPPING_MAP.get(code);
    }
}
