package com.wanshifu.domain.push.ability;

import com.wanshifu.domain.push.model.OrderPushRqt;
import com.wanshifu.domain.push.model.enums.BusinessLineIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023-09-22 09:55
 * @Description
 * @Version v1
 **/
@Slf4j
@Service
public class FinishedProductOrderPushResultHandler extends OrderPushResultHandlerSupport {

    @Override
    public boolean matching(OrderPushRqt orderPushRqt) {
        return orderPushRqt.getBusinessLineId() == BusinessLineIdEnum.ONE.id;
    }
}
