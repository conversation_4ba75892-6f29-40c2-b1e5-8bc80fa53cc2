package com.wanshifu.domain.push.model;

import com.wanshifu.order.offer.domains.po.InfoOrderBase;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class InnovateBusinessPushResp {

    /**
     * 订单基础信息
     */
    private InfoOrderBase infoOrderBase;

    /**
     * 推单的师傅集
     */
    private List<Long> pushMasterIds;

    /**
     * 首次推单师傅集合
     */
    private Set<Long> firstPushMasterIds;

}
