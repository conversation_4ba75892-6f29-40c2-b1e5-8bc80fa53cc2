package com.wanshifu.domain.push.listener.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.google.common.collect.Lists;
import com.wanshifu.domain.agent.gateway.AgentPushDetailManagerGateway;
import com.wanshifu.domain.base.ability.DTOFormatAbilityService;
import com.wanshifu.domain.base.handler.AbstractCommonOrderGeneralTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.base.model.PushSinkVersionEnum;
import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.push.model.MasterOfferPriceMessage;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.socre.ability.OrderPushSortScoreService;
import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.infrastructure.OrderDistanceConditionConfig;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.repository.OrderDistanceConditionConfigRepository;
import com.wanshifu.infrastructure.repository.OrderPushRepository;
import com.wanshifu.order.offer.domains.api.response.SimpleOrderGrab;
import com.wanshifu.order.offer.domains.enums.offer.OfferSourceEnum;
import com.wanshifu.order.offer.domains.vo.GeneralMasterGrabMessage;
import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderExtraDataDTO;
import com.wanshifu.order.push.domains.dto.OrderGrabDTO;
import com.wanshifu.order.push.enums.PushBusinessCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 师傅报价
 * @date 2024/3/14 15:18
 */
@ConsumeTag(value = ConsumeTagEnum.GENERAL_MASTER_GRAB)
@Slf4j
public class MasterGeneralGrabMessageProcessor extends AbstractCommonOrderGeneralTopicMessageTag<GeneralMasterGrabMessage> {


    @Resource
    private OrderPushSortScoreService orderPushSortScoreService;

    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    @Resource
    private ApolloSwitchUtils apolloSwitchUtils;

    @Resource
    private CommonAddressService commonAddressService;

    @Resource
    private AgentPushDetailManagerGateway agentPushDetailManagerGateway;

    @Resource
    private OrderDistanceConditionConfigRepository orderDistanceConditionConfigRepository;


    @Resource
    private OrderPushRepository orderPushRepository;


    @Resource
    private SpecialOrderGateway specialOrderGateway;


    @Value("${order.sideOrder.distance.limitCount:20}")
    private int sideOrderLimitCount;

    @Resource
    private Executor otherBusDiscardPolicyExecutor;

    @Override
    public void postHandler(GeneralMasterGrabMessage generalMasterGrabMessage) {
        log.info("interface.simplify switch,orderInfo:{}", JSONUtil.toJsonStr(generalMasterGrabMessage));
        if(!apolloSwitchUtils.isOpenIterationVersionSwitch(PushSinkVersionEnum.PUSH_SINK_20240320.getVersion())){
            return;
        }


        SimpleOrderGrab simpleOrderGrab = commonOrderOfferService.getSimpleOrderGrabByGlobalId(generalMasterGrabMessage.getGlobalOrderTraceId());
        if(Objects.isNull(simpleOrderGrab) || Objects.isNull(simpleOrderGrab.getOrderBase())){
            throw new BusException(PushBusinessCode.IGNORE_ERROR.code, "订单不存在");
        }

        Long thirdDivisionId = simpleOrderGrab.getOrderBase().getThirdDivisionId();
        if (Objects.isNull(thirdDivisionId) || thirdDivisionId == 0L) {
            return;
        }
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(thirdDivisionId,
                "topic_common_order_general.master_offerPrice",
                JSONUtil.toJsonStr(generalMasterGrabMessage));


        CompletableFuture.runAsync(() ->agentPushDetailManagerGateway.grabOrder(simpleOrderGrab.getOrderBase().getOrderId(), generalMasterGrabMessage.getMasterId(), generalMasterGrabMessage.getGrabTime()), otherBusDiscardPolicyExecutor);



        }

}
