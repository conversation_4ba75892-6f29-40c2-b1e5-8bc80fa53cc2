package com.wanshifu.domain.push.controller;

import com.wanshifu.domain.push.listener.mq.MasterBountyMessageProcessor;
import com.wanshifu.domain.push.listener.mq.MasterOfferPriceSuccessMessageProcessor;
import com.wanshifu.domain.push.listener.mq.message.MasterBountyMessage;
import com.wanshifu.domain.push.model.MasterOfferPriceMessage;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/17 16:48
 */
@RestController
@RequestMapping("/orderPush/mqTest")
public class MqTestController {

    @Resource
    private MasterBountyMessageProcessor masterBountyMessageProcessor;


    @Resource
    private MasterOfferPriceSuccessMessageProcessor masterOfferPriceSuccessMessageProcessor;

    @PostMapping("/masterBountyUpdate")
    public void masterBountyUpdate(@RequestBody MasterBountyMessage masterBountyMessage) {
        masterBountyMessageProcessor.postHandler(masterBountyMessage);
    }

    @PostMapping("/masterOfferPriceUpdate")
    public void masterOfferPriceUpdate(@RequestBody MasterOfferPriceMessage masterOfferPriceMessage) {
        masterOfferPriceSuccessMessageProcessor.postHandler(masterOfferPriceMessage);
    }
}
