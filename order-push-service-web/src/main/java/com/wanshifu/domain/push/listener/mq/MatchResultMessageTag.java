package com.wanshifu.domain.push.listener.mq;

import com.google.common.base.Preconditions;
import com.wanshifu.domain.base.handler.AbstractMatchMasterTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.base.tools.FeiShuUtils;
import com.wanshifu.domain.push.ability.OrderPushResultHandler;
import com.wanshifu.domain.push.model.OrderPushRqt;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024-03-06 11:21
 * @Description 匹配结果消息(推单结果通知）
 * @Version v1
 **/
@ConsumeTag(ConsumeTagEnum.MATCH_RESULT)
public class MatchResultMessageTag extends AbstractMatchMasterTopicMessageTag<OrderPushRqt> {

    @Autowired
    private List<OrderPushResultHandler<OrderPushRqt>> orderPushResultHandlerList;


    @Override
    public void postHandler(OrderPushRqt orderPushRqt) {
        if (orderPushRqt.getFirstPush() == 1) {
            Preconditions.checkNotNull(orderPushRqt.getPushDivisionLevel(), "首次推送时，推送地址缺失");
        }
        OrderPushResultHandler<OrderPushRqt> orderPushResultHandler = orderPushResultHandlerList.stream().filter(e -> e.matching(orderPushRqt)).findFirst().orElse(null);
        if (Objects.isNull(orderPushResultHandler)) {
            FeiShuUtils.sendTempMsg("推单结果通知",orderPushRqt , "订单未匹配到推单结果处理器");
            return;
        }
        orderPushResultHandler.postProcessor(orderPushRqt);

    }


    @Override
    public Long getGlobalOrderTraceId(OrderPushRqt orderPushRqt) {
        return orderPushRqt.getGlobalOrderTraceId();
    }
}
