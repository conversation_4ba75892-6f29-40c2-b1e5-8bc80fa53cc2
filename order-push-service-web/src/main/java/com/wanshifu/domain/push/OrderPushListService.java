package com.wanshifu.domain.push;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.order.push.request.*;
import com.wanshifu.order.push.request.push.MasterAppletListOrderPushRqt;
import com.wanshifu.order.push.request.push.TmplCityOrderPushRqt;
import com.wanshifu.order.push.response.*;
import com.wanshifu.order.push.response.push.MasterAppletListOrderPushResp;
import com.wanshifu.order.push.response.push.TmplCityOrderPushResp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 14:29
 */
public interface OrderPushListService {

    /**
     *  师傅待报价订单列表--特殊列表
     * @param rqt WaitOfferSpecialListRqt
     * @return SimplePageInfo<WaitOfferV2Resp>
     */
    SimplePageInfo<WaitOfferV2Resp> waitOfferSpecialList(WaitOfferSpecialListRqt rqt);


    /**
     * 师傅待报价订单列表V2
     * @param waitOfferRqt WaitOfferV2Rqt
     * @return SimplePageInfo<WaitOfferV2Resp>
     */
    SimplePageInfo<WaitOfferV2Resp> waitOfferV2(WaitOfferV2Rqt waitOfferRqt);

    /**
     * 家庭模版城市订单推单列表
     * @param rqt TmplCityOrderPushRqt
     * @return SimplePageInfo<TmplCityOrderPushResp>
     */
    SimplePageInfo<TmplCityOrderPushResp> tmplCityOrderPushList(TmplCityOrderPushRqt rqt);

    /**
     * 师傅待报价订单列表不分页
     * @param waitOfferNoPageRqt WaitOfferNoPageRqt
     */
    List<WaitOfferNoPageResp> waitOfferNoPage(WaitOfferNoPageRqt waitOfferNoPageRqt);

    /**
     * IOC(智能运营)活动待报价-筛选数据
     * @param rqt IocWaitOfferFilterRqt
     * @return List<IocWaitOfferFilterResp>
     */
    List<IocWaitOfferFilterResp> getIocWaitOfferList(IocWaitOfferFilterRqt rqt);


    /**
     * 批量获取订单待报价信息
     * @param batchGetOrderWaitOfferInfoReq BatchGetOrderWaitOfferInfoReq
     * @return List<WaitOfferV2Resp>
     */
    List<WaitOfferV2Resp> batchGetOrderWaitOfferInfo(BatchGetOrderWaitOfferInfoReq batchGetOrderWaitOfferInfoReq);

    /**
     * 获取订单推单消息（master-notice-service调用）
     *
     * @param provinceNextId 省下级地址id
     * @param orderId orderId
     * @return List<BatchOrderPushResp>
     */
    List<BatchOrderPushResp> getOrderPushForNotice(Long orderId, Long provinceNextId);

    /**
     * 获取订单推单列表
     *
     * @param provinceNextId 省下级地址id
     * @param orderId orderId 订单id
     * @return List<BatchOrderPushResp>
     */
    List<BatchOrderPushResp> getOrderPushList(Long orderId, Long provinceNextId);

    /**
     * 师傅小程序游客模式获取推单列表
     * 返回20条
     * @param rqt masterAppletListOrderPushRqt
     * @return List<MasterAppletListOrderPushResp>
     */
    List<MasterAppletListOrderPushResp> unLoginGetOrderPushList(MasterAppletListOrderPushRqt rqt);

    List<BatchOrderPushResp> getMasterOrderPushList(WaitOfferNoPageRqt waitOfferNoPageRqt);


    List<BatchOrderPushResp> getMasterOrderPushByAccount(Long provinceNextId,Long masterId,Long accountId,String accountType);


    List<BatchOrderPushResp> getOrderPushForAiNotice(GetOrderPushForAiNoticeRqt rqt);


    List<BatchOrderPushResp> getFamilyOrderPushList(GetFamilyOrderPushListRqt rqt);

    List<PushMasterListResp> getPushMasterList(GetPushMasterListRqt rqt);


    List<BatchOrderPushResp> getPushedExperiencedMasterList(GetPushedExperiencedMasterListRqt rqt);


    /**
     * 网站订单详情获取订单可推单的全部师傅数
     * @param rqt
     * @return
     */
    SiteOrderDetailPushMasterCountResp getPushMasterCountBySiteOrderDetail(SiteOrderDetailPushMasterCountReq rqt);


    /**
     * 网站订单详情获取附近推单师傅
     * @param rqt
     * @return
     */
    SimplePageInfo<SiteOrderDetailMasterListResp> getSiteOrderDetailMasterList(SiteOrderDetailMasterListReq rqt);

}

