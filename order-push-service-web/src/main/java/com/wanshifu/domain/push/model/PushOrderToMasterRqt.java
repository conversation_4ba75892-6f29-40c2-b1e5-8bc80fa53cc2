package com.wanshifu.domain.push.model;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.order.push.request.push.ExclusiveOrderLabel;
import com.wanshifu.order.push.request.push.MasterAddressInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;


/**
 * 订单推送类
 */
@Data
public class PushOrderToMasterRqt {



    @NotNull
    @Min(1L)
    private Long masterId;

    @NotNull
    private Long cityDivisionId;


    /**
     * 推单业务类型
     * direct_assignment_push: 直接指派推单；
     * direct_push: 直接推单
     * smart_push：智能推单;
     */
    @ValueIn(value = "direct_assignment_push,direct_push,smart_push",required = true)
    private String pushScenarioType;




    /**
     * pre_exclusive:专属订单批量推送
     * pre_exclusive_single:专属订单只有一个专属师傅，系统自动报价
     * agent:代理商 v7.6
     * package_order:订单包 v7.7.2
     * brand: 品牌师傅
     * normal:普通推单模式 (默认)
     * new_model:样板城市推单
     * new_model_single:样板城市推单（只推主力师傅）
     */
    private String pushMode;


    /**
     * 师傅经度
     */
    private BigDecimal masterLongitude;

    /**
     * 师傅纬度
     */
    private BigDecimal masterLatitude;




    /**
     * 推送师傅信息
     */
    @Valid
    @NotEmpty
    private List<PushOrderDetailInfo> pushOrderDetailInfoList;



    /**
     * 推送的地址级别（3：三级地址推单，4：四级地址推单）
     */
    @Value("3,4")
    private Integer pushDivisionLevel;


    /**
     * 师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    private String masterSourceType;

    private String matchSceneCode;

    /**
     * 推单标识 0：正常推单 1：附近推单
     */
    private Integer pushFlag;

    /**
     * 是否按照距离推单
     */
    private Boolean accordingDistancePushFlag;


    @Data
    public static class PushOrderDetailInfo{
        /**
         * 全局id
         */
        private Long globalOrderTraceId;


        private Long orderId;

        /**
         * 业务线id
         */
        @NotNull
        @Min(1)
        private Integer businessLineId;




        /**
         * 师傅常驻地址和客户之间的路径规划距离
         */
        private Long pushDistance;

        /**
         * 推单距离类型 1：导航距离(默认) 2:直线距离
         */
        private Integer pushDistanceType;

        /**
         * 是否按照技能推单 1:是 0:否
         */
        private Integer accordingTechnologyPushFlag = 1;


        /**
         * 1: 跨城市推单，0：非跨城市推单
         */
        private Integer crossCityPush;


        /**
         * 是否首次推单
         */
        @NotNull
        @Min(0)
        private Integer firstPush;

        /**
         * 普通推单首次有效推单(首轮首次)
         * 0：非，1：是
         */
        private Integer firstTimeValidPush;


        /**
         * 推送的地址级别（3：三级地址推单，4：四级地址推单）
         */
        @Value("3,4")
        private Integer pushDivisionLevel;



    }

}
