package com.wanshifu.domain.push.handler.clearorderpush.impl;

import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.push.context.ClearPushStrategyContext;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.handler.clearorderpush.OrderPushClearService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.push.enums.OrderPushClearType;
import com.wanshifu.order.push.request.push.ClearOrderPushRqt;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/4 18:55
 */
@Component
public class GrabDefiniteClearPushServiceImpl implements OrderPushClearService, InitializingBean {

    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;

    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    @Override
    public int clear(ClearOrderPushRqt clearOrderPushRqt) {
        Long orderId = clearOrderPushRqt.getOrderId();
        Long masterId = clearOrderPushRqt.getMasterId();

        OrderBaseComposite orderBaseComposite = commonOrderOfferService.getOrderBaseComposite(orderId);
        if(Objects.isNull(orderBaseComposite)){
            return 0;
        }

//        OrderExclusiveTagResp resp = commonOrderOfferService.getOrderExclusiveTag(orderId, masterId, "skill_task_order");

        orderPushManagerGateway.clearOrderPushByOrderIdForGrabDefinite(clearOrderPushRqt.getProvinceNextIds(), clearOrderPushRqt.getOrderId());

        if(CollectionUtils.isNotEmpty(clearOrderPushRqt.getBusinessParams()) &&
                clearOrderPushRqt.getBusinessParams().contains("technique_verify_order")){
            Long categoryId = Long.valueOf(orderBaseComposite.getOrderBase().getCategoryId());
            orderPushManagerGateway.clearTechniqueVerifyOrderForGrabDefinite(clearOrderPushRqt.getProvinceNextIds(),masterId,categoryId);
        }

        return 1;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ClearPushStrategyContext.register(OrderPushClearType.GRAB_DEFINITE, this);
    }
}
