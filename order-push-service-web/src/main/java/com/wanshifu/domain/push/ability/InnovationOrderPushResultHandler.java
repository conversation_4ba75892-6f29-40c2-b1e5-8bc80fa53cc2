package com.wanshifu.domain.push.ability;
import com.wanshifu.domain.push.model.OrderPushRqt;
import com.wanshifu.domain.push.model.enums.BusinessLineIdEnum;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023-09-22 09:57
 * @Description
 * @Version v1
 **/
@Service
public class InnovationOrderPushResultHandler extends OrderPushResultHandlerSupport {


    @Override
    public boolean matching(OrderPushRqt orderPushRqt) {
        return orderPushRqt.getBusinessLineId() == BusinessLineIdEnum.THREE.id;
    }


    @Override
    public void postProcessor(OrderPushRqt orderPushRqt) {
        orderPushOperateService.innovateBusinessPush(orderPushRqt);
    }
}
