package com.wanshifu.domain.push.gateway;


import cn.hutool.core.date.DateTime;
import com.wanshifu.domain.push.model.BatchGetMasterViewNumberV2RqtBo;
import com.wanshifu.domain.push.model.GetMasterOrderCategoryRateBo;
import com.wanshifu.domain.push.model.WaitOfferCountGateway;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.push.request.GetTodayUserHireOrderRqt;
import com.wanshifu.order.push.request.GetWaitOfferMasterIdsByOrderIdRqt;
import com.wanshifu.order.push.request.push.NoOfferByOrderIdRqt;
import com.wanshifu.order.push.response.GetMasterViewNumberResp;
import com.wanshifu.order.push.response.GetTodayUserHireOrderResp;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 10:33
 */
public interface OrderPushResourceGateway {
    /**
     * 查询师傅待报价列表订单类目占比.
     *
     * @param masterIdList 师傅ids
     * @param provinceNextId 省下级地址id
     * @return
     */
    List<GetMasterOrderCategoryRateBo> getMasterOrderCategoryRate(List<Long> provinceNextId, List<Long> masterIdList);

    /**
     * 分页查询已推单师傅
     * @param rqt
     * @return
     */
    List<Long> getWaitOfferMasterIdsByOrderId(List<Long> provinceNextId, GetWaitOfferMasterIdsByOrderIdRqt rqt);

    /**
     * 查询师傅待报价列表可筛选订单类目id集合
     * @param provinceNextId 省下级地址id
     * @param masterId 师傅id
     * @param currentDateTime 当前时间
     * @return
     */
    List<Long> selectMasterCategorySelector(List<Long> provinceNextId, Long masterId, DateTime currentDateTime);

    /**
     * 查询师傅推送后是否已读详情
     *
     * @return
     */
    List<Long> getUnread(List<Long> provinceNextId, OrderBase orderBase, OrderGrab orderGrab);

    GetMasterViewNumberResp batchGetMasterViewNumberV2(BatchGetMasterViewNumberV2RqtBo rqtBo, OrderBase orderBase);

    Integer analyticWaitOfferCount(WaitOfferCountGateway waitOfferCountGateway);

    Integer analyticWaitOfferCountV2(WaitOfferCountGateway waitOfferCountGateway);

    GetTodayUserHireOrderResp getTodayUserHireOrder(List<Long> provinceNextId, GetTodayUserHireOrderRqt getTodayUserHireOrderRqt);

    /**
     * 查询师傅正常推单记录
     * @param provinceNextId 省下级地址id
     * @param masterId 师傅id
     * @return
     */
    List<OrderPush> getMasterNormalOrderPushList(List<Long> provinceNextId, Long masterId);

    /**
     * 根据orderIds + masterId 批量查询订单推送信息
     *
     * @param provinceNextId 省下级地址id
     * @param orderIds 订单ids
     * @param masterId 师傅id
     * @return
     */
    List<OrderPush> batchGetOrderPushByOrderIdsAndMasterId(List<Long> provinceNextId, List<Long> orderIds, Long masterId);

    /**
     * 订单ID和师傅ID取订单推送信息
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     * @return
     */
    OrderPush getOrderPush(List<Long> provinceNextId, Long orderId, Long masterId);

    /**
     * 订单ID和师傅ID取订单推送信息
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     * @return
     */
    List<OrderPush> getMasterOrderPush(List<Long> provinceNextId, Long masterId,Integer appointType,Integer limitCount);

    /**
     * 查询订单被查看人数
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    Integer getOrderShowNumOfPeople(List<Long> provinceNextId, Long orderId);

    /**
     * 根据orderId查询订单推送信息
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    List<OrderPush> getOrderPushByOrderId(List<Long> provinceNextId, Long orderId);

    /**
     * 根据orderId查询订单推送未报价信息
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    List<OrderPush> getOrderPushNoOfferByOrderId(List<Long> provinceNextId, Long orderId);

    /**
     * 根据orderId查询订单推送未报价信息
     * @param rqt
     * @return
     */
    SimplePageInfo<OrderPush> getOrderPushNoOfferByOrderIdV2(NoOfferByOrderIdRqt rqt);


    /**
     * 推单师傅统计
     * @param provinceNextId
     * @param orderId
     * @param mode
     * @return
     */
    Integer orderPushCount(List<Long> provinceNextId, Long orderId,Integer mode);


}
