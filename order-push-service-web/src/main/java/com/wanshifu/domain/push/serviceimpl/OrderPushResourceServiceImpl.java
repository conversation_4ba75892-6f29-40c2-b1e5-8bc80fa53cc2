package com.wanshifu.domain.push.serviceimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.base.tools.BeanEnhanceUtil;
import com.wanshifu.domain.interfereOrderPush.InterfereOrderPushListService;
import com.wanshifu.domain.push.OrderPushResourceService;
import com.wanshifu.domain.push.ability.OrderPushAbilityService;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.gateway.OrderPushResourceGateway;
import com.wanshifu.domain.push.model.BatchGetMasterViewNumberV2RqtBo;
import com.wanshifu.domain.push.model.GetMasterOrderCategoryRateBo;
import com.wanshifu.domain.push.model.WaitOfferCountGateway;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DataUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.InterfereOrderPush;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.order.offer.domains.api.response.OrderBaseSampleComposite;
import com.wanshifu.order.offer.domains.api.response.SimpleOrderGrab;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderGrabDTO;
import com.wanshifu.order.push.domains.dto.OrderPushDTO;
import com.wanshifu.order.push.enums.PushBusinessCode;
import com.wanshifu.order.push.request.*;
import com.wanshifu.order.push.request.push.NoOfferByOrderIdRqt;
import com.wanshifu.order.push.response.*;
import com.wanshifu.order.push.response.tmplcity.ListTmplCityOrderRecommendMasterRespV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 14:29
 */
@Service
@Slf4j
public class OrderPushResourceServiceImpl implements OrderPushResourceService {

    // 待报价列表根据类目筛选：[1:开启；0:关闭-默认]
    @Value("${order.category.selector:0}")
    private Integer categorySelector;

    @Value("${degrade.wait-offer-count.switch:off}")
    private String waitOfferCountSwitch;


    @Value("${degrade.order-push-count.switch:off}")
    private String orderPushCountDegradeSwitch;

    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;

    @Resource
    private OrderPushAbilityService orderPushAbilityService;

    @Resource
    private OrderPushResourceGateway orderPushResourceGateway;

    @Resource
    private CommonAddressService commonAddressService;

    @Resource(name = "analyticWaitOfferCountExecutor")
    private Executor analyticWaitOfferCountExecutor;


    @Resource
    private ApolloSwitchUtils apolloSwitchUtils;

    /**
     * 单独处理的线程池
     */
    public static ThreadPoolExecutor threadPoolExecutor
            = new ThreadPoolExecutor(10, Integer.MAX_VALUE, 60,
            TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
            new ThreadPoolExecutor.CallerRunsPolicy());


    @Override
    public List<BatchGetPushTimeResp> batchPushTime(BatchGetPushTimeRqt getPushTimeRqt) {
        //计算省下级地址id
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(getPushTimeRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.batchPushTime",
                JSONUtil.toJsonStr(getPushTimeRqt));
        List<OrderPush> orderPushes = orderPushManagerGateway.selectByMasterIdAndOrderIds(provinceNextIds, getPushTimeRqt.getMasterId(), getPushTimeRqt.getOrderIds());
        return orderPushes.stream().map(op -> DataUtils.copyObject(op, BatchGetPushTimeResp.class)).collect(Collectors.toList());
    }

    @Override
    public List<Long> getInvitePushMasterIds(GetInvitePushMasterIdsRqt rqt) {
        OrderBase orderBase = commonOrderOfferService.getOrderBase(null, rqt.getGlobalOrderTraceId());
        if (orderBase == null) {
            return Collections.emptyList();
        }
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getInvitePushMasterIds",
                JSONUtil.toJsonStr(rqt));
        return orderPushManagerGateway.selectInviteMasterByOrderId(provinceNextId, orderBase.getOrderId());
    }


    /**
     * 查询该师傅的在途的推单订单
     *
     * @param rqt
     * @return
     */
    @Override
    public List<OrderPushDTO> selectPushOrderByMasterId(SelectPushOrderByMasterIdRqt rqt) {
        Long masterId = rqt.getMasterId();
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.selectPushOrderByMasterId", JSONUtil.toJsonStr(rqt));
        List<OrderPush> orderPushes = orderPushManagerGateway.selectPushOrderByMasterId(provinceNextId, masterId);
        if (CollectionUtils.isEmpty(orderPushes)) {
            return new ArrayList<>();
        }
        return orderPushes.stream().map(orderPush -> {
            OrderPushDTO orderPushDTO = new OrderPushDTO();
            BeanUtils.copyProperties(orderPush, orderPushDTO);
            return orderPushDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BatchOrderPushResp> batchOrderPush(BatchOrderPushRqt batchOrderPushRqt) {

        Long orderId = batchOrderPushRqt.getOrderId();
        List<Long> masterId = batchOrderPushRqt.getMasterId();
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(batchOrderPushRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.batchOrderPush", JSONUtil.toJsonStr(batchOrderPushRqt));

        List<OrderPush> orderPushes = orderPushManagerGateway.queryOrderToMasterIdsPushInfo(provinceNextId, orderId, masterId);
        if (CollUtil.isNotEmpty(orderPushes)) {
            return DataUtils.copyList(BatchOrderPushResp.class, orderPushes);
        }
        return null;
    }


    @Override
    public String getHandoffTag(Long orderId) {
        OrderBase orderBaseInfo = commonOrderOfferService.getOrderBase(orderId, null);
        return orderPushAbilityService.getPushDockingHandoffTag(orderBaseInfo.getThirdDivisionId());
    }

    @Override
    public List<GetMasterOrderCategoryRateResp> getMasterOrderCategoryRate(GetMasterOrderCategoryRateRqt rqt) {
        //获取省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getMasterOrderCategoryRate", JSONUtil.toJsonStr(rqt));
        List<GetMasterOrderCategoryRateBo> masterOrderCategoryRate = orderPushResourceGateway.getMasterOrderCategoryRate(provinceNextId, rqt.getMasterIdList());
        if (CollectionUtils.isEmpty(masterOrderCategoryRate)) {
            return new ArrayList<>();
        }
        return masterOrderCategoryRate.stream().map(rate -> {
            GetMasterOrderCategoryRateResp getMasterOrderCategoryRateResp = new GetMasterOrderCategoryRateResp();
            BeanUtils.copyProperties(rate, getMasterOrderCategoryRateResp);
            return getMasterOrderCategoryRateResp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Long> getWaitOfferMasterIdsByOrderId(GetWaitOfferMasterIdsByOrderIdRqt rqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getWaitOfferMasterIdsByOrderId",
                JSONUtil.toJsonStr(rqt));
        return orderPushResourceGateway.getWaitOfferMasterIdsByOrderId(provinceNextId, rqt);
    }

    @Override
    public List<Long> getMasterCategorySelector(GetMasterCategorySelectorRqt rqt) {
        if (categorySelector == null || categorySelector == 0) {
            return Collections.emptyList();
        }
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getMasterCategorySelector",
                JSONUtil.toJsonStr(rqt));
        return orderPushResourceGateway.selectMasterCategorySelector(provinceNextId, rqt.getMasterId(), DateUtil.date());
    }

    @Override
    public GetUnreadResp getUnread(GetUnreadRqt getUnreadRqt) {
        OrderBaseSampleComposite orderBaseSampleComposite = commonOrderOfferService.orderSampleDetailByOrderId(getUnreadRqt.getOrderId());
        if (Objects.isNull(orderBaseSampleComposite)
                || Objects.isNull(orderBaseSampleComposite.getOrderBase())
                || Objects.isNull(orderBaseSampleComposite.getOrderGrab())) {
            return null;
        }
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(getUnreadRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getUnread",
                JSONUtil.toJsonStr(getUnreadRqt));
        List<Long> masterIdList = orderPushResourceGateway.getUnread(provinceNextId, orderBaseSampleComposite.getOrderBase(), orderBaseSampleComposite.getOrderGrab());
        if (CollectionUtils.isEmpty(masterIdList)) {
            return null;
        }

        OrderBaseDTO orderBaseDTO = new OrderBaseDTO();
        BeanUtils.copyProperties(orderBaseSampleComposite.getOrderBase(), orderBaseDTO);
        OrderGrabDTO orderGrabDTO = new OrderGrabDTO();
        BeanUtils.copyProperties(orderBaseSampleComposite.getOrderGrab(), orderGrabDTO);
        GetUnreadResp getUnreadResp = new GetUnreadResp();
        getUnreadResp.setOrderBaseDTO(orderBaseDTO);
        getUnreadResp.setOrderGrabDTO(orderGrabDTO);
        getUnreadResp.setMasterIds(masterIdList);
        return getUnreadResp;
    }

    @Override
    public List<GetMasterViewNumberResp> batchGetMasterViewNumber(BatchGetMasterViewNumberRqt rqt) {
        return new ArrayList<>();
    }

    @Override
    public List<GetMasterViewNumberResp> batchGetMasterViewNumberV2(BatchGetMasterViewNumberV2Rqt rqt) {
        if (CollectionUtil.isEmpty(rqt.getOrderInfos())) {
            return Collections.emptyList();
        }
        List<Long> globalOrderTraceIds = rqt.getOrderInfos().stream().map(BatchGetMasterViewNumberV2Rqt.OrderInfo::getGlobalOrderTraceId).collect(Collectors.toList());
        List<OrderBase> orderBaseBatch = commonOrderOfferService.getOrderBaseBatch(null, null, globalOrderTraceIds);
        if (CollectionUtils.isEmpty(orderBaseBatch)) {
            return new ArrayList<>();
        }
        List<GetMasterViewNumberResp> respList = Lists.newArrayList();
        Map<Long, OrderBase> orderBaseMap = orderBaseBatch.stream().collect(Collectors.toMap(OrderBase::getGlobalOrderTraceId, Function.identity()));
        rqt.getOrderInfos().forEach(orderInfo -> {
            BatchGetMasterViewNumberV2RqtBo bo = BeanUtil.toBean(orderInfo, BatchGetMasterViewNumberV2RqtBo.class);
            //计算省下级地址id
            List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(orderInfo.getProvinceNextId(),
                    "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.batchGetMasterViewNumberV2",
                    JSONUtil.toJsonStr(rqt));
            bo.setProvinceNextId(provinceNextId);
            if (apolloSwitchUtils.isOuterCitySwitch(provinceNextId.get(0))) {
                return;
            }
            respList.add(orderPushResourceGateway.batchGetMasterViewNumberV2(bo, orderBaseMap.get(orderInfo.getGlobalOrderTraceId())));
        });
        return respList;
    }

    @Override
    public WaitOfferCountResp analyticWaitOfferCount(WaitOfferCountReq analyticWaitOfferCountReq) {
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(analyticWaitOfferCountReq.getProvinceNextId(), "analyticWaitOfferCount", JSONUtil.toJsonStr(analyticWaitOfferCountReq));
        log.info("analyticWaitOfferCount params >>> {}", JSONUtil.toJsonStr(analyticWaitOfferCountReq));
        Set<Integer> defaultModeList = Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7, 8, 10).stream().collect(Collectors.toSet());
        Set<Integer> statisticsMode = analyticWaitOfferCountReq.getStatisticsMode();

        if (CollectionUtils.isNotEmpty(statisticsMode)) {
            Set<Integer> finalDefaultModeList = defaultModeList;
            List<Integer> definitionModeList = statisticsMode.stream().filter(it -> !finalDefaultModeList.contains(it)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(definitionModeList)) {
                throw new BusException(PushBusinessCode.REQ_VALIDATION_ERROR.code, String.format("未被定义的统计模式 :{%s}", definitionModeList));
            }
            defaultModeList = statisticsMode;
        }

        if (StringUtils.isNotEmpty(waitOfferCountSwitch) && waitOfferCountSwitch.equals("on")) {
            List<WaitOfferCountResp.CountResp> collect = defaultModeList.stream().map(
                    it -> new WaitOfferCountResp.CountResp(it, 0)
            ).collect(Collectors.toList());
            WaitOfferCountResp analyticWaitOfferCountResp = new WaitOfferCountResp();
            analyticWaitOfferCountResp.setCountRespList(collect);
            return analyticWaitOfferCountResp;
        }

        String jsonString = JSON.toJSONString(analyticWaitOfferCountReq);
        Date date = new Date();
        List<WaitOfferCountGateway> analyticWaitOfferCountGateways =
                defaultModeList.stream().map(it -> {
                    WaitOfferCountGateway analyticWaitOfferCountGateway = null;
                    switch (it) {
                        case 0:
                            //推荐
                            analyticWaitOfferCountGateway = JSON.parseObject(jsonString, WaitOfferCountGateway.class);
                            analyticWaitOfferCountGateway.setPushFlag(0);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            analyticWaitOfferCountGateway.setMold(it);

                            break;
                        case 1:
                            //附近单
                            analyticWaitOfferCountGateway = JSON.parseObject(jsonString, WaitOfferCountGateway.class);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            analyticWaitOfferCountGateway.setPushFlag(1);
                            analyticWaitOfferCountGateway.setMold(it);
                            break;
                        case 4:
                            //附近红包单
                            analyticWaitOfferCountGateway = JSON.parseObject(jsonString, WaitOfferCountGateway.class);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            analyticWaitOfferCountGateway.setPushFlag(4);
                            analyticWaitOfferCountGateway.setMold(it);
                            break;
                        case 5:
                            //附近更多
                            analyticWaitOfferCountGateway = JSON.parseObject(jsonString, WaitOfferCountGateway.class);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            analyticWaitOfferCountGateway.setPushFlag(5);
                            analyticWaitOfferCountGateway.setMold(it);
                            break;
                        case 2:
                            //合作专区
                            analyticWaitOfferCountGateway = JSON.parseObject(jsonString, WaitOfferCountGateway.class);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            analyticWaitOfferCountGateway.setMenuCategory(1);
                            analyticWaitOfferCountGateway.setPushFlag(0);
                            analyticWaitOfferCountGateway.setMold(it);
                            break;
                        case 6:
                            //悬赏专区
                            analyticWaitOfferCountGateway = JSON.parseObject(jsonString, WaitOfferCountGateway.class);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            analyticWaitOfferCountGateway.setMenuCategory(2);
                            analyticWaitOfferCountGateway.setPushFlag(0);
                            analyticWaitOfferCountGateway.setMold(it);
                            break;
                        case 7:
                            //必接专区
                            analyticWaitOfferCountGateway = JSON.parseObject(jsonString, WaitOfferCountGateway.class);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            analyticWaitOfferCountGateway.setMustOrderFlag(1);
                            analyticWaitOfferCountGateway.setPushFlag(0);
                            analyticWaitOfferCountGateway.setMold(it);
                            break;

                        case 8:
                            //金牌维修师傅专区
                            analyticWaitOfferCountGateway = JSON.parseObject(jsonString, WaitOfferCountGateway.class);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            analyticWaitOfferCountGateway.setMenuCategory(5);
                            analyticWaitOfferCountGateway.setPushFlag(0);
                            analyticWaitOfferCountGateway.setMold(it);
                            break;

                        case 10:
                            //专属好单
                            analyticWaitOfferCountGateway = JSON.parseObject(jsonString, WaitOfferCountGateway.class);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            analyticWaitOfferCountGateway.setExclusiveGoodOrderFlag(1);
                            analyticWaitOfferCountGateway.setMold(it);
                            break;

                        case 3:
                            //我的菜单
                            com.wanshifu.order.offer.domains.api.request.offer.WaitOfferCountReq copyAnalyticWaitOfferCountReq = JSON.parseObject(jsonString, com.wanshifu.order.offer.domains.api.request.offer.WaitOfferCountReq.class);
                            //it 对象不能随便使用，会存在并发问题
                            List<Long> divisionId = copyAnalyticWaitOfferCountReq.getDivisionId();
                            List<Integer> appointType = copyAnalyticWaitOfferCountReq.getAppointType();
                            List<Integer> categoryId = copyAnalyticWaitOfferCountReq.getCategoryId();
                            List<Integer> isArrived = copyAnalyticWaitOfferCountReq.getIsArrived();
                            List<Integer> orderAccountLabel = copyAnalyticWaitOfferCountReq.getOrderAccountLabel();
                            List<Integer> techniqueTypeId = copyAnalyticWaitOfferCountReq.getTechniqueTypeId();
                            com.wanshifu.order.offer.domains.api.request.offer.WaitOfferCountReq.CustomizeMenusCondition customizeMenusCondition = copyAnalyticWaitOfferCountReq.getCustomizeMenusCondition();
                            Integer pushFlag = null;
                            if (Objects.nonNull(customizeMenusCondition)) {
                                //处理自定义菜单情况
                                divisionId = conditionTransform(divisionId, customizeMenusCondition.getDivisionIds());
                                categoryId = conditionTransform(categoryId, customizeMenusCondition.getCategoryIds());
                                isArrived = conditionTransform(isArrived, customizeMenusCondition.getGoodsIsArrived());
                                orderAccountLabel = conditionTransform(orderAccountLabel, customizeMenusCondition.getOrderAccountLabel());
                                techniqueTypeId = conditionTransform(techniqueTypeId, customizeMenusCondition.getTechniqueTypeIds());
                                appointType = conditionTransform(appointType, customizeMenusCondition.getAppointTypes());
                                pushFlag = customizeMenusCondition.getPushFlag();
                                copyAnalyticWaitOfferCountReq.setDivisionId(divisionId);
                                copyAnalyticWaitOfferCountReq.setAppointType(appointType);
                                copyAnalyticWaitOfferCountReq.setCategoryId(categoryId);
                                copyAnalyticWaitOfferCountReq.setIsArrived(isArrived);
                                copyAnalyticWaitOfferCountReq.setOrderAccountLabel(orderAccountLabel);
                                copyAnalyticWaitOfferCountReq.setTechniqueTypeId(techniqueTypeId);
                            }
                            analyticWaitOfferCountGateway = BeanEnhanceUtil.copyBean(copyAnalyticWaitOfferCountReq, WaitOfferCountGateway::new);
                            analyticWaitOfferCountGateway.setCurrentDateTime(date);
                            //默认全部包含（推荐和附近更多）
                            analyticWaitOfferCountGateway.setPushFlag(pushFlag);
                            analyticWaitOfferCountGateway.setMold(it);
                            break;
                        default:
                            throw new IllegalStateException("Unexpected value: " + it);
                    }

                    return analyticWaitOfferCountGateway;
                }).collect(Collectors.toList());
        List<CompletableFuture<WaitOfferCountResp.CountResp>> completableFutureList = new ArrayList<>();

        for (WaitOfferCountGateway waitOfferCountGateway : analyticWaitOfferCountGateways) {
            CompletableFuture<WaitOfferCountResp.CountResp> exceptionally = CompletableFuture.supplyAsync(() -> {
                        //转换gateway对象，查询数据库
                        int count;
                        if (apolloSwitchUtils.isAnalyticWaitOfferCountSharing(provinceNextIds.get(0))) {

                            waitOfferCountGateway.setProvinceNextIdList(provinceNextIds);
                            waitOfferCountGateway.setTableName("ltb_order_push");

                            List<String> displayLongTailLv1ServeIdsList = ApolloSwitchUtils.getDisplayLongTailLv1ServeIds();
                            if (Objects.nonNull(waitOfferCountGateway.getPushFlag())
                                    && waitOfferCountGateway.getPushFlag() == 0
                                    && Objects.nonNull(waitOfferCountGateway.getMasterSourceType())
                                    && "tob".equals(waitOfferCountGateway.getMasterSourceType())
                                    && CollectionUtil.isNotEmpty(displayLongTailLv1ServeIdsList)) {
                                log.info("待接单默认列表计算角标包含>>技能不相关订单>>>>>masterId:{},pushFlag:{},masterSourceType:{},displayLongTailLv1ServeIdsList:{}",
                                        waitOfferCountGateway.getMasterId(), waitOfferCountGateway.getPushFlag(), waitOfferCountGateway.getMasterSourceType(), JSONUtil.toJsonStr(displayLongTailLv1ServeIdsList));
                                //待接单默认列表展示技能不相关订单
                                waitOfferCountGateway.setDisplayLongTailOrderLevel1ServeIds(displayLongTailLv1ServeIdsList.stream().map(Long::valueOf).collect(Collectors.toList()));
                            }

                            count = orderPushResourceGateway.analyticWaitOfferCountV2(waitOfferCountGateway);
                        } else {
                            count = orderPushResourceGateway.analyticWaitOfferCount(waitOfferCountGateway);
                        }
                        return new WaitOfferCountResp.CountResp(waitOfferCountGateway.getMold(), count);
                    },
                    analyticWaitOfferCountExecutor

            ).exceptionally(error -> {
                log.error("异步查询线程异常:{}", error);
                return new WaitOfferCountResp.CountResp(waitOfferCountGateway.getMold(), 0);
            });
            completableFutureList.add(exceptionally);
        }
        //合并异步线程
        List<WaitOfferCountResp.CountResp> countResps = completableFutureList.stream().map(
                it -> {
                    try {
                        //1000毫秒，未返回结果。kill线程
                        return it.get(2000, TimeUnit.MILLISECONDS);
                    } catch (Exception e) {
                        log.error("合并执行线程异常:{}", e);
                        return null;
                    }

                }
        ).filter(Objects::nonNull).collect(Collectors.toList());
        WaitOfferCountResp analyticWaitOfferCountResp = new WaitOfferCountResp();
        analyticWaitOfferCountResp.setCountRespList(countResps);
        return analyticWaitOfferCountResp;
    }


    @Override
    public OrderPushCountResp orderPushCount(OrderPushCountRqt orderPushCountRqt) {

        log.info("orderPushCount params >>> {}", JSONUtil.toJsonStr(orderPushCountRqt));
        Set<Integer> defaultModeList = Arrays.asList(0, 1).stream().collect(Collectors.toSet());
        Set<Integer> statisticsMode = orderPushCountRqt.getStatisticsMode();

        if (CollectionUtils.isNotEmpty(statisticsMode)) {
            Set<Integer> finalDefaultModeList = defaultModeList;
            List<Integer> definitionModeList = statisticsMode.stream().filter(it -> !finalDefaultModeList.contains(it)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(definitionModeList)) {
                throw new BusException(PushBusinessCode.REQ_VALIDATION_ERROR.code, String.format("未被定义的统计模式 :{%s}", definitionModeList));
            }
            defaultModeList = statisticsMode;
        }

        if (StringUtils.isNotEmpty(orderPushCountDegradeSwitch) && orderPushCountDegradeSwitch.equals("on")) {
            List<OrderPushCountResp.CountResp> collect = defaultModeList.stream().map(
                    it -> new OrderPushCountResp.CountResp(it, 0)
            ).collect(Collectors.toList());
            OrderPushCountResp orderPushCountResp = new OrderPushCountResp();
            orderPushCountResp.setCountRespList(collect);
            return orderPushCountResp;
        }


        Long globalOrderTraceId = orderPushCountRqt.getGlobalOrderTraceId();
        OrderBase orderBase = commonOrderOfferService.getOrderBase(null, globalOrderTraceId);

        if (Objects.isNull(orderBase)) {
            List<OrderPushCountResp.CountResp> collect = defaultModeList.stream().map(
                    it -> new OrderPushCountResp.CountResp(it, 0)
            ).collect(Collectors.toList());
            OrderPushCountResp orderPushCountResp = new OrderPushCountResp();
            orderPushCountResp.setCountRespList(collect);
            return orderPushCountResp;
        }

        Long orderId = orderBase.getOrderId();

        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(orderBase.getThirdDivisionId(), "orderPushCount", JSONUtil.toJsonStr(orderPushCountRqt));

        List<OrderPushCountResp.CountResp> countRespList =
                defaultModeList.stream().map(it -> {
                    Integer count = orderPushResourceGateway.orderPushCount(provinceNextIds, orderId, it);
                    OrderPushCountResp.CountResp countResp = new OrderPushCountResp.CountResp(it, count);
                    return countResp;
                }).collect(Collectors.toList());


        OrderPushCountResp orderPushCountResp = new OrderPushCountResp();
        orderPushCountResp.setCountRespList(countRespList);
        return orderPushCountResp;
    }


    public static <T extends List> T conditionTransform(T t1, T t2) {
        Boolean isInputCondition = CollectionUtils.isNotEmpty(t1) ? true : false;
        Boolean isCustomizeMenusCondition = CollectionUtils.isNotEmpty(t2) ? true : false;
        if (isInputCondition && !isCustomizeMenusCondition) {
            return t1;
        }
        if (!isInputCondition && isCustomizeMenusCondition) {
            return t2;
        }

        if (isInputCondition && isCustomizeMenusCondition) {
            //取交集
            t1.retainAll(t2);
            return CollectionUtils.isNotEmpty(t1) ? t1 : null;
        }

        return null;
    }

    @Override
    public GetTodayUserHireOrderResp getTodayUserHireOrder(GetTodayUserHireOrderRqt getTodayUserHireOrderRqt) {
        //获取省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(getTodayUserHireOrderRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getTodayUserHireOrder", JSONUtil.toJsonStr(getTodayUserHireOrderRqt));
        return orderPushResourceGateway.getTodayUserHireOrder(provinceNextId, getTodayUserHireOrderRqt);
    }

    @Override
    public List<OrderPushDTO> batchGetOrderPush(BatchOrderPushRqt batchOrderPushRqt) {
        Long orderId = batchOrderPushRqt.getOrderId();
        List<Long> masterId = batchOrderPushRqt.getMasterId();
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(batchOrderPushRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.batchGetOrderPush", JSONUtil.toJsonStr(batchOrderPushRqt));
        List<OrderPush> orderPushes = orderPushManagerGateway.queryOrderToMasterIdsPushInfo(provinceNextId, orderId, masterId);
        if (CollUtil.isNotEmpty(orderPushes)) {
            return DataUtils.copyList(OrderPushDTO.class, orderPushes);
        }
        return new ArrayList<>();
    }

    @Override
    public List<OrderPushDTO> getOrderPushByOrderId(Long provinceNextId, Long orderId) {
        //计算省下级地址id
        List<Long> calcProvinceNextId = commonAddressService.getProvinceNextIdV2(provinceNextId,
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getOrderPushByOrderId",
                orderId.toString());
        List<OrderPush> orderPushes = orderPushResourceGateway.getOrderPushByOrderId(calcProvinceNextId, orderId);
        if (CollUtil.isNotEmpty(orderPushes)) {
            return DataUtils.copyList(OrderPushDTO.class, orderPushes);
        }
        return new ArrayList<>();
    }

    @Override
    public List<OrderPushDTO> getOrderPushNoOfferByOrderId(Long provinceNextId, Long orderId) {
        //计算省下级地址id
        List<Long> calcProvinceNextId = commonAddressService.getProvinceNextIdV2(provinceNextId,
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getOrderPushNoOfferByOrderId",
                orderId.toString());
        List<OrderPush> orderPushes = orderPushResourceGateway.getOrderPushNoOfferByOrderId(calcProvinceNextId, orderId);
        if (CollUtil.isNotEmpty(orderPushes)) {
            return DataUtils.copyList(OrderPushDTO.class, orderPushes);
        }
        return new ArrayList<>();
    }

    @Override
    public SimplePageInfo<OrderPushDTO> getOrderPushNoOfferByOrderIdV2(NoOfferByOrderIdRqt rqt) {
        //计算省下级地址id
        List<Long> calcProvinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getOrderPushNoOfferByOrderId",
                rqt.getOrderId().toString());
        rqt.setProvinceNextIds(calcProvinceNextId);
        SimplePageInfo<OrderPush> orderPushes = orderPushResourceGateway.getOrderPushNoOfferByOrderIdV2(rqt);
        if (Objects.isNull(orderPushes) || CollectionUtils.isEmpty(orderPushes.getList())) {
            return new SimplePageInfo<>();
        }
        List<OrderPush> orderPushList = orderPushes.getList();
        List<OrderPushDTO> orderPushDTOList = orderPushList.stream().map(orderPush -> DataUtils.copyObject(orderPush, OrderPushDTO.class)).collect(Collectors.toList());

        SimplePageInfo<OrderPushDTO> respSimplePageInfo = new SimplePageInfo<>(orderPushDTOList);
        respSimplePageInfo.setPageNum(orderPushes.getPageNum());
        respSimplePageInfo.setPages(orderPushes.getPages());
        respSimplePageInfo.setPageSize(orderPushes.getPageSize());
        respSimplePageInfo.setTotal(orderPushes.getTotal());
        return respSimplePageInfo;
    }

    @Override
    public List<OrderPushDTO> batchGetOrderPushByOrderIdsAndMasterId(BatchGetOrderPushRqt batchGetOrderPushRqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(batchGetOrderPushRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.batchGetOrderPushByOrderIdsAndMasterId",
                JSONUtil.toJsonStr(batchGetOrderPushRqt));
        List<OrderPush> orderPushList = orderPushResourceGateway.batchGetOrderPushByOrderIdsAndMasterId(provinceNextId, batchGetOrderPushRqt.getOrderIds(), batchGetOrderPushRqt.getMasterId());
        if (CollectionUtils.isEmpty(orderPushList)) {
            return new ArrayList<>();
        }
        return orderPushList.stream().map(orderPush -> DataUtils.copyObject(orderPush, OrderPushDTO.class)).collect(Collectors.toList());
    }

    @Override
    public OrderPushDTO getOrderPush(GetOrderPushRqt getOrderPushRqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(getOrderPushRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getOrderPush",
                JSONUtil.toJsonStr(getOrderPushRqt));
        OrderPush orderPush = orderPushResourceGateway.getOrderPush(provinceNextId, getOrderPushRqt.getOrderId(), getOrderPushRqt.getMasterId());
        if (Objects.isNull(orderPush)) {
            return null;
        }
        return DataUtils.copyObject(orderPush, OrderPushDTO.class);
    }

    @Override
    public Integer getOrderShowNumOfPeople(GetOrderShowRqt getOrderShowRqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(getOrderShowRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getOrderShowNumOfPeople",
                JSONUtil.toJsonStr(getOrderShowRqt));
        return orderPushResourceGateway.getOrderShowNumOfPeople(provinceNextId, getOrderShowRqt.getOrderId());
    }


    @Resource
    private InterfereOrderPushListService interfereOrderPushListService;

    @Override
    public List<GetInterfereOrderPushListResp> getInterfereOrderPushList(GetInterfereOrderPushListRqt rqt) {

        SimpleOrderGrab simpleOrderGrab = commonOrderOfferService.getSimpleOrderGrabByGlobalId(rqt.getGlobalOrderTraceId());

        if (Objects.isNull(simpleOrderGrab) || Objects.isNull(simpleOrderGrab.getOrderBase())) {
            return null;
        }

        Long orderId = simpleOrderGrab.getOrderBase().getOrderId();
        Long thirdDivisonId = simpleOrderGrab.getOrderBase().getThirdDivisionId();

        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(thirdDivisonId,
                "com.wanshifu.domain.push.serviceimpl.OrderPushResourceServiceImpl.getInterfereOrderPushList",
                JSONUtil.toJsonStr(rqt));
        List<InterfereOrderPush> orderPushList = interfereOrderPushListService.getInterfereOrderPushList(rqt);

        if (CollectionUtils.isEmpty(orderPushList)) {
            return null;
        }

        return orderPushList.stream()
                .map(respBo -> {
                    GetInterfereOrderPushListResp resp = BeanUtil.toBean(respBo, GetInterfereOrderPushListResp.class);
                    resp.setGlobalOrderTraceId(rqt.getGlobalOrderTraceId());
                    resp.setScore(respBo.getScore());
                    return resp;
                })
                .collect(Collectors.toList());
    }
}
