package com.wanshifu.domain.push.gateway;

import com.wanshifu.domain.push.model.*;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.master.order.domains.api.request.innovateBusiness.InnovateBusinessPushRqt;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.push.request.push.*;
import com.wanshifu.order.push.response.push.PushedToMasterResp;
import com.wanshifu.order.push.response.push.BatchUpdatePushDistanceResp;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/29 14:45
 */
public interface OrderPushOperateGateway {
    /**
     * 推单给师傅
     *
     * @param pushOrderRqt
     * @return
     */
    public Set<Long> pushedToMaster(List<Long> provinceNextId, PushedToMasterGatewayRqt pushOrderRqt);

    /**
     * 记录样板城市订单推单日志
     * @param orderPushRqt
     */
    void recordTmplCityOrderPushLog(OrderPushRqt orderPushRqt);

    /**
     * 信息订单推送
     *
     * @param convertInnovateRqt
     * @return
     */
    InnovateBusinessPushResp pushedInnovateToMaster(List<Long> provinceNextId, InnovateBusinessPushGatewayRqt convertInnovateRqt);


    void resendOrderPush(ResendOrderPushRqt resendOrderPushRqt, Boolean isChangeDivision, Boolean isChangeTechnology,
                         Long masterId, String removeTechnologyIds, List<Long> removeServeThirdDivisionIdList,
                         List<Long> removeServeFourthDivisionIdList, List<OrderPush> masterNormalList,
                         Map<Long, OrderBase> orderBaseMap);

    /**
     * 清理原表过期的推单记录
     * @param clearExpiredPushRqt clearExpiredPushRqt
     */
    Integer clearExpiredOrderPush(ClearExpiredPushRqt clearExpiredPushRqt);

    /**
     * 清理分表过期的推单记录
     * @param clearExpiredPushRqt clearExpiredPushRqt
     */
    void clearExpiredOrderPushV2(ClearExpiredPushRqt clearExpiredPushRqt);

    /**
     * 批量更新师傅推单距离和经纬度
     * @param batchUpdatePushDistanceRqt
     * @return
     */
    @Deprecated
    BatchUpdatePushDistanceResp batchUpdatePushDistance(BatchUpdatePushDistanceRqt batchUpdatePushDistanceRqt);

    /**
     * 批量更新师傅推单距离和经纬度
     * 待报价列表数据架构升级项目V2版本，去除根据主键pushId操作逻辑，增加省下级地址id参数传递
     * @param batchUpdatePushDistanceV2Rqt
     * @return
     */
    BatchUpdatePushDistanceResp batchUpdatePushDistanceV2(BatchUpdatePushDistanceV2Rqt batchUpdatePushDistanceV2Rqt);

    /**
     * 手动推单
     * @param orderId
     * @param masterId
     * @return
     */
    @Deprecated
    int manualClearPush(Long orderId,Long masterId);

    /**
     * 修改推单记录菜单类别
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param masterId 师傅id
     * @param menuCategory 菜单类别
     */
    int updateOrderPushMenuCategory(List<Long> provinceNextId, Long orderId, Long masterId, Integer menuCategory);

    /**
     * 修改推单记录菜单类别
     * @param provinceNextId
     * @param orderId
     * @param masterIds
     * @param menuCategory
     * @return
     */
    int updateOrderPushMenuCategory(List<Long> provinceNextId, Long orderId, List<Long> masterIds, Integer menuCategory);

    /**
     * 更新推送竞争少订单
     * @param rqt
     **/
    PushLessContendOrderResp pushLessContendOrder(PushLessContendOrderRqt rqt);

    /**
     * 清理订单推单记录
     * @param orderPushClear
     */
    void orderPushClear(OrderPushClear orderPushClear);

    /**
     * 更新orderPush的offerTime
     *
     * @param updateOrderPushOfferTimeRqt
     * @return
     */
    Integer updateOrderPushOfferTime(List<Long> provinceNextId, UpdateOrderPushOfferTimeRqt updateOrderPushOfferTimeRqt);

    /**
     * 更新orderPush的firstViewTime
     *
     * @param updateFirstViewTimeRqt
     * @return
     */
    Integer updateOrderPushFirstViewTime(List<Long> provinceNextId, UpdateFirstViewTimeRqt updateFirstViewTimeRqt);

    /**
     * 更新推单拉取距离状态
     *
     * @param updatePullOrderDistanceRqt
     * @return
     */
    Integer updatePullOrderDistance(List<Long> provinceNextId, UpdatePullOrderDistanceRqtV2 updatePullOrderDistanceRqt);

    /**
     * 更新物流到货状态--用于排序
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @param isArrived 是否到货
     * @return
     */
    Integer updateIsArrivedByOrderId(List<Long> provinceNextId, Long orderId, Integer isArrived);

    /**
     * 批量更新menuCategory，由0改为2，也就是移入悬赏分区
     * @param provinceNextId
     * @param orderId
     * @param menuCategory
     */
    void updateMenuCategoryBatch(List<Long> provinceNextId, Long orderId, int menuCategory);


    /**
     * 批量更新menuCategory,由2改为0，也就是移出悬赏分区
     * @param provinceNextId
     * @param orderId
     */
    void updateMenuCategoryBatchV2(List<Long> provinceNextId, Long orderId);

    /**
     * 批量更新offer
     * @param provinceNextId
     * @param orderId
     */
    void updateOfferBatch(List<Long> provinceNextId, Long orderId);


    /**
     * 更新orderPush的firstViewTime
     *
     * @param updatePullViewTimeRqt
     * @return
     */
    Integer updatePullViewTime(List<Long> provinceNextId, UpdatePullViewTimeRqt updatePullViewTimeRqt);

    /**
     * 记录普通推单首次推送师傅数量
     * @param orderPushRqt
     */
    void recordFirstPushMatchMaster(OrderPushRqt orderPushRqt);

    /**
     * 代理商推单后首次查看逻辑
     *
     * @param provinceNextId
     * @param orderId
     * @param masterId
     */
    void agentPushFirstView(List<Long> provinceNextId, Long orderId, Long masterId);
}
