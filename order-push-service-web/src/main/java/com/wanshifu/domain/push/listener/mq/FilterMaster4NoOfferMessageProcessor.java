package com.wanshifu.domain.push.listener.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.handler.AbstractMatchMasterTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.push.listener.mq.message.FilterMaster4NoOfferMessage;
import com.wanshifu.infrastructure.NoOfferFilterMaster;
import com.wanshifu.infrastructure.repository.NoOfferFilterMasterRepository;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 9:20
 */
@ConsumeTag(value = ConsumeTagEnum.FILTER_MASTER_FOR_NO_OFFER)
@Slf4j
public class FilterMaster4NoOfferMessageProcessor extends AbstractMatchMasterTopicMessageTag<FilterMaster4NoOfferMessage> {

    @Resource
    private NoOfferFilterMasterRepository noOfferFilterMasterRepository;

    @Override
    public void postHandler(FilterMaster4NoOfferMessage filterMaster4NoOfferMessage) {
        log.info("FilterMaster4NoOfferMessage:{}", JSONUtil.toJsonStr(filterMaster4NoOfferMessage));
        if (Objects.isNull(filterMaster4NoOfferMessage)
                || Objects.isNull(filterMaster4NoOfferMessage.getOrderId())
                || Objects.isNull(filterMaster4NoOfferMessage.getGlobalOrderId())
                || CollectionUtil.isEmpty(filterMaster4NoOfferMessage.getMasterList())) {
            return;
        }

        //先根据orderId删除，再新增
        noOfferFilterMasterRepository.deleteByOrderIdAndPutTagFlag(filterMaster4NoOfferMessage.getOrderId(), 0);

        List<NoOfferFilterMaster> hasPutTags = noOfferFilterMasterRepository.selectByOrderIdAndPutFlagTag(filterMaster4NoOfferMessage.getOrderId(), 1);

        log.info("FilterMaster4NoOfferMessage >>> hasPutTags:{}", JSONUtil.toJsonStr(hasPutTags));

        List<NoOfferFilterMaster> noOfferFilterMasterList = Lists.newArrayList();
        Date now = new Date();
        for (FilterMaster4NoOfferMessage.OrderPushMaster orderPushMaster : filterMaster4NoOfferMessage.getMasterList()) {
            if (CollectionUtil.isNotEmpty(hasPutTags)) {
                List<Long> hasPutTagMasterIds = hasPutTags.stream().map(NoOfferFilterMaster::getMasterId).collect(Collectors.toList());
                if (hasPutTagMasterIds.contains(orderPushMaster.getMasterId())) {
                    continue;
                }
            }
            NoOfferFilterMaster noOfferFilterMaster = new NoOfferFilterMaster();
            noOfferFilterMaster.setOrderId(filterMaster4NoOfferMessage.getOrderId());
            noOfferFilterMaster.setGlobalOrderId(filterMaster4NoOfferMessage.getGlobalOrderId());
            noOfferFilterMaster.setMasterId(orderPushMaster.getMasterId());
            noOfferFilterMaster.setScore(Objects.isNull(orderPushMaster.getScore()) ? BigDecimal.ZERO : orderPushMaster.getScore());
            noOfferFilterMaster.setPutTagFlag(0);
            noOfferFilterMaster.setCreateTime(now);
            noOfferFilterMaster.setUpdateTime(now);
            noOfferFilterMasterList.add(noOfferFilterMaster);
        }
        noOfferFilterMasterRepository.insertNoOfferFilterMasterList(noOfferFilterMasterList);
    }
}
