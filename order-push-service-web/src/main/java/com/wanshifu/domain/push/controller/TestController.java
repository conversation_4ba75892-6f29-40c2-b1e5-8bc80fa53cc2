package com.wanshifu.domain.push.controller;

import com.wanshifu.domain.push.controller.test.UpdateOfferBatchReq;
import com.wanshifu.domain.push.gateway.OrderPushListGateway;
import com.wanshifu.domain.push.gateway.OrderPushOperateGateway;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.infrastructure.OrderPush;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/7 15:59
 */
@RestController
@RequestMapping("/orderPush/test")
public class TestController {

    @Resource
    private CommonAddressService commonAddressService;

    @Resource
    private OrderPushOperateGateway orderPushOperateGateway;

    @Resource
    private OrderPushListGateway orderPushListGateway;

    @PostMapping("/updateOfferBatch")
    public void updateOfferBatch(@RequestBody UpdateOfferBatchReq updateOfferBatchReq) {
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(updateOfferBatchReq.getProvinceNextId(), null, null);
        orderPushOperateGateway.updateOfferBatch(provinceNextIds, updateOfferBatchReq.getOrderId());
    }

    @PostMapping("/updateOfferBatchV2")
    public void updateOfferBatchV2(@RequestBody UpdateOfferBatchReq updateOfferBatchReq) {
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(updateOfferBatchReq.getProvinceNextId(), null, null);

        //首次报价，更新  订单是否有报价  标记
        OrderPush orderPush = orderPushListGateway.getOrderPushByOrderIdAndOffer(provinceNextIds, updateOfferBatchReq.getOrderId(), 1);
        if (Objects.isNull(orderPush)) {
            //首次报价更新
            orderPushOperateGateway.updateOfferBatch(provinceNextIds, updateOfferBatchReq.getOrderId());
        }
    }

}
