package com.wanshifu.domain.push.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2023-10-19 18:14
 * @Description
 * @Version v1
 **/
@Data
public class ExclusiveOrderTransferMessage {

    /**
     * 订单全局ID
     */
    @NotNull
    private Long globalOrderTraceId;

    /**
     * 下单人账号ID
     */
    @NotNull
    private Long accountId;

    /**
     * 下单人账号类型
     */
    @NotEmpty
    private String accountType;

    /**
     * 专属转单来源
     *  brand_order_to_general:   品牌转普通、
     *  exclusive_order_to_general: 专属转普通
     *  urgent_order_to_general :  加急转普通
     */
    @NotEmpty
    private String exclusiveOrderTransferSource;

    @NotEmpty
    private String note;

    private String emergencyOrderStatus = "cancel";

    /**
     * 切换开关标签 new-新 old-旧
     */
    private String handoffTag;

    /**
     * 是否更新标签
     */
    private Boolean isUpdateExclusiveFlag;

    private Integer exclusiveFlag;
}
