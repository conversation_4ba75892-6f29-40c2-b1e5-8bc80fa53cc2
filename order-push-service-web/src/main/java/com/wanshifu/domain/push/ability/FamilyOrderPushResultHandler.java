package com.wanshifu.domain.push.ability;

import com.wanshifu.domain.push.model.OrderPushRqt;
import com.wanshifu.domain.push.model.enums.BusinessLineIdEnum;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023-09-22 09:56
 * @Description 家庭业务推单处理
 * @Version v1
 **/
@Service
public class FamilyOrderPushResultHandler extends OrderPushResultHandlerSupport {
    @Override
    public boolean matching(OrderPushRqt orderPushRqt) {
        return orderPushRqt.getBusinessLineId() == BusinessLineIdEnum.TWO.id;
    }

}
