package com.wanshifu.domain.push;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.order.push.domains.dto.OrderPushDTO;
import com.wanshifu.order.push.request.*;
import com.wanshifu.order.push.request.push.NoOfferByOrderIdRqt;
import com.wanshifu.order.push.response.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 14:27
 */
public interface OrderPushResourceService {
    List<BatchGetPushTimeResp> batchPushTime(BatchGetPushTimeRqt getPushTimeRqt);

    List<Long> getInvitePushMasterIds(GetInvitePushMasterIdsRqt rqt);

    List<OrderPushDTO> selectPushOrderByMasterId(SelectPushOrderByMasterIdRqt rqt);

    List<BatchOrderPushResp> batchOrderPush(BatchOrderPushRqt batchOrderPushRqt);

    public String getHandoffTag(Long orderId);

    /**
     *查询师傅待报价列表订单类目占比.
     * @param rqt
     * @return
     */
    List<GetMasterOrderCategoryRateResp> getMasterOrderCategoryRate(GetMasterOrderCategoryRateRqt rqt);

    /**
     * 分页查询已推单师傅
     * @param rqt
     * @return
     */
    List<Long> getWaitOfferMasterIdsByOrderId(GetWaitOfferMasterIdsByOrderIdRqt rqt);

    /**
     *  查询师傅待报价列表可筛选订单类目选择器
     * @param rqt
     * @return
     */
    List<Long> getMasterCategorySelector(GetMasterCategorySelectorRqt rqt);

    /**
     * 查询师傅推送后是否已读详情
     * @param getUnreadRqt
     * @return
     */
    GetUnreadResp getUnread(GetUnreadRqt getUnreadRqt);

    /**
     * 批量获取订单已查看师傅数量 (最多20条)
     * @param rqt
     * @return
     */
    List<GetMasterViewNumberResp> batchGetMasterViewNumber(BatchGetMasterViewNumberRqt rqt);

    /**
     * 批量获取订单已查看师傅数量 V2(最多20条)
     * @param rqt BatchGetMasterViewNumberV2Rqt
     * @return List<GetMasterViewNumberResp>
     */
    List<GetMasterViewNumberResp> batchGetMasterViewNumberV2(BatchGetMasterViewNumberV2Rqt rqt);

    /**
     * 统计待报价指标数量
     * @param analyticWaitOfferCountReq
     * @return
     */
    WaitOfferCountResp analyticWaitOfferCount (WaitOfferCountReq analyticWaitOfferCountReq);

    /**
     * 获取当天是否有用户直接雇佣的订单
     * @param getTodayUserHireOrderRqt
     * @return
     */
    GetTodayUserHireOrderResp getTodayUserHireOrder(GetTodayUserHireOrderRqt getTodayUserHireOrderRqt);

    List<OrderPushDTO> batchGetOrderPush(BatchOrderPushRqt batchOrderPushRqt);

    /**
     * 根据orderIds + masterId 批量查询订单推送信息
     * @param batchGetOrderPushRqt
     * @return
     */
    List<OrderPushDTO> batchGetOrderPushByOrderIdsAndMasterId(BatchGetOrderPushRqt batchGetOrderPushRqt);

    /**
     * 订单ID和师傅ID取订单推送信息
     * @param getOrderPushRqt
     * @return
     */
    OrderPushDTO getOrderPush(GetOrderPushRqt getOrderPushRqt);

    /**
     * 查询订单被师傅查看人数
     * @param getOrderShowRqt
     * @return
     */
    Integer getOrderShowNumOfPeople(GetOrderShowRqt getOrderShowRqt);

    /**
     * 根据orderId查询订单推送信息
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    List<OrderPushDTO> getOrderPushByOrderId(Long provinceNextId, Long orderId);

    /**
     * 根据orderId查询订单推送未报价信息
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    List<OrderPushDTO> getOrderPushNoOfferByOrderId(Long provinceNextId, Long orderId);

    /**
     * 根据orderId查询订单推送未报价信息 分页接口
     * @param rqt
     * @return
     */
    SimplePageInfo<OrderPushDTO> getOrderPushNoOfferByOrderIdV2(NoOfferByOrderIdRqt rqt);


    /**
     * 根据orderId查询平台干预师傅列表
     * @param rqt
     * @return
     */
    List<GetInterfereOrderPushListResp> getInterfereOrderPushList(GetInterfereOrderPushListRqt rqt);

    /**
     * 推单师傅统计
     * @param orderPushCountRqt
     * @return
     */
    OrderPushCountResp orderPushCount(OrderPushCountRqt orderPushCountRqt);


    }
