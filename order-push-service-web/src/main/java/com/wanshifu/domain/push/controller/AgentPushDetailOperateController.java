package com.wanshifu.domain.push.controller;

import com.wanshifu.domain.agent.AgentPushDetailOperateService;
import com.wanshifu.order.push.api.AgentPushDetailOperateApi;
import com.wanshifu.order.push.request.agent.UpdateAgentByDisinterestOrderRqt;
import com.wanshifu.order.push.request.agent.UpdateAgentByGrabRqt;
import com.wanshifu.order.push.request.agent.UpdateAgentByOfferPriceRqt;
import com.wanshifu.order.push.request.agent.ViewOrderRqt;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/11 14:52
 */
@RestController
@RequestMapping("/agentPush/operate")
public class AgentPushDetailOperateController implements AgentPushDetailOperateApi {

    @Resource
    private AgentPushDetailOperateService agentPushDetailOperateService;

    /**
     * 查看订单
     * @param viewOrderRqt
     * @return
     */
    @PostMapping("viewOrder")
    @Override
    public void viewOrder(@RequestBody @Valid ViewOrderRqt viewOrderRqt) {
        agentPushDetailOperateService.viewOrder(viewOrderRqt);
    }

    /**
     * 报价
     * @param updateAgentByOfferPriceRqt
     */
    @PostMapping("offerPrice")
    @Override
    public void offerPrice(@RequestBody @Valid UpdateAgentByOfferPriceRqt updateAgentByOfferPriceRqt) {
        agentPushDetailOperateService.offerPrice(updateAgentByOfferPriceRqt);
    }

    /**
     * 一口价抢单
     * @param updateAgentByGrabRqt
     */
    @PostMapping("grabOrder")
    @Override
    public void grabOrder(@RequestBody @Valid UpdateAgentByGrabRqt updateAgentByGrabRqt) {
        agentPushDetailOperateService.grabOrder(updateAgentByGrabRqt);
    }

    /**
     * 师傅不感兴趣订单
     * @param updateAgentByDisinterestOrderRqt
     */
    @PostMapping("disinterestOrder")
    @Override
    public void disinterestOrder(@RequestBody @Valid UpdateAgentByDisinterestOrderRqt updateAgentByDisinterestOrderRqt) {
        agentPushDetailOperateService.disinterestOrder(updateAgentByDisinterestOrderRqt);
    }
}
