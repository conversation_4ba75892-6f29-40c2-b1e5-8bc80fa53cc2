package com.wanshifu.domain.push.model;

import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.order.offer.domains.po.OrderLogisticsInfo;
import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderExtraDataDTO;
import com.wanshifu.order.push.domains.dto.OrderLogisticsInfoDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/6/24 10:28
 */
@Data
public class WaitOfferNoPageRespBo {
    /**
     * 订单物流信息
     */
    @Deprecated
    private OrderLogisticsInfo orderLogisticsInfo;

    /**
     * 订单额外信息
     */
    private OrderExtraData orderExtraData;

    /**
     * 订单基本信息
     */
    private OrderBase orderBase;

    /**
     * 订单首个图片aid
     */
    private Long orderFirstImageAid;

}
