package com.wanshifu.domain.push.model;

import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.po.InfoOrderBase;
import com.wanshifu.order.push.enums.PushScenarioType;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023-09-21 18:37
 * @Description
 * @Version v1
 **/
@Data
public class PushedResultNotices {

    /**
     * 业务线 1.成品  2. 家庭  3.创新（信息）
     *
     */
    private Integer businessLineId;

    /**
     * 订单全局id
     */
    private Long globalOrderTraceId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单信息
     */
    private OrderBaseComposite orderBaseComposite;

    /**
     * 信息订单
     */
    private InfoOrderBase infoOrderBase;

     /**
     * 推送的师傅
     */
    @NotEmpty
    private Set<Long> pushMasterIds;

    /**
     * 推送的师傅信息
     */
    @NotEmpty
    private List<MasterInfo>  masterInfoList;


    /**
     * 推单模式
     */
    private String pushMode;

    /**
     * 推单场景
     */
    @NotNull
    private PushScenarioType pushScenarioType;

    /**
     * 推送的地址级别（3：三级地址推单，4：四级地址推单）
     */
    private Integer pushDivisionLevel;
    /**
     * 是否首单推送
     */
    @NotNull
    private Integer firstPush;

    /**
     * 普通推单首次有效推单(首轮首次)
     * 0：非，1：是
     */
    private Integer firstTimeValidPush;

    /**
     * 专属招募id v7.3
     */
    private Long recruitId;

    /**
     * 是否有价格 v7.3
     */
    private boolean hasPrice;

    /**
     * 是否团队师傅推单
     */
    private Integer teamMasterOrderPush;

    /**
     * 订单包id
     */
    private Long packageId;

    /**
     * 订单包匹配到的子属性
     */
    private String goodsAttribute;

    /**
     * 订单包子属性对应输入值范围
     */
    private String orderPackageAttributeRange;

    /**
     * 推单标识 0：正常推单 1：附近推单
     */
    private Integer pushFlag;

    /**
     * 代理商推单师傅
     */
    private String agentMasterList;

    /**
     * 推单后无人接单重推时间配置
     */
    private Integer agentPushNoHiredRePushTime;

    /**
     * 推送模式-细分类型(专属 pre_exclusive_single(专属单个师傅),pre_exclusive_single_transfer_grab_offer_price(专属转抢单))
     */
    private  String pushModeType;


    private Integer exclusiveOrderFlag;
    /**
     * 专属标签名称
     */
    private String recruitTagName;

    /**
     * 师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    private String masterSourceType;


    /**
     * 匹配场景
     */
    private String matchSceneCode;


    @Data
    public static class MasterInfo{

        /**
         * 师傅ID
         */
        private Long masterId;
        /**
         * 招募id
         */
        private String recruitId;

        /**
         * 标签名称: contract：直约 ，brand:品牌
         */
        private String tagName;

        /**
         * 标签扩展信息
         */
        private String tagExpand;

        /**
         * 是否按照技能推单 1:是 0:否
         */
        private Integer accordingTechnologyPushFlag;

        /**
         * 自动报价金额
         */
        private BigDecimal autoPrice;


        /**
         * 自动接单顺序
         */
        private Integer autoOfferSort;


        /**
         * 分时师傅分流标记，0: 未分流，可正常报价/抢单，1：分流，不可报价/抢单
         */
        private Integer shuntFlag;
    }
}
