package com.wanshifu.domain.push.handler.clearorderpush.impl;

import com.google.common.collect.Lists;
import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.push.context.ClearPushStrategyContext;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.handler.clearorderpush.OrderPushClearService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.order.push.enums.OrderPushClearType;
import com.wanshifu.order.push.request.push.ClearOrderPushRqt;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/4 18:55
 */
@Component
public class OfferModifyOrderClearPushServiceImpl implements OrderPushClearService, InitializingBean {

    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int clear(ClearOrderPushRqt clearOrderPushRqt) {
        if(CollectionUtils.isEmpty(clearOrderPushRqt.getMasterIds())){
            return 0;
        }
        // 物理删除
        orderPushManagerGateway.deleteOrderPushByOrderIdAndMasterIds(clearOrderPushRqt.getProvinceNextIds(), clearOrderPushRqt.getOrderId(), clearOrderPushRqt.getMasterIds());
        return 1;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ClearPushStrategyContext.register(OrderPushClearType.OFFER_MODIFY_ORDER, this);
    }
}
