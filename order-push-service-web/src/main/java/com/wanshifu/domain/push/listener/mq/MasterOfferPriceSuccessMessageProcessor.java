package com.wanshifu.domain.push.listener.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.wanshifu.domain.agent.AgentPushDetailOperateService;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.domain.base.ability.DTOFormatAbilityService;
import com.wanshifu.domain.base.handler.AbstractCommonOrderGeneralTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.base.model.PushProducerTagEnum;
import com.wanshifu.domain.base.tools.TopicHelper;
import com.wanshifu.domain.push.gateway.OrderPushListGateway;
import com.wanshifu.domain.push.gateway.OrderPushOperateGateway;
import com.wanshifu.domain.push.listener.mq.message.OrderPushUpdateOfferMessage;
import com.wanshifu.domain.push.model.MasterOfferPriceMessage;
import com.wanshifu.domain.push.model.OrderRemoveTagDTO;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.socre.ability.OrderPushSortScoreService;
import com.wanshifu.domain.special.SpecialOrderOperationService;
import com.wanshifu.domain.special.SpecialOrderResourceService;
import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.infrastructure.NoOfferFilterMaster;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.repository.NoOfferFilterMasterRepository;
import com.wanshifu.order.offer.domains.api.response.SimpleOrderGrab;
import com.wanshifu.order.offer.domains.enums.offer.OfferSourceEnum;
import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderDistanceDTO;
import com.wanshifu.order.push.domains.dto.OrderExtraDataDTO;
import com.wanshifu.order.push.domains.dto.OrderGrabDTO;
import com.wanshifu.order.push.enums.PushBusinessCode;
import com.wanshifu.order.push.request.agent.UpdateAgentByOfferPriceRqt;
import com.wanshifu.order.push.request.special.GetOrderDistanceRqt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 师傅报价
 * @date 2024/3/14 15:18
 */
@ConsumeTag(value = ConsumeTagEnum.MASTER_OFFER_PRICE)
@Slf4j
public class MasterOfferPriceSuccessMessageProcessor extends AbstractCommonOrderGeneralTopicMessageTag<MasterOfferPriceMessage> {


    @Resource
    private OrderPushSortScoreService orderPushSortScoreService;

    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    @Resource
    private CommonAddressService commonAddressService;

    @Resource
    private SpecialOrderGateway specialOrderGateway;

    @Resource
    private SpecialOrderResourceService specialOrderResourceService;

    @Resource
    private SpecialOrderOperationService specialOrderOperationService;

    @Resource
    private AgentPushDetailOperateService agentPushDetailOperateService;

    @Resource
    private OrderPushOperateGateway orderPushOperateGateway;

    @Resource
    private OrderPushListGateway orderPushListGateway;

    @Resource
    private MessageSenderService mqSendGateway;

    @Resource
    private NoOfferFilterMasterRepository noOfferFilterMasterRepository;


    /**
     * 报价后订单移除标签数量配置
     */
    @Value("${remove.bounty.label.offerPriceNum:3}")
    private int removeBountyLabelOfferPriceNum;


    /**
     * 报价后订单移除分区数量配置
     */
    @Value("${remove.bounty.offerPriceNum:6}")
    private int removeBountyOfferPriceNum;

    /**
     * 报价后订单师傅维度移出可联系订单标签配置
     */
    @Value("${offerPrice.remove.order.can.contact.num:3}")
    private int removeCanContactTagLimitCount;

    /**
     * 自动报价场景与更新报价状态场景死锁优化开关
     */
    @Value("${updateOfferDeadLockSwitch:on}")
    private String updateOfferDeadLockSwitch;

    /**
     * 更新报价状态延迟时间
     */
    @Value("${updateOrderPushOfferDelayMillSeconds:1000}")
    private long updateOrderPushOfferDelayMillSeconds;

    @Override
    public void postHandler(MasterOfferPriceMessage masterOfferPriceMessage) {
        log.info("interface.simplify switch,orderInfo:{}", JSONUtil.toJsonStr(masterOfferPriceMessage));

        if(OfferSourceEnum.PREPAYMENT_RESPOND_ORDER.source.equals(masterOfferPriceMessage.getOfferSource())){
            return;
        }
        SimpleOrderGrab simpleOrderGrab = commonOrderOfferService.getSimpleOrderGrabByGlobalId(masterOfferPriceMessage.getGlobalOrderTraceId());
        if(Objects.isNull(simpleOrderGrab) || Objects.isNull(simpleOrderGrab.getOrderBase())){
            throw new BusException(PushBusinessCode.IGNORE_ERROR.code, "订单不存在");
        }

        Long thirdDivisionId = simpleOrderGrab.getOrderBase().getThirdDivisionId();
        if (Objects.isNull(thirdDivisionId) || thirdDivisionId == 0L) {
            return;
        }

        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(thirdDivisionId,
                "topic_common_order_general.master_offerPrice",
                JSONUtil.toJsonStr(masterOfferPriceMessage));

        Long orderId = masterOfferPriceMessage.getMasterOrderId();

        String orderTagTypes = masterOfferPriceMessage.getOrderTagTypes();
        if (!Strings.isNullOrEmpty(orderTagTypes) && orderTagTypes.contains("bounty_order")) {
            int offerNumber = masterOfferPriceMessage.getOfferNumber();
            if (offerNumber == removeBountyLabelOfferPriceNum) {
                //移除悬赏标签
                removeOrderTag(masterOfferPriceMessage.getGlobalOrderTraceId(), orderId);
            }

            if (offerNumber == removeBountyOfferPriceNum) {
                //订单移出悬赏分区
                orderPushOperateGateway.updateMenuCategoryBatchV2(provinceNextId, orderId);
            }

        }

        int offerNumber = masterOfferPriceMessage.getOfferNumber();
        if (offerNumber == removeCanContactTagLimitCount) {
            //移除可联系订单标签
            List<NoOfferFilterMaster> filterMasterList = noOfferFilterMasterRepository.selectByOrderIdAndPutFlagTag(orderId, 1);
            if (CollectionUtil.isNotEmpty(filterMasterList)) {
                removeCanContactTag(masterOfferPriceMessage.getGlobalOrderTraceId(), orderId,filterMasterList.stream().map(NoOfferFilterMaster::getMasterId).collect(Collectors.toList()));
                //报价3次后清理合适师傅数据
                noOfferFilterMasterRepository.deleteByOrderId(orderId);
            }
        }

        //首次报价，更新  订单是否有报价  标记
        if ("on".equals(updateOfferDeadLockSwitch)) {
            OrderPushUpdateOfferMessage message = new OrderPushUpdateOfferMessage();
            message.setOrderId(orderId);
            message.setProvinceNextId(provinceNextId);
            mqSendGateway.sendDelayMessage(TopicHelper.ORDER_PUSH_INTERNAL_NORMAL_BUSINESS_TOPIC, ConsumeTagEnum.ORDER_PUSH_UPDATE_OFFER.getValue(), JSON.toJSONString(message), updateOrderPushOfferDelayMillSeconds);
        } else {
            OrderPush orderPush = orderPushListGateway.getOrderPushByOrderIdAndOffer(provinceNextId, orderId, 1);
            if (Objects.isNull(orderPush)) {
                //首次报价更新
                orderPushOperateGateway.updateOfferBatch(provinceNextId, orderId);
            }
        }


        if(specialOrderGateway.checkNearbyOrderDistanceConfig(3, simpleOrderGrab.getOrderGrab().getSecondDivisionId())){
            GetOrderDistanceRqt rqt = new GetOrderDistanceRqt();
            rqt.setOrderId(orderId);
            OrderDistanceDTO orderDistanceDTO = specialOrderResourceService.selectOrderDistanceByOrderId(rqt);
            if(Objects.isNull(orderDistanceDTO)){
                specialOrderOperationService.insertNearbyOrderList(orderId);
            }
        }

        UpdateAgentByOfferPriceRqt updateAgentByOfferPriceRqt = new UpdateAgentByOfferPriceRqt();
        updateAgentByOfferPriceRqt.setOrderId(orderId);
        updateAgentByOfferPriceRqt.setMasterId(masterOfferPriceMessage.getMasterId());
        updateAgentByOfferPriceRqt.setOfferTime(new Date());
        agentPushDetailOperateService.offerPrice(updateAgentByOfferPriceRqt);


        orderPushSortScoreService.masterOfferSuccessSortNotice(provinceNextId, DTOFormatAbilityService.copyObject(simpleOrderGrab.getOrderBase(), OrderBaseDTO::new),
                DTOFormatAbilityService.copyObject(simpleOrderGrab.getOrderExtraData(), OrderExtraDataDTO::new),
                DTOFormatAbilityService.copyObject(simpleOrderGrab.getOrderGrab(), OrderGrabDTO::new),
                masterOfferPriceMessage.getMasterId());
    }


    /**
     * 悬赏订单移出标签
     * @param globalOrderTraceId
     * @param orderId
     */
    private void removeOrderTag(Long globalOrderTraceId, Long orderId){
        OrderRemoveTagDTO rqt = new OrderRemoveTagDTO();
        rqt.setGlobalOrderTraceId(globalOrderTraceId);
        rqt.setOrderId(orderId);
        rqt.setDimension("order");
        rqt.setTagType("bounty_order");
        rqt.setTagName("悬赏");
        mqSendGateway.sendSyncMessage(TopicHelper.ORDER_PUSH_NOTICE_TOPIC, PushProducerTagEnum.ORDER_REMOVE_TAG.tag, JSON.toJSONString(rqt),true);
    }

    /**
     * 可联系订单移出标签
     *
     * @param globalOrderTraceId
     * @param orderId
     */
    private void removeCanContactTag(Long globalOrderTraceId, Long orderId, List<Long> masterIdList) {
        OrderRemoveTagDTO rqt = new OrderRemoveTagDTO();
        rqt.setGlobalOrderTraceId(globalOrderTraceId);
        rqt.setOrderId(orderId);
        rqt.setDimension("master");
        rqt.setMasterIds(masterIdList);
        rqt.setTagType("order_can_contact");
        rqt.setTagName("可联系订单");
        mqSendGateway.sendSyncMessage(TopicHelper.ORDER_PUSH_NOTICE_TOPIC, PushProducerTagEnum.ORDER_REMOVE_TAG.tag, JSON.toJSONString(rqt), true);
    }
}
