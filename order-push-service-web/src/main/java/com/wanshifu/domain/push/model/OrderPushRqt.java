package com.wanshifu.domain.push.model;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.order.push.request.push.ExclusiveOrderLabel;
import com.wanshifu.order.push.request.push.MasterAddressInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 订单推送类
 */
@Data
public class OrderPushRqt {

    @NotNull
    @Min(1L)
    private Long orderId;

    /**
     * 全局id
     */
    private Long globalOrderTraceId;

    /**
     * 推单业务类型
     * direct_assignment_push: 直接指派推单；
     * direct_push: 直接推单
     * smart_push：智能推单;
     */
    @ValueIn(value = "direct_assignment_push,direct_push,smart_push",required = true)
    private String pushScenarioType;


    /**
     * 业务线id
     */
    @NotNull
    @Min(1)
    private Integer businessLineId;
    /**
     * pre_exclusive:专属订单批量推送
     * pre_exclusive_single:专属订单只有一个专属师傅，系统自动报价
     * agent:代理商 v7.6
     * package_order:订单包 v7.7.2
     * brand: 品牌师傅
     * normal:普通推单模式 (默认)
     * new_model:样板城市推单
     * new_model_single:样板城市推单（只推主力师傅）
     */
    private String pushMode;


    /**
     * 推送师傅信息
     */
    @Valid
    @NotEmpty
    private List<MasterAddressInfo> masterAddressInfoList;

    /**
     * 是否首次推单
     */
    @NotNull
    @Min(0)
    private Integer firstPush;

    /**
     * 普通推单首次有效推单(首轮首次)
     * 0：非，1：是
     */
    private Integer firstTimeValidPush;

    /**
     * 普通推单首次有效师傅推送，(首轮首次)
     * 0：非首次有效推送，1：首次有效推送
     */
    private Integer normalFirstTimeValidPush;

    /**
     * 普通推单首次有效师傅推送匹配的可推单师傅数
     */
    private Integer normalFirstMatchMasterNum;

    /**
     * 推送的地址级别（3：三级地址推单，4：四级地址推单）
     */
    @Value("3,4")
    private Integer pushDivisionLevel;


    /**
     * 专属招募id v7.3
     */
    private Long recruitId;


    /**
     * 是否有价格 v7.3
     */
    private boolean hasPrice;

    /**
     * 是否团队师傅 1-是 0-否
     */
    private Integer teamMasterOrderPush;

    /**
     * 订单包id
     */
    private Long packageId;

    /**
     * 订单包匹配到的子属性
     */
    private String goodsAttribute;

    /**
     * 订单包子属性对应输入值范围
     */
    private String orderPackageAttributeRange;

    /**
     * 推单标识 0：正常推单 1：附近推单
     */
    private Integer pushFlag;

    /**
     * 代理商推单师傅
     */
    private String agentMasterList;

    /**
     * 理商推送类型为push时才有数据，定向推送推单后无人接单重推时间配置
     */
    private Integer agentPushNoHiredRePushTime;

    /**
     * 理商推送类型为push时才有数据，定向推送推单首次查看后无人接单重推时间配置
     */
    private Integer agentFirstViewNoHiredRePushTime;

    /**
     * 是否按照距离推单
     */
    private Boolean accordingDistancePushFlag;

    /**
     * 专属订单标签信息
     */
    @Valid
    private ExclusiveOrderLabel exclusiveOrderLabel;
    /**
     * 推送模式-细分类型(
     * 专属pre_exclusive_single(专属单个师傅),
     *  pre_exclusive_single_transfer_grab_offer_price(专属转抢单))
     */
    private String pushModeType;

    /**
     * 师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    private String masterSourceType;

    private String matchSceneCode;

}
