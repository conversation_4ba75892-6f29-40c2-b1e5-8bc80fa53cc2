package com.wanshifu.domain.push.serviceimpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.domain.agent.AgentPushDetailOperateService;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.domain.base.ability.CommonBooleanValueService;
import com.wanshifu.domain.base.ability.DTOFormatAbilityService;
import com.wanshifu.domain.base.model.PushProducerTagEnum;
import com.wanshifu.domain.base.model.RedisKeyConstant;
import com.wanshifu.domain.base.tools.BeanEnhanceUtil;
import com.wanshifu.domain.base.tools.FeiShuUtils;
import com.wanshifu.domain.base.tools.LongUtil;
import com.wanshifu.domain.base.tools.TopicHelper;
import com.wanshifu.domain.push.OrderPushOperateService;
import com.wanshifu.domain.push.context.ClearPushStrategyContext;
import com.wanshifu.domain.push.gateway.OrderPushOperateGateway;
import com.wanshifu.domain.push.gateway.OrderPushResourceGateway;
import com.wanshifu.domain.push.model.*;
import com.wanshifu.domain.push.model.event.PushedResultSuccessEvent;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.fulfill.CommonOrderFulfillService;
import com.wanshifu.domain.sdk.master.CommonMasterService;
import com.wanshifu.domain.push.model.PushLessContendOrderRqt;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.socre.ability.OrderPushSortScoreService;
import com.wanshifu.domain.special.gateway.SpecialOrderGateway;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.OrderDistance;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.master.order.domains.enums.ServeTransferStatusEnum;
import com.wanshifu.master.order.domains.fulfillment.po.OrderServeTransferInfo;
import com.wanshifu.order.offer.domains.DTO.OrderAutoGrabDTO;
import com.wanshifu.order.offer.domains.api.response.infoorder.GetInfoOrderBaseCompositeResp;
import com.wanshifu.order.offer.domains.enums.*;
import com.wanshifu.master.order.domains.po.ServeStop;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.offer.PushingOrderCompositeResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.code.OfferBusinessCode;
import com.wanshifu.order.offer.domains.enums.offer.OrderOfferLimitCount;
import com.wanshifu.order.offer.domains.po.InfoOrderBase;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.offer.domains.po.OrderOfferLimit;
import com.wanshifu.order.push.domains.dto.OrderBaseCompositeDTO;
import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderExtraDataDTO;
import com.wanshifu.order.push.domains.dto.OrderGrabDTO;
import com.wanshifu.order.push.enums.OrderPushClearType;
import com.wanshifu.order.push.enums.PushBusinessCode;
import com.wanshifu.order.push.enums.PushScenarioType;
import com.wanshifu.order.push.request.agent.ViewOrderRqt;
import com.wanshifu.order.push.request.push.*;
import com.wanshifu.order.push.response.push.BatchUpdatePushDistanceResp;
import com.wanshifu.order.push.response.push.PushedToMasterResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DeadlockLoserDataAccessException;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/28 18:01
 */
@Service
@Slf4j
public class OrderPushOperateServiceImpl implements OrderPushOperateService {

    @Resource
    private OrderPushResourceGateway orderPushResourceGateway;

    @Resource
    private CommonOrderOfferService commonOrderOfferService;
    @Resource
    private OrderPushOperateGateway orderPushOperateGateway;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private CommonBooleanValueService commonBooleanValueService;
    @Resource
    private CommonMasterService commonMasterService;
    @Resource
    private AsyncConfigurer asyncConfigurer;
    @Resource
    private CommonOrderFulfillService commonFulfillOrderService;
    @Resource
    private OrderPushSortScoreService orderPushSortScoreService;

    @Resource
    private SpecialOrderGateway specialOrderGateway;

    @Resource
    private AgentPushDetailOperateService agentPushDetailOperateService;

    @Resource
    private CommonAddressService commonAddressService;

    @Resource(name = "recordTmplCityOrderPushLogExecutor")
    private Executor recordTmplCityOrderPushLogExecutor;

    /**
     * 推单数据插入分布式锁开关
     */
    @Value("${order.orderPush.distributeLockInsertOrderPushSwitch:on}")
    private String distributeLockInsertOrderPushSwitch;

    /**
     * 推单数据插入分布式锁超时时间,单位：毫秒
     */
    @Value("${order.orderPush.distributeLockInsertOrderPushExpireTime:6000}")
    private int distributeLockInsertOrderPushExpireTime;

    /**
     * 推单数据插入批次大小
     */
    @Value("${order.orderPush.insertOrderPushBatchSize:50}")
    private int insertOrderPushBatchSize;


    /**
     * 更新查看标记开关
     */
    @Value("${updatePullView.switch:on}")
    private String updatePullViewSwitch;

    /**
     * 死锁优化开关
     */
    @Value("${order.orderPush.pushDeadLockSwitch:on}")
    private String pushDeadLockSwitch;

    @Autowired
    private RedisHelper redisHelper;


    @Resource(name = "asyncUpdateExecutor")
    private Executor executor;

    @Resource(name = "otherBusDiscardPolicyExecutor")
    private Executor otherExecutor;

    @Resource
    private MessageSenderService mqSendGateway;

    /**
     * 智能排序批量开关
     */
    @Value("${orderSort.batch.switch:on}")
    private String orderSortBatchSwitch;


    @Override
    public PushedToMasterResp pushedToMaster(PushedToMasterRqt pushedToMasterRqt) {
        if (!PushScenarioType.bussWorkScene(pushedToMasterRqt.getPushScenarioType())) {
            throw new BusException("推单场景错误，目前只支持手动推单或 再次推单");
        }

        OrderPushCompositeRqt orderPushCompositeRqt = convertOrderPushComposite(pushedToMasterRqt.getOrderId(),
                pushedToMasterRqt.getMasterAddressInfoList(), new OrderPushRqt());
        //非推单中台通知的，设置推单场景
        orderPushCompositeRqt.setPushScenarioType(pushedToMasterRqt.getPushScenarioType().scenarioVal);
        //设置推送师傅
        orderPushCompositeRqt.setMasterAddressInfoList(pushedToMasterRqt.getMasterAddressInfoList());
        PushedToMasterResp pushedToMasterResp = null;
        //获取省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(pushedToMasterRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.pushedToMaster(com.wanshifu.order.push.request.push.PushedToMasterRqt)",
                JSONUtil.toJsonStr(pushedToMasterRqt));
        try {
            //写推单数据
            pushedToMasterResp = this.commonPushedToMaster(provinceNextId, pushedToMasterRqt, orderPushCompositeRqt);

            if("on".equals(orderSortBatchSwitch)){
                //后续异步处理
                asyncConfigurer.getAsyncExecutor().execute(() -> {
                    //排序通知
                    try {
                        asyncPushSortScore(provinceNextId, orderPushCompositeRqt);
                    } catch (Exception e) {
                        log.error("处理推单异步处理异常:{}", e);
                        FeiShuUtils.sendTempMsg("处理推单结果异步（推单消息排序）", orderPushCompositeRqt, e);
                    }
                });
            }

                //发布推单事件
            pushPushEvent(orderPushCompositeRqt);
        } catch (Exception e) {
            if (commonBooleanValueService.isUnknownException(e)) {
                FeiShuUtils.sendTempMsg("处理订单推单结果(手动推单)", pushedToMasterRqt, e);
            }
            throw e;
        }

        return pushedToMasterResp;
    }


    @Override
    public PushedToMasterResp pushedToMaster (OrderPushRqt orderPushRqt) {
        log.info("订单推单入参:{}", JSONUtil.toJsonStr(orderPushRqt));
        if (!PushScenarioType.pushWorkScene(PushScenarioType.tf(orderPushRqt.getPushScenarioType()))) {
            throw new BusException("推单场景错误，目前只支持直接指派推单、直接推单、智能推单场景");
        }
        try {
            Long orderId = orderPushRqt.getOrderId();
            List<MasterAddressInfo> masterAddressInfoList = orderPushRqt.getMasterAddressInfoList();
            OrderPushCompositeRqt orderPushCompositeRqt = convertOrderPushComposite(orderId,
                    masterAddressInfoList, orderPushRqt);

            //apollo配置控制使用分布式锁开关
            if ("on".equals(distributeLockInsertOrderPushSwitch) && !"on".equals(pushDeadLockSwitch)) {
                String lockKey = "distributeLockPushedToMaster:".concat(orderId.toString());
                long start = System.currentTimeMillis();
                String requestId = UUID.randomUUID().toString();
                boolean lockAcquired = false;
                while (!lockAcquired) {
                    lockAcquired = redisHelper.tryGetDistributedLock(lockKey, requestId, distributeLockInsertOrderPushExpireTime);
                    if (!lockAcquired) {
                        log.error(String.format("distributeLockInsertOrderPush acquired lock fail! order:%d", orderId));
                        // 没获取到锁,等待一段时间后重试
                        long currentTime = System.currentTimeMillis();
                        if (currentTime - start > distributeLockInsertOrderPushExpireTime) {
                            log.error(String.format("distributeLockInsertOrderPush acquired lock expire! order:%d", orderId));
                            //发飞书告警
                            FeiShuUtils.sendTempMsg("distributeLockInsertOrderPush", "orderId:".concat(orderId.toString()), "distributeLockInsertOrderPush acquired lock expire!");
                            // 获取锁超时
                            throw new RuntimeException("acquired lock expire!");
                        }

                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }
                // 获取锁成功
                try {
                    //分批次写push表
                    this.branchExecutor(orderPushCompositeRqt);
                } finally {
                    // 释放分布式锁
                    redisHelper.releaseDistributedLock(lockKey, requestId);
                }
            } else {
                //分批次写push表
                this.branchExecutor(orderPushCompositeRqt);
            }

            //发布推单事件
            this.pushPushEvent(orderPushCompositeRqt);
            //代理商定向推送时逻辑
            if ("agent".equals(orderPushRqt.getPushMode())
                    && Objects.nonNull(orderPushRqt.getAgentFirstViewNoHiredRePushTime())) {
                setAgentFirstViewNoHiredRePushTime4Redis(orderId, orderPushRqt.getAgentFirstViewNoHiredRePushTime());
            }

            //样板城市订单记录推单日志
            if ("new_model".equals(orderPushRqt.getPushMode())
                    || "new_model_transfor_normal".equals(orderPushRqt.getPushMode())) {

                CompletableFuture.runAsync(() -> orderPushOperateGateway.recordTmplCityOrderPushLog(orderPushRqt), recordTmplCityOrderPushLogExecutor);
            }
            //普通推单首次推送时记录匹配的可推单师傅数
            if ("normal".equals(orderPushRqt.getPushMode())) {
                CompletableFuture.runAsync(() -> orderPushOperateGateway.recordFirstPushMatchMaster(orderPushRqt), otherExecutor);
            }
        }catch (Exception e) {
            log.error("pushedToMasterException >>> 订单推单异常。orderId:{}", orderPushRqt.getOrderId(), e);
            if (commonBooleanValueService.isUnknownException(e)) {
                FeiShuUtils.sendTempMsg("处理订单推单结果(智能推单)", orderPushRqt, e);
            }
            throw e;
        }
        return null;
    }

    private void setAgentFirstViewNoHiredRePushTime4Redis(Long orderId, Integer agentFirstViewNoHiredRePushTime) {

        AgentFirstViewNoHiredRePushTime config = new AgentFirstViewNoHiredRePushTime();
        config.setAgentFirstViewNoHiredRePushTime(agentFirstViewNoHiredRePushTime);

        redisHelper.set(RedisKeyConstant.AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME_KEY.concat(orderId.toString()), JSONUtil.toJsonStr(config), 2 * 24 * 60 * 60);
    }

    @Override
    public PushedToMasterResp commonPushedToMaster(List<Long> provinceNextId, PushedToMasterRqt pushedToMasterRqt,
                                                   OrderPushCompositeRqt orderPushRqt) {
        //参数转换
        PushedToMasterGatewayRqt pushedToMasterGatewayRqt = converted(pushedToMasterRqt, orderPushRqt);
        //操作push表，实际推单师傅
        Set<Long> masterIds = orderPushOperateGateway.pushedToMaster(provinceNextId, pushedToMasterGatewayRqt);
        PushingOrderCompositeResp orderComposite = pushedToMasterGatewayRqt.getOrderComposite();
        PushedToMasterResp pushedToMasterResp = new PushedToMasterResp();
        pushedToMasterResp.setOrderBaseCompositeDTO(
                DTOFormatAbilityService.compositeOrderBaseCompositeDTO(orderComposite.getOrderBaseComposite()));
        pushedToMasterResp.setPushMasterIds(masterIds);

        //后续异步处理
        asyncConfigurer.getAsyncExecutor().execute(() -> {
            //排序通知
            try {
                if(!"on".equals(orderSortBatchSwitch)){
                    asyncPushSortScore(provinceNextId, orderPushRqt, pushedToMasterGatewayRqt, pushedToMasterResp);
                };
                putOrderTag(provinceNextId, orderPushRqt, pushedToMasterGatewayRqt, pushedToMasterResp);
                putMasterOrderSpecialGroupTag(orderPushRqt, masterIds);
            } catch (Exception e) {
                log.error("处理推单异步处理异常:{}", e);
                FeiShuUtils.sendTempMsg("处理推单结果异步（推单消息排序）", orderPushRqt, e);
            }
        });

        return pushedToMasterResp;
    }



    @Override
    public InnovateBusinessPushResp innovateBusinessPush(OrderPushRqt rqt) {
        List<MasterAddressInfo> masterAddressInfoList = rqt.getMasterAddressInfoList();
        InnovateBusinessPushGatewayRqt innovateBusinessPushGatewayRqt = convertInnovateRqt(rqt);
        //获取省下级地址id
        List<Long> provinceNextId = getProvinceNextIdByInfoOrderBase(innovateBusinessPushGatewayRqt.getInfoOrderBase());

        InnovateBusinessPushResp innovateBusinessPushResp = orderPushOperateGateway.pushedInnovateToMaster(provinceNextId, innovateBusinessPushGatewayRqt);
        Set<Long> firstPushMasterIds = innovateBusinessPushResp.getFirstPushMasterIds();
        //更新首次推单数量
        this.updateFirstPushNumber(rqt.getOrderId(),firstPushMasterIds);

        Set<Long> pushMasterIds = masterAddressInfoList.stream().map(MasterAddressInfo::getMasterId).collect(Collectors.toSet());
        OrderPushCompositeRqt orderPushCompositeRqt = BeanEnhanceUtil.copyBean(rqt, OrderPushCompositeRqt::new);
        PushedResultSuccessEvent pushedResultSuccessEvent = new PushedResultSuccessEvent(orderPushCompositeRqt);
        pushedResultSuccessEvent.setBusinessLineId(innovateBusinessPushResp.getInfoOrderBase().getBusinessLineId());
        pushedResultSuccessEvent.setInfoOrderBase(innovateBusinessPushResp.getInfoOrderBase());
        pushedResultSuccessEvent.setPushScenarioType(PushScenarioType.tf(rqt.getPushScenarioType()));
        pushedResultSuccessEvent.setPushMode(rqt.getPushMode());
        pushedResultSuccessEvent.setPushMasterIds(pushMasterIds);
        applicationEventPublisher.publishEvent(pushedResultSuccessEvent);
        return innovateBusinessPushResp;
    }



    @Override
    public int resendOrderPushByMaster(ResendOrderPushRqt resendOrderPushRqt) {
        String params = JSON.toJSONString(resendOrderPushRqt);
        log.info("resendOrderPushByMaster={}", params);
        Long masterId = resendOrderPushRqt.getMasterId();
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(resendOrderPushRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.resendOrderPushByMaster",
                params);
        resendOrderPushRqt.setProvinceNextIdList(provinceNextId);
        String oldServeThirdDivisionIds = formatCommaDelimitedString(resendOrderPushRqt.getOldServeThirdDivisionIds());
        String newServeThirdDivisionIds = formatCommaDelimitedString(resendOrderPushRqt.getNewServeThirdDivisionIds());
        String oldServeFourthDivisionIds = formatCommaDelimitedString(resendOrderPushRqt.getOldServeFourthDivisionIds());
        String newServeFourthDivisionIds = formatCommaDelimitedString(resendOrderPushRqt.getNewServeFourthDivisionIds());
        String removeTechnologyIds = resendOrderPushRqt.getRemoveTechnologyIds();

        //确定是否需要变更推单记录
        List<Long> removeServeThirdDivisionIdList = new ArrayList<>();
        List<Long> removeServeFourthDivisionIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(oldServeThirdDivisionIds) && StringUtils.isNotBlank(newServeThirdDivisionIds)
                && !oldServeThirdDivisionIds.equals(newServeThirdDivisionIds)) {
            List<String> oldServeThirdDivisionIdList = StringUtils.splitCommaToList(oldServeThirdDivisionIds);
            List<String> newServeThirdDivisionIdsList = StringUtils.splitCommaToList(newServeThirdDivisionIds);
            oldServeThirdDivisionIdList.removeAll(newServeThirdDivisionIdsList);
            removeServeThirdDivisionIdList.addAll(oldServeThirdDivisionIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
        }

        if (StringUtils.isNotBlank(oldServeFourthDivisionIds) && StringUtils.isNotBlank(newServeFourthDivisionIds)
                && !oldServeFourthDivisionIds.equals(newServeFourthDivisionIds)) {
            List<String> oldServeFourthDivisionIdList = StringUtils.splitCommaToList(oldServeFourthDivisionIds);
            List<String> newServeFourthDivisionIdsList = StringUtils.splitCommaToList(newServeFourthDivisionIds);
            oldServeFourthDivisionIdList.removeAll(newServeFourthDivisionIdsList);
            removeServeFourthDivisionIdList.addAll(oldServeFourthDivisionIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        Boolean isChangeServerThirdDivisionId = CollectionUtils.isNotEmpty(removeServeThirdDivisionIdList);
        Boolean isChangeServerFourthDivisionId = CollectionUtils.isNotEmpty(removeServeFourthDivisionIdList);

        Boolean isChangeDivision = isChangeServerThirdDivisionId || isChangeServerFourthDivisionId;
        Boolean isChangeTechnology = StringUtils.isNotBlank(removeTechnologyIds);
        //处理地址变更 + 技能变更
        if (!isChangeDivision && !isChangeTechnology) {
            return 0;
        }
        //获取当前师傅推单记录
        List<OrderPush> masterNormalList = orderPushResourceGateway.getMasterNormalOrderPushList(provinceNextId, masterId);
        if (CollectionUtils.isEmpty(masterNormalList)) {
            return 0;
        }
        //地区 + 地区+师傅技能
        List<Long> orderIds = masterNormalList.stream().map(OrderPush::getOrderId).distinct().collect(Collectors.toList());
        List<List<Long>> orderIdsPartition = Lists.partition(orderIds, 200);
        List<CompletableFuture<List<OrderBase>>> completableFutureList = new ArrayList<>();
        for (List<Long> orderIdList : orderIdsPartition) {
            CompletableFuture<List<OrderBase>> exceptionally = CompletableFuture.supplyAsync(() -> commonOrderOfferService.getOrderBaseBatchForLimit(orderIdList, null, null),
                    executor
            );
            completableFutureList.add(exceptionally);
        }

        List<OrderBase> orderBaseList = completableFutureList.stream().map(CompletableFuture::join).flatMap(List::stream).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderBaseList)) {
            return 0;
        }
        Map<Long, OrderBase> orderBaseMap = orderBaseList.stream().collect(Collectors.toMap(OrderBase::getOrderId, Function.identity(), (x1, x2) -> x1));

        orderPushOperateGateway.resendOrderPush(resendOrderPushRqt, isChangeDivision, isChangeTechnology, masterId, removeTechnologyIds, removeServeThirdDivisionIdList, removeServeFourthDivisionIdList, masterNormalList, orderBaseMap);
        return 1;
    }

    /**
     * 格式化逗号分隔字符串
     *
     * @param commaDelimitedString
     * @return
     */
    protected String formatCommaDelimitedString(String commaDelimitedString) {

        if (StringUtils.isBlank(commaDelimitedString)) {
            return null;
        }
        return commaDelimitedString.trim().replace("，", ",").replaceAll(",{2,}", ",");
    }

    @Override
    public Integer clearExpiredOrderPush(ClearExpiredPushRqt clearExpiredPushRqt) {
        return orderPushOperateGateway.clearExpiredOrderPush(clearExpiredPushRqt);
    }

    @Override
    public void clearExpiredOrderPushV2(ClearExpiredPushRqt clearExpiredPushRqt) {
        orderPushOperateGateway.clearExpiredOrderPushV2(clearExpiredPushRqt);
    }

    @Override
    @Deprecated
    public BatchUpdatePushDistanceResp batchUpdatePushDistance(BatchUpdatePushDistanceRqt batchUpdatePushDistanceRqt) {
        return orderPushOperateGateway.batchUpdatePushDistance(batchUpdatePushDistanceRqt);
    }

    @Override
    public BatchUpdatePushDistanceResp batchUpdatePushDistanceV2(BatchUpdatePushDistanceV2Rqt batchUpdatePushDistanceV2Rqt) {
        return orderPushOperateGateway.batchUpdatePushDistanceV2(batchUpdatePushDistanceV2Rqt);
    }

    @Override
    @Deprecated
    public int manualClearPush(Long orderId, Long masterId) {
        return orderPushOperateGateway.manualClearPush(orderId, masterId);
    }


    @Override
    public Integer updateOrderPushMenuCategory(UpdateOrderPushMenuCategoryRqt updateOrderPushMenuCategoryRqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(updateOrderPushMenuCategoryRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.updateOrderPushMenuCategory",
                JSONUtil.toJsonStr(updateOrderPushMenuCategoryRqt));
        return orderPushOperateGateway.updateOrderPushMenuCategory(provinceNextId, updateOrderPushMenuCategoryRqt.getOrderId(), updateOrderPushMenuCategoryRqt.getMasterId(), updateOrderPushMenuCategoryRqt.getMenuCategory());
    }

    @Override
    public void pushLessContendOrder(PushLessContendOrderRqt pushLessContendOrderRqt) {
        orderPushOperateGateway.pushLessContendOrder(pushLessContendOrderRqt);
    }


    @Override
    public Integer clearOrderPush(ClearOrderPushRqt clearOrderPushRqt) {
        try {
            //计算省下级地址id
            List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(clearOrderPushRqt.getProvinceNextId(),
                    "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.clearOrderPush",
                    JSONUtil.toJsonStr(clearOrderPushRqt));
            clearOrderPushRqt.setProvinceNextIds(provinceNextId);
            return ClearPushStrategyContext.getInstance(OrderPushClearType.of(clearOrderPushRqt.getBusinessType())).clear(clearOrderPushRqt);
        }catch (Exception e){
            if(e instanceof DeadlockLoserDataAccessException){
                throw new BusException(PushBusinessCode.DEAD_LOCK_ERROR.code, e.getMessage());
            }
            throw e;
        }
    }

    @Override
    public Integer updateOrderPushOfferTime(UpdateOrderPushOfferTimeRqt updateOrderPushOfferTimeRqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(updateOrderPushOfferTimeRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.updateOrderPushOfferTime",
                JSONUtil.toJsonStr(updateOrderPushOfferTimeRqt));
        return orderPushOperateGateway.updateOrderPushOfferTime(provinceNextId, updateOrderPushOfferTimeRqt);
    }

    @Override
    public Integer updateOrderPushFirstViewTime(UpdateFirstViewTimeRqt updateFirstViewTimeRqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(updateFirstViewTimeRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.updateOrderPushFirstViewTime",
                JSONUtil.toJsonStr(updateFirstViewTimeRqt));

        CompletableFuture.runAsync(() -> orderPushOperateGateway.agentPushFirstView(provinceNextId,updateFirstViewTimeRqt.getOrderId(), updateFirstViewTimeRqt.getMasterId()), otherExecutor);

        return orderPushOperateGateway.updateOrderPushFirstViewTime(provinceNextId, updateFirstViewTimeRqt);
    }

    @Override
    public Integer updatePullOrderDistanceByPushId(UpdatePullOrderDistanceRqt updatePullOrderDistanceRqt) {
        log.error("updatePullOrderDistanceByPushIdIsDeprecated!");
        throw new BusException("updatePullOrderDistanceByPushIdIsDeprecated!");
    }

    @Override
    public Integer updatePullOrderDistanceByPushIdV2(UpdatePullOrderDistanceRqtV2 updatePullOrderDistanceRqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(updatePullOrderDistanceRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.updatePullOrderDistanceByPushIdV2",
                JSONUtil.toJsonStr(updatePullOrderDistanceRqt));
        return orderPushOperateGateway.updatePullOrderDistance(provinceNextId, updatePullOrderDistanceRqt);
    }

    @Override
    public Integer updateIsArrivedByOrderId(UpdateIsArrivedRqt updateIsArrivedRqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(updateIsArrivedRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.updateIsArrivedByOrderId",
                JSONUtil.toJsonStr(updateIsArrivedRqt));
        CompletableFuture.runAsync(() -> orderPushOperateGateway.updateIsArrivedByOrderId(provinceNextId, updateIsArrivedRqt.getOrderId(), updateIsArrivedRqt.getIsArrived()), executor);
        return 1;
    }

    private  PushedToMasterGatewayRqt converted (PushedToMasterRqt pushedToMasterRqt,
                                                 OrderPushCompositeRqt orderPushRqt) {
        String pushMold = pushedToMasterRqt.getPushMold();
        PushedToMasterGatewayRqt pushedToMasterGatewayRqt = BeanEnhanceUtil.copyBean(pushedToMasterRqt,PushedToMasterGatewayRqt::new);
        pushedToMasterGatewayRqt.setOrderComposite(orderPushRqt.getOrderComposite());
        pushedToMasterGatewayRqt.setServeStops(orderPushRqt.getServeStops());
        pushedToMasterGatewayRqt.setTechniqueTypeIds(orderPushRqt.getTechniqueTypeIds());
        //代理商标签
        if ("agent".equals(pushMold) ) {
            if (StringUtils.isEmpty(pushedToMasterRqt.getAgentMasterList())) {
                throw new BusException("代理推单模式，缺少代理师傅信息");
            }
            pushedToMasterGatewayRqt.setNobodyOfferHour(1);
            pushedToMasterGatewayRqt.setAgentPushMasterList(JSON.parseArray(pushedToMasterRqt.getAgentMasterList(), AgentPushMaster.class));
            pushedToMasterGatewayRqt.setAgentOrderFlag( OrderTagEnum.AGENT_ORDER.type);
        }
        //耦合专属标签
        ExclusiveOrderLabel exclusiveOrderLabel = pushedToMasterRqt.getExclusiveOrderLabel();
        if (Objects.nonNull(exclusiveOrderLabel) && Objects.nonNull(exclusiveOrderLabel.getExclusiveFlag()) ) {
            pushedToMasterGatewayRqt.setExclusiveFlag(exclusiveOrderLabel.getExclusiveFlag());
        }
        List<MasterAddressInfo> filterMasterAddressInfoList = pushedToMasterGatewayRqt.getMasterAddressInfoList().stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(MasterAddressInfo :: getMasterId))), ArrayList::new));
        //推送的师傅（去重后）
        pushedToMasterGatewayRqt.setMasterAddressInfoList(filterMasterAddressInfoList);
        //推单时间
        pushedToMasterGatewayRqt.setPushTime(new Date());
        return pushedToMasterGatewayRqt;
    }


    private void asyncPushSortScore(List<Long> provinceNextId, OrderPushCompositeRqt orderPushRqt) {

        Set<Long> masterIds = orderPushRqt.getMasterAddressInfoList().stream().map(MasterAddressInfo::getMasterId).collect(Collectors.toSet());
        PushingOrderCompositeResp orderComposite = orderPushRqt.getOrderComposite();
        PushedToMasterResp pushedToMasterResp = new PushedToMasterResp();
        pushedToMasterResp.setOrderBaseCompositeDTO(
                DTOFormatAbilityService.compositeOrderBaseCompositeDTO(orderComposite.getOrderBaseComposite()));
        pushedToMasterResp.setPushMasterIds(masterIds);

        PushScenarioType pushScenarioType = PushScenarioType.tf(orderPushRqt.getPushScenarioType());
        OrderBaseCompositeDTO orderBaseCompositeDTO = pushedToMasterResp.getOrderBaseCompositeDTO();
        String pushMode = orderPushRqt.getPushMode();
        //操作订单排序
        switch (pushScenarioType) {
            case DIRECT_ASSIGNMENT_PUSH:
            case DIRECT_PUSH:
            case SMART_PUSH:
            case MANUAL_PUSH:
                OrderBaseDTO orderBase = orderBaseCompositeDTO.getOrderBaseDTO();
                OrderExtraDataDTO orderExtraData = orderBaseCompositeDTO.getOrderExtraDataDTO();
                OrderGrabDTO orderGrab = orderBaseCompositeDTO.getOrderGrabDTO();

                List<Long> pushMasterIds = new ArrayList<>();
                pushMasterIds.addAll(pushedToMasterResp.getPushMasterIds());
                Integer artificialPush = pushScenarioType.isManualPush() ? 1 : 0;
                orderPushSortScoreService.orderPushSuccessSortNotice(provinceNextId, orderBase, orderExtraData, orderGrab,
                        pushMode,
                        getRecruitTagName(orderPushRqt.getExclusiveOrderLabel()), artificialPush
                        , pushMasterIds,orderPushRqt.getMatchSceneCode());
                break;

            case AGAIN_PUSH:
                break;
        }
    }


    private void asyncPushSortScore(List<Long> provinceNextId, OrderPushRqt orderPushRqt,
                                    PushedToMasterGatewayRqt pushedToMasterGatewayRqt,
                                    PushedToMasterResp pushedToMasterResp) {
        PushScenarioType pushScenarioType = pushedToMasterGatewayRqt.getPushScenarioType();
        OrderBaseCompositeDTO orderBaseCompositeDTO = pushedToMasterResp.getOrderBaseCompositeDTO();
        String pushMode = orderPushRqt.getPushMode();
        //操作订单排序
        switch (pushScenarioType) {
            case DIRECT_ASSIGNMENT_PUSH:
            case DIRECT_PUSH:
            case SMART_PUSH:
            case MANUAL_PUSH:
                OrderBaseDTO orderBase = orderBaseCompositeDTO.getOrderBaseDTO();
                OrderExtraDataDTO orderExtraData = orderBaseCompositeDTO.getOrderExtraDataDTO();
                OrderGrabDTO orderGrab = orderBaseCompositeDTO.getOrderGrabDTO();

                List<Long> pushMasterIds = new ArrayList<>();
                pushMasterIds.addAll(pushedToMasterResp.getPushMasterIds());
                Integer artificialPush = pushScenarioType.isManualPush() ? 1 : 0;
                orderPushSortScoreService.orderPushSuccessSortNotice(provinceNextId, orderBase, orderExtraData, orderGrab,
                        pushMode,
                        getRecruitTagName(orderPushRqt.getExclusiveOrderLabel()), artificialPush
                        , pushMasterIds,orderPushRqt.getMatchSceneCode());
                break;

            case AGAIN_PUSH:
                break;
        }
    }



    private void putOrderTag(List<Long> provinceNextId, OrderPushRqt orderPushRqt,
                                    PushedToMasterGatewayRqt pushedToMasterGatewayRqt,
                                    PushedToMasterResp pushedToMasterResp) {

        if ("toc".equals(pushedToMasterGatewayRqt.getMasterSourceType())) {
            //c端师傅app下架必看分区
            return;
        }
        List<Long> masterIdList = pushedToMasterGatewayRqt.getMasterAddressInfoList().stream().filter(masterAddressInfo -> Objects.nonNull(masterAddressInfo.getMustOrderFlag()) && masterAddressInfo.getMustOrderFlag() == 1).map(MasterAddressInfo::getMasterId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(masterIdList)){
            return ;
        }
        OrderPutTagDTO rqt = new OrderPutTagDTO();
        rqt.setGlobalOrderTraceId(orderPushRqt.getGlobalOrderTraceId());
        rqt.setOrderId(orderPushRqt.getOrderId());
        rqt.setDimension("master");
        rqt.setMasterIds(masterIdList);
        rqt.setTagType("must_order_receive");
        rqt.setTagName("必看");
        rqt.setIsDynamicTag(1);
        mqSendGateway.sendSyncMessage(TopicHelper.ORDER_PUSH_NOTICE_TOPIC, PushProducerTagEnum.ORDER_TAG.tag, JSON.toJSONString(rqt), true);

    }


    private void putMasterOrderSpecialGroupTag(OrderPushRqt orderPushRqt, Set<Long> masterIdList) {
        if (!"special_group".equals(orderPushRqt.getPushMode())) {
            return;
        }

        if(CollectionUtils.isEmpty(masterIdList)){
            return;
        }
        OrderPutTagDTO rqt = new OrderPutTagDTO();
        rqt.setGlobalOrderTraceId(orderPushRqt.getGlobalOrderTraceId());
        rqt.setOrderId(orderPushRqt.getOrderId());
        rqt.setDimension("master");
        rqt.setMasterIds(new ArrayList<>(masterIdList));
        rqt.setTagType("priority_order");
        rqt.setTagName("优先接单");
        rqt.setIsDynamicTag(1);
        mqSendGateway.sendSyncMessage(TopicHelper.ORDER_PUSH_NOTICE_TOPIC, PushProducerTagEnum.ORDER_TAG.tag, JSON.toJSONString(rqt), true);

    }



    private String getRecruitTagName(ExclusiveOrderLabel exclusiveOrderLabel) {
        String recruitTagName = "";
        //更新专属标签
        if (Objects.nonNull(exclusiveOrderLabel)) {
            recruitTagName = exclusiveOrderLabel.getRecruitTagName();
        }
        return recruitTagName;
    }

    /**
     * 推送订单前的校验
     *
     * @param pushOrderComposite
     * @param orderServeTransferInfo
     */
    private void checkOrderPush(String pushMode,String matchSceneCode,PushingOrderCompositeResp pushOrderComposite,
                                MasterAddressInfo masterAddressInfo, OrderServeTransferInfo orderServeTransferInfo) {

        OrderBaseComposite orderBaseComposite = pushOrderComposite.getOrderBaseComposite();
        OrderBase orderBase = orderBaseComposite.getOrderBase();
        OrderGrab orderGrab = orderBaseComposite.getOrderGrab();
        OrderOfferLimit orderOfferLimit = pushOrderComposite.getOrderOfferLimit();
        OrderAutoGrabDTO orderAutoGrabDTO = pushOrderComposite.getOrderAutoGrabDTO();


        if (!OrderStatus.TRADING.code.equals(orderBase.getOrderStatus())) {
            throw new BusException(PushBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code,"当前订单已交易完成/关闭");
        }
        if (Objects.isNull(orderGrab)) {
            if (orderBase.getAccountType().equals(AccountType.ENTERPRISE.code)) {
                throw new BusException(PushBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code, "订单指派记录不存在");
            }
            throw new BusException("订单指派记录不存在");
        }

        boolean isMasterTransfer = checkMasterTransfer(orderServeTransferInfo);

        if (!AppointType.NORMAL.value.equals(orderGrab.getAppointType())) {
            if(!isMasterTransfer){
                if (orderGrab.getHireMasterId() > 0 ||  orderGrab.getConfirmServeStatus() != 0) {
                    throw new BusException(PushBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code, "订单已雇佣师傅！");
                }
            }

        } else {
            Assert.isTrue(orderGrab.getHireMasterId() > 0, "直接雇佣师傅ID缺失");
            if (AccountType.ENTERPRISE.code.equals(orderGrab.getAccountType())) {
                throw new BusException(PushBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code,"总包直接雇佣无需推单!");
            }
            Long masterId = masterAddressInfo.getMasterId();
            Assert.isTrue(masterId.equals(orderGrab.getHireMasterId()), "直接雇佣推送师傅非法");
        }

        if (orderGrab.getEndDate() != null) {
            if (orderGrab.getEndDate().compareTo(new Date()) <= 0) {
                throw new BusException(PushBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code,"订单已截止报价!");
            }
        }

        //如果订单达到最高报价人数则不推单
        if (orderOfferLimit != null && orderOfferLimit.getMaxOfferNumber() > 0 && orderGrab.getOfferNumber() >= orderOfferLimit.getMaxOfferNumber()) {
            if((!"cooperation_business".equals(pushMode)) && (!"extra_contest_offer_number".equals(matchSceneCode)) && (!"full_time_master".equals(pushMode))){
              throw new BusException(PushBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code,"订单已达到最高报价人数!");
            }
        }else if (OrderFrom.IKEA.valueEn.equals(orderBase.getOrderFrom())) {
            //宜家订单限制报价5个人
            if (orderGrab.getOfferNumber() >= OrderOfferLimitCount.IKEA_OFFER_LIMIT_COUNT) {
                throw new BusException(OfferBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code,"订单已达到最高报价人数!");
            }
        } else if ("rigorous_selection".equals(orderBase.getOrderLabel())) {
            //严选订单限制报价15个人
            if (orderGrab.getOfferNumber() >= OrderOfferLimitCount.RIGOROUS_SELECTION_OFFER_LIMIT_COUNT) {
                throw new BusException(OfferBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code,"订单已达到最高报价人数!");
            }
        }
        if (Objects.nonNull(orderAutoGrabDTO) &&  AutoGrabStatus.isWaitConfirm(orderAutoGrabDTO.getGrabStatus())){
            throw new BusException(OfferBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code,"订单锁单中，不支持推单!");
        }

    }

    private boolean checkMasterTransfer(OrderServeTransferInfo orderServeTransferInfo) {
        if(Objects.isNull(orderServeTransferInfo)){
            return false;
        }
        return ServeTransferStatusEnum.TRANSFERRING.code.equals(orderServeTransferInfo.getTransferStatus());
    }


    /**
     * 转换orderPush通用对象，对数据进行校验
     * @param orderId
     * @param masterAddressInfoList
     * @param orderPushRqt
     * @return
     */
    private  OrderPushCompositeRqt convertOrderPushComposite(   Long orderId,
                                                                List<MasterAddressInfo> masterAddressInfoList,
                                                                OrderPushRqt orderPushRqt) {
        //订单信息
        PushingOrderCompositeResp pushingOrderCompositeResp = commonOrderOfferService.getPushingOrderCompositeResp(orderId);
        if (Objects.isNull(pushingOrderCompositeResp) || Objects.isNull(pushingOrderCompositeResp.getOrderBaseComposite())) {
            throw new BusException("获取订单信息失败");
        }
        OrderBase orderBase = pushingOrderCompositeResp.getOrderBaseComposite().getOrderBase();
        // 查询师傅转单
        OrderServeTransferInfo orderServeTransferInfo = commonFulfillOrderService.getOrderServeTransferInfo(orderBase.getGlobalOrderTraceId());
        //校验订单数据
        checkOrderPush(orderPushRqt.getPushMode(),orderPushRqt.getMatchSceneCode(),pushingOrderCompositeResp, CollUtil.getFirst(masterAddressInfoList), orderServeTransferInfo);
        //查询技能类型
        List<String> techniqueTypeIds = commonMasterService.getTechniqueTypeIdByOrderBindingTechnologyIds(orderBase.getBindingTechnologyIds());
        String  techniqueTypeIdStr = String.join(",", techniqueTypeIds);
        List<ServeStop> orderServeStopRecording = null;
        if (AccountType.ENTERPRISE.code.equals(orderBase.getAccountType()) &&
                OrderFrom.ENTERPRISE_SYSTEM.valueEn.equals(orderBase.getOrderFrom())) {
            //总包外部订单。需要推单前，先查询已取消指派的信息，后面用于过滤师傅
            orderServeStopRecording = commonFulfillOrderService.getOrderServeStopRecording(orderId);
        }

        OrderPushCompositeRqt orderPushCompositeRqt = BeanEnhanceUtil.copyBean(orderPushRqt, OrderPushCompositeRqt::new);
        orderPushCompositeRqt.setOrderComposite(pushingOrderCompositeResp);
        orderPushCompositeRqt.setServeStops(orderServeStopRecording);
        orderPushCompositeRqt.setTechniqueTypeIds(techniqueTypeIdStr);
        return orderPushCompositeRqt;
    }


    /**
     * 转换成gateway参数
     * @param orderPushRqt
     * @return
     */
    private InnovateBusinessPushGatewayRqt convertInnovateRqt(OrderPushRqt orderPushRqt) {
        GetInfoOrderBaseCompositeResp orderBaseCompositeResp = commonOrderOfferService.getInfoOrderBase(orderPushRqt.getOrderId());
        InfoOrderBase infoOrderBase = orderBaseCompositeResp.getInfoOrderBase();
        if (!OrderStatus.TRADING.code.equals(infoOrderBase.getOrderStatus())){
            throw new BusException(OfferBusinessCode.MQ_FILTER_MESSAGE_ERR0R.code, "当前订单已关闭");
        }
        //获取订单对应技能类型
        List<String> techniqueTypeIds = commonMasterService.getTechniqueTypeIdByOrderBindingTechnologyIds(infoOrderBase.getBindingTechnologyIds());
        if (CollUtil.isEmpty(techniqueTypeIds)) {
            log.error("orderInnovatePushMasterService-1, orderId:{}, techniqueTypeIds为空", orderPushRqt.getOrderId());
        }
        InnovateBusinessPushGatewayRqt innovateBusinessPushRqt = new InnovateBusinessPushGatewayRqt();
        List<MasterAddressInfo> masterAddressInfoList = orderPushRqt.getMasterAddressInfoList();
        List<Long> masterIds = masterAddressInfoList.stream().map(MasterAddressInfo::getMasterId).collect(Collectors.toList());
        innovateBusinessPushRqt.setMasterIdList(masterIds);
        innovateBusinessPushRqt.setMasterAddressInfoList(masterAddressInfoList);
        innovateBusinessPushRqt.setOrderId(orderPushRqt.getOrderId());
        innovateBusinessPushRqt.setPushTime(new Date());
        innovateBusinessPushRqt.setTechniqueTypeIds(String.join(",", techniqueTypeIds));
        innovateBusinessPushRqt.setInfoOrderBase(infoOrderBase);
        innovateBusinessPushRqt.setMasterSourceType(orderPushRqt.getMasterSourceType());

        return innovateBusinessPushRqt;
    }


    private void updateFirstPushNumber(Long infoOrderId,Set<Long> firstPushMasterIds){
        if (CollectionUtils.isEmpty(firstPushMasterIds)){
            return;
        }
        commonOrderOfferService.updateInfoOrderFirstPushNumber(infoOrderId,firstPushMasterIds.size());
    }



    private void branchExecutor (OrderPushCompositeRqt orderPushRqt) {

        List<Long> provinceNextId = getProvinceNextIdByOrderPushRqt(orderPushRqt);

        List<MasterAddressInfo> masterAddressInfoList = orderPushRqt.getMasterAddressInfoList();

        if ("on".equals(pushDeadLockSwitch)) {

            //这里不做分批，异步逻辑里面进行分批推单
            writePush(masterAddressInfoList ,orderPushRqt);

        } else {


            //分批次进行推单
            int size = masterAddressInfoList.size();
            int baseNum = insertOrderPushBatchSize;
            List<MasterAddressInfo> newMasterList;
            if (size > baseNum) {
                int mod = size % baseNum;
                int quotient = size / baseNum;
                if (mod > 0) {
                    quotient += 1;
                }
                for (int i = 0; i < quotient; i++) {
                    int formIndex = i * baseNum;
                    if (i == quotient - 1) {
                        int toIndex = 0;
                        if (mod == 0) {
                            toIndex = size;
                        } else {
                            toIndex = formIndex + mod;
                        }
                        newMasterList = masterAddressInfoList.subList(formIndex, toIndex);
                    } else {
                        newMasterList = masterAddressInfoList.subList(formIndex, formIndex + baseNum);
                    }
                    writePush(newMasterList,orderPushRqt);
                }
            } else if (size > 0) {
                writePush (masterAddressInfoList ,orderPushRqt);
            } else {
                log.warn("batchPushAlgorithmPushOrderLog..orderId={},size={},msg={}", orderPushRqt.getOrderId(), size, "筛选师傅的个数是0");
            }


        }



        if("on".equals(orderSortBatchSwitch)){
            //后续异步处理
            asyncConfigurer.getAsyncExecutor().execute(() -> {
                //排序通知
                try {
                    asyncPushSortScore(provinceNextId, orderPushRqt);
                } catch (Exception e) {
                    log.error("处理推单异步处理异常:{}", e);
                    FeiShuUtils.sendTempMsg("处理推单结果异步（推单消息排序）", orderPushRqt, e);
                }
            });
        }


    }


    protected void writePush (List<MasterAddressInfo> masterAddressInfoList,
                              OrderPushCompositeRqt orderPushRqt){
        //推单等级
        Long orderId = orderPushRqt.getOrderId();
        List<Long> masterIdList = masterAddressInfoList.stream().map(MasterAddressInfo::getMasterId).collect(Collectors.toList());
        log.info("writePush..orderId=[{}];masterIdList=[{}],orderPushRqt={}", orderId, masterIdList, JSON.toJSONString(orderPushRqt));
        if (commonBooleanValueService.isPressureBeta()) {
            return;
        }
        PushedToMasterRqt pushedToMasterRqt = new PushedToMasterRqt();
        pushedToMasterRqt.setOrderId(orderId);
        pushedToMasterRqt.setPushMold(orderPushRqt.getPushMode());
        pushedToMasterRqt.setPushScenarioType(PushScenarioType.tf(orderPushRqt.getPushScenarioType()));
        pushedToMasterRqt.setMasterAddressInfoList(masterAddressInfoList);
        pushedToMasterRqt.setPushDivisionLevel(Optional.ofNullable(orderPushRqt.getPushDivisionLevel()).orElse(3));
        pushedToMasterRqt.setPushFlag(Optional.ofNullable(orderPushRqt.getPushFlag()).orElse(0));
        pushedToMasterRqt.setAccordingDistancePushFlag((Objects.nonNull(orderPushRqt.getAccordingDistancePushFlag()) && orderPushRqt.getAccordingDistancePushFlag() ) ? 1 : 0);
        pushedToMasterRqt.setAgentMasterList(orderPushRqt.getAgentMasterList());
        pushedToMasterRqt.setExclusiveOrderLabel(orderPushRqt.getExclusiveOrderLabel());
        pushedToMasterRqt.setMasterSourceType(orderPushRqt.getMasterSourceType());

        //获取省下级地址id
        List<Long> provinceNextId = getProvinceNextIdByOrderPushRqt(orderPushRqt);
        this.commonPushedToMaster(provinceNextId, pushedToMasterRqt, orderPushRqt);
    }





    private void pushPushEvent(OrderPushCompositeRqt orderPushCompositeRqt) {
        OrderBaseComposite orderBaseComposite = orderPushCompositeRqt.getOrderComposite().getOrderBaseComposite();
        PushedResultSuccessEvent pushedResultSuccessEvent = new PushedResultSuccessEvent(orderPushCompositeRqt);
        pushedResultSuccessEvent.setBusinessLineId(orderBaseComposite.getOrderBase().getBusinessLineId());
        pushedResultSuccessEvent.setPushMode(orderPushCompositeRqt.getPushMode());
        pushedResultSuccessEvent.setPushScenarioType(PushScenarioType.tf(orderPushCompositeRqt.getPushScenarioType()));
        Set<Long> pushMasterIds = orderPushCompositeRqt.getMasterAddressInfoList().stream().map(MasterAddressInfo::getMasterId).collect(Collectors.toSet());
        pushedResultSuccessEvent.setPushMasterIds(pushMasterIds);
        applicationEventPublisher.publishEvent(pushedResultSuccessEvent);
    }

    private List<Long> getProvinceNextIdByOrderPushRqt(OrderPushCompositeRqt orderPushRqt) {
        if (Objects.isNull(orderPushRqt)) {
            log.error("getProvinceNextIdByOrderPushRqt fail! orderPushRqt is null!");
            throw new BusException("getProvinceNextIdByOrderPushRqt fail!");
        }
        if (Objects.isNull(orderPushRqt.getOrderComposite())) {
            log.error("getProvinceNextIdByOrderPushRqt fail! getOrderComposite is null!");
            throw new BusException("getProvinceNextIdByOrderPushRqt fail!");
        }
        if (Objects.isNull(orderPushRqt.getOrderComposite().getOrderBaseComposite())) {
            log.error("getProvinceNextIdByOrderPushRqt fail! OrderBaseComposite is null!");
            throw new BusException("getProvinceNextIdByOrderPushRqt fail!");
        }
        if (Objects.isNull(orderPushRqt.getOrderComposite().getOrderBaseComposite().getOrderBase())) {
            log.error("getProvinceNextIdByOrderPushRqt fail! OrderBase is null!");
            throw new BusException("getProvinceNextIdByOrderPushRqt fail!");
        }
        Long thirdDivisionId = orderPushRqt.getOrderComposite().getOrderBaseComposite().getOrderBase().getThirdDivisionId();
        Long fourthDivisionId = orderPushRqt.getOrderComposite().getOrderBaseComposite().getOrderBase().getFourthDivisionId();
        List<Long> provinceNextId = null;
        String params = JSONUtil.toJsonStr(orderPushRqt);
        if (LongUtil.notEmpty(thirdDivisionId)) {
            provinceNextId = commonAddressService.getProvinceNextIdV2(thirdDivisionId, "commonPushedToMaster.getProvinceNextIdByOrderPushRqt", params);
        } else if (LongUtil.notEmpty(fourthDivisionId)) {
            provinceNextId = commonAddressService.getProvinceNextIdV2(fourthDivisionId, "commonPushedToMaster.getProvinceNextIdByOrderPushRqt", params);
        }
        if (Objects.isNull(provinceNextId)) {
            log.error("getProvinceNextIdByOrderPushRqt fail! provinceNextId error is 0L");
            throw new BusException("getProvinceNextIdByOrderPushRqt fail!");
        }
        return provinceNextId;
    }

    private List<Long> getProvinceNextIdByInfoOrderBase(InfoOrderBase infoOrderBase) {
        if (Objects.isNull(infoOrderBase)) {
            log.error("getProvinceNextIdByInfoOrderBase fail! infoOrderBase is null!");
            throw new BusException("getProvinceNextIdByInfoOrderBase fail!");
        }
        Long thirdDivisionId = infoOrderBase.getThirdDivisionId();
        Long fourthDivisionId = infoOrderBase.getFourthDivisionId();
        List<Long> provinceNextId = null;
        String params = JSONUtil.toJsonStr(infoOrderBase);
        if (LongUtil.notEmpty(thirdDivisionId)) {
            provinceNextId = commonAddressService.getProvinceNextIdV2(thirdDivisionId, "commonPushedToMaster.getProvinceNextIdByInfoOrderBase", params);
        } else if (LongUtil.notEmpty(fourthDivisionId)) {
            provinceNextId = commonAddressService.getProvinceNextIdV2(fourthDivisionId, "commonPushedToMaster.getProvinceNextIdByInfoOrderBase", params);
        }
        if (Objects.isNull(provinceNextId)) {
            log.error("getProvinceNextIdByInfoOrderBase fail! provinceNextId error is 0L");
            throw new BusException("getProvinceNextIdByInfoOrderBase fail!");
        }
        return provinceNextId;
    }

    @Override
    public Integer updatePullViewTime(UpdatePullViewTimeRqt updatePullViewTimeRqt){
        if(!"on".equals(updatePullViewSwitch)){
            return 0;
        }
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(updatePullViewTimeRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.updatePullViewTime",
                JSONUtil.toJsonStr(updatePullViewTimeRqt));
        return orderPushOperateGateway.updatePullViewTime(provinceNextId, updatePullViewTimeRqt);
    }

    @Override
    public void afterOrderDetail(AfterOrderDetailRqt afterOrderDetailRqt) {
        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(afterOrderDetailRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushOperateServiceImpl.afterOrderDetail",
                JSONUtil.toJsonStr(afterOrderDetailRqt));

        Long orderId = afterOrderDetailRqt.getOrderId();
        Long masterId = afterOrderDetailRqt.getMasterId();
        Date firstViewTime = afterOrderDetailRqt.getFirstViewTime();
        Long secondDivisionId = afterOrderDetailRqt.getSecondDivisionId();
        Integer categoryId = afterOrderDetailRqt.getCategoryId();
        Integer agentViewOrderFlag = afterOrderDetailRqt.getAgentViewOrderFlag();

        OrderPush orderPush = orderPushResourceGateway.getOrderPush(provinceNextId, orderId, masterId);
        if (Objects.isNull(orderPush)) {
            return;
        }

        //1.更新查看推单记录时间
        UpdateFirstViewTimeRqt updateFirstViewTimeRqt = new UpdateFirstViewTimeRqt();
        updateFirstViewTimeRqt.setOrderId(orderId);
        updateFirstViewTimeRqt.setMasterId(masterId);
        updateFirstViewTimeRqt.setFirstViewTime(firstViewTime);
        orderPushOperateGateway.updateOrderPushFirstViewTime(provinceNextId, updateFirstViewTimeRqt);

        //2.发送附近单mq
        if (Objects.nonNull(secondDivisionId)
                && Objects.nonNull(categoryId)) {
            //检查配置
            boolean checkSideOrderDistanceConfigFlag = specialOrderGateway.checkSideOrderDistanceConfig(2, categoryId, secondDivisionId, orderPush.getPushDistance());
            //检查是否已经生成附近单
            OrderDistance orderDistance = specialOrderGateway.selectOrderDistanceByOrderId(orderId);
            if (checkSideOrderDistanceConfigFlag && ObjectUtil.isNull(orderDistance)) {
                //发送附近单mq
                specialOrderGateway.nearbyOrderPushDistance(orderId);
            }
        }

        //3.代理商查看订单
        if (agentViewOrderFlag == 1) {
            ViewOrderRqt viewOrderRqt = new ViewOrderRqt();
            viewOrderRqt.setOrderId(orderId);
            viewOrderRqt.setMasterId(masterId);
            viewOrderRqt.setViewTime(firstViewTime);
            agentPushDetailOperateService.viewOrder(viewOrderRqt);
        }

    }
}
