package com.wanshifu.domain.push.serviceimpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.base.ability.DTOFormatAbilityService;
import com.wanshifu.domain.base.model.RedisKeyConstant;
import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.base.tools.BeanEnhanceUtil;
import com.wanshifu.domain.push.OrderPushListService;
import com.wanshifu.domain.push.gateway.OrderPushListGateway;
import com.wanshifu.domain.push.model.*;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.api.BigdataOpenServiceApi;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.sdk.resp.GetMstServeCompleteInfoV3Resp;
import com.wanshifu.domain.sdk.rqt.GetMstServeCompleteInfoV3Rqt;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DataUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.gatewayreq.push.MasterAppletListOrderPushGatewayRqt;
import com.wanshifu.order.config.api.GoodsServiceApi;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.po.Goods;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.*;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.enums.OrderTagEnum;
import com.wanshifu.order.offer.domains.po.*;
import com.wanshifu.order.push.domains.dto.*;
import com.wanshifu.order.push.request.*;
import com.wanshifu.order.push.request.push.MasterAppletListOrderPushRqt;
import com.wanshifu.order.push.request.push.TmplCityOrderPushRqt;
import com.wanshifu.order.push.response.*;
import com.wanshifu.order.push.response.push.MasterAppletListOrderPushResp;
import com.wanshifu.order.push.response.push.TmplCityOrderPushResp;
import com.wanshifu.orderconfig.api.GoodsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/22 18:44
 */
@Service
@Slf4j
public class OrderPushListServiceImpl implements OrderPushListService {


    @Value("${offerList.tobSortQueryType.switch:true}")
    private Boolean tobSortQueryTypeSwitch;

    @Value("${order.orderPush.masterAppletListOrderPushSwitch:on}")
    private String masterAppletListOrderPushSwitch;

    /**
     * 样板城市推单列表开关
     * 快速降级使用
     */
    @Value(("${tmpl.city.push.list.switch:on}"))
    private String tmplCityOrderPushSwitch;


    @Value("${getPushedExperiencedMasterList.limitCount:300}")
    private Integer getPushedExperiencedMasterListLimitCount;

    @Autowired
    private RedisHelper redisHelper;

    @Resource
    private OrderPushListGateway orderPushListGateway;
    @Resource
    private CommonOrderOfferService commonOrderOfferService;
    @Resource
    private CommonAddressService commonAddressService;

    @Resource
    private ApolloSwitchUtils apolloSwitchUtils;

    @Value("${order.orderPush.getSiteOrderDetailMasterListSwitch:on}")
    private String getSiteOrderDetailMasterListSwitch;


    /**
     * 查询师傅待接单列表特定商家订单
     * 快速降级使用
     */
    @Value(("${master.user.orderPushListSwitch:on}"))
    private String masterUserOrderPushListSwitch;


    /**
     * ai查询订单推送师傅列表开关
     * 快速降级使用
     */
    @Value(("${api.orderPushList.switch:on}"))
    private String aiOrderPushListSwitch;

    /**
     *
     * 快速降级使用
     */
    @Value(("${getPushedExperiencedMasterList.switch:on}"))
    private String getPushedExperiencedMasterListSwitch;


    /**
     *
     * 快速降级使用
     */
    @Value(("${getPushMasterList.switch:on}"))
    private String getPushMasterListSwitch;


    @Resource
    private BigdataOpenServiceApi bigdataOpenServiceApi;

    @Resource
    private ServeServiceApi serveServiceApi;

    @Resource
    private GoodsApi goodsApi;

    @Resource
    private GoodsServiceApi goodsServiceApi;


    @Value("${pushed.experienced.master.serveCompleteCnt:0}")
    private Integer pushedExperiencedMasterServeCompleteCnt;


    @Resource
    private CallGateway callGateway;


    @Override
    public SimplePageInfo<WaitOfferV2Resp> waitOfferSpecialList(WaitOfferSpecialListRqt rqt) {
        //获取省下级地址id
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.waitOfferSpecialList",
                JSONUtil.toJsonStr(rqt));

        SimplePageInfo<WaitOfferV2RespBo> waitOfferV2RespBoSimplePageInfo = orderPushListGateway.waitOfferSpecialList(provinceNextIds, rqt);

        SimplePageInfo<WaitOfferV2Resp> pageInfo = new SimplePageInfo<>();

        if (Objects.isNull(waitOfferV2RespBoSimplePageInfo) || CollectionUtils.isEmpty(waitOfferV2RespBoSimplePageInfo.getList())) {
            return pageInfo;
        }

        List<WaitOfferV2Resp> waitOfferV2RespList = waitOfferV2RespBoSimplePageInfo.getList().stream().map(DTOFormatAbilityService::getWaitOfferV2Resp).collect(Collectors.toList());
        pageInfo.setList(waitOfferV2RespList);
        pageInfo.setTotal(waitOfferV2RespBoSimplePageInfo.getTotal());
        pageInfo.setPageNum(waitOfferV2RespBoSimplePageInfo.getPageNum());
        pageInfo.setPageSize(waitOfferV2RespBoSimplePageInfo.getPageSize());
        pageInfo.setPages(waitOfferV2RespBoSimplePageInfo.getPages());
        return pageInfo;

    }


    /**
     * 师傅待报价订单列表V2 App7.0多版本兼容
     * 综合排序: 宜家订单，推送时间倒序排列--->严选订单，推送时间倒序排列--->“直接指派”的订单，同为“直接指派”的订单按订单推送的时间倒序排序
     * --->一口价订单，推送时间倒序排列--->其余的订单，按推送的时间倒序排序--->意向单全部排在最末尾
     * sortQueryType: 代表优先排序的条件
     * newOrder[新单]: 订单仅按照订单推送时间先后排列
     * ---已移除---competitionIsLess[竞争少]: 将所有竞争少订单置顶，竞争少订单按照报价人数越少的排在前面，相同报价人数的按照订单推送时间先后排列
     * arrival[已到货]: 含物流信息的订单，物流状态为已到货；不含物流信息板块的订单，都默认为“已到货”
     * divisionId和divisionId: 代表筛选条件
     * 意向单不存在竞争少排序
     * 模式: 报价包含直接雇佣
     * <p>
     * 筛选：订单来源(order_from),1:商家(site),2:总包(enterprise_system、thirdpart),3:宜家(ikea),4:家庭(applet)
     *
     * @param waitOfferRqt
     * @return
     */
    @Override
    public SimplePageInfo<WaitOfferV2Resp> waitOfferV2(WaitOfferV2Rqt waitOfferRqt) {
        log.info("orderPushListWaitOfferV2 params 待报价列表参数 >>> {}", JSONUtil.toJsonStr(waitOfferRqt));
        WaitOfferToV2 waitOfferTo = convertedWaitOfferToV2(waitOfferRqt);
        //获取省下级地址id
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(waitOfferRqt.getProvinceNextId(), "waitOfferV2",JSONUtil.toJsonStr(waitOfferRqt));
        waitOfferTo.setProvinceNextIds(provinceNextIds);
        waitOfferTo.setFromAccount(waitOfferRqt.getFromAccount());
        waitOfferTo.setOrderId(waitOfferTo.getOrderId());
        Integer tmplCityMasterRole = waitOfferRqt.getTmplCityMasterRole();
        if (Objects.nonNull(tmplCityMasterRole)) {
            if (tmplCityMasterRole == 1) {
                //主力师傅只查样板城市订单
                waitOfferTo.setTmplCityFlag(Lists.newArrayList(1));
            } else if (tmplCityMasterRole == 3) {
                //普通师傅只查非样板城市订单+样板城市订单转推普通师傅订单
                waitOfferTo.setTmplCityFlag(Lists.newArrayList(0, 2));
            }
        }
        SimplePageInfo<WaitOfferV2RespBo> waitOfferV2RespBoSimplePageInfo = orderPushListGateway.waitOfferV2(waitOfferTo, waitOfferRqt.getCityId(),
                waitOfferRqt.getPageNum(),
                waitOfferRqt.getPageSize());
        SimplePageInfo<WaitOfferV2Resp> pageInfo = new SimplePageInfo<>();

        if (Objects.isNull(waitOfferV2RespBoSimplePageInfo) || CollectionUtils.isEmpty(waitOfferV2RespBoSimplePageInfo.getList())) {
            return pageInfo;
        }

        List<WaitOfferV2Resp> waitOfferV2RespList = waitOfferV2RespBoSimplePageInfo.getList().stream().map(DTOFormatAbilityService::getWaitOfferV2Resp).collect(Collectors.toList());
        pageInfo.setList(waitOfferV2RespList);
        pageInfo.setTotal(waitOfferV2RespBoSimplePageInfo.getTotal());
        pageInfo.setPageNum(waitOfferV2RespBoSimplePageInfo.getPageNum());
        pageInfo.setPageSize(waitOfferV2RespBoSimplePageInfo.getPageSize());
        pageInfo.setPages(waitOfferV2RespBoSimplePageInfo.getPages());
        return pageInfo;

    }

    @Override
    public SimplePageInfo<TmplCityOrderPushResp> tmplCityOrderPushList(TmplCityOrderPushRqt rqt) {
        if (!"on".equals(tmplCityOrderPushSwitch)) {
            //样板城市推单列表开关
            return new SimplePageInfo<>();
        }

        if (!redisHelper.exists(RedisKeyConstant.MASTER_TMPL_CITY_FLAG_KEY.concat(rqt.getMasterId().toString()))) {
            //该师傅在缓存中不存在的家庭样板城市推单标识，说明该师傅不是储备师傅，没有样板城市推单(主力师傅是会直接指派自动接单的)，
            //或者该师傅是储备师傅，但是样板城市推单数据已经弹窗了，现在没有样板城市推单数据了，直接返回
            return new SimplePageInfo<>();
        }

        //获取省下级地址id
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.tmplCityOrderPushList",
                JSONUtil.toJsonStr(rqt));

        rqt.setProvinceNextIdList(provinceNextIds);

        //查询推单信息
        SimplePageInfo<OrderPush> orderPushSimplePageInfo = orderPushListGateway.tmplCityOrderPushList(rqt);
        if (Objects.isNull(orderPushSimplePageInfo) || CollectionUtils.isEmpty(orderPushSimplePageInfo.getList())) {
            return new SimplePageInfo<>();
        }

        List<OrderPush> orderPushList = orderPushSimplePageInfo.getList();
        //查询订单信息
        List<Long> orderIds = orderPushList.stream().map(OrderPush::getOrderId).collect(Collectors.toList());
        OrderInfoBatchComposite orderInfoBatchComposite = commonOrderOfferService.getOrderInfoBatchComposite(orderIds);

        List<TmplCityOrderPushResp> tmplCityOrderPushList = new ArrayList<>();
        orderPushList.forEach(it -> tmplCityOrderPushList.add(setTmplCityOrderPushResp(it, orderInfoBatchComposite)));

        SimplePageInfo<TmplCityOrderPushResp> respSimplePageInfo = new SimplePageInfo<>(tmplCityOrderPushList);
        respSimplePageInfo.setPageNum(orderPushSimplePageInfo.getPageNum());
        respSimplePageInfo.setPages(orderPushSimplePageInfo.getPages());
        respSimplePageInfo.setPageSize(orderPushSimplePageInfo.getPageSize());
        respSimplePageInfo.setTotal(orderPushSimplePageInfo.getTotal());
        return respSimplePageInfo;
    }

    private WaitOfferToV2 convertedWaitOfferToV2(WaitOfferV2Rqt waitOfferRqt) {
        WaitOfferToV2 waitOfferTo = DataUtils.copyObject(waitOfferRqt, WaitOfferToV2.class);
        //筛选：订单来源
        waitOfferTo.setOrderAccountLabel(waitOfferRqt.getOrderFrom());
        //休息中加急单查询
        if (waitOfferRqt.getPushFrom() != null && waitOfferRqt.getPushFrom() == 2) {
            Assert.notNull(waitOfferRqt.getRestStartTime(), "查询加急单，休息开始时间不能为空");
            waitOfferTo.setPushFrom(waitOfferRqt.getPushFrom());
            waitOfferTo.setRestStartTime(waitOfferRqt.getRestStartTime());
        }
        String sortQueryType = waitOfferTo.getSortQueryType();
        if (tobSortQueryTypeSwitch) {
            if (StringUtils.isBlank(sortQueryType)) {
                waitOfferTo.setSortQueryType("tobCustomCategory");
            }
        }
        //产品定义需求。竞争少榜单查询条件，不支持智能排序，避免查询报错，如果传了智能排序类型，默认给空
        if (Objects.nonNull(waitOfferRqt.getLessContendFlag()) &&
                StringUtils.isNotEmpty(sortQueryType) && sortQueryType.equals("smart")) {
            sortQueryType = null;
            waitOfferTo.setSortQueryType(null);
        }

        //智能排序查询的order_push_score表
        String tableName = StringUtils.equals(sortQueryType, "smart") ? "ltb_order_push_score" : "ltb_order_push";
        waitOfferTo.setTableName(tableName);
        return waitOfferTo;
    }

    @Override
    public List<WaitOfferNoPageResp> waitOfferNoPage(WaitOfferNoPageRqt waitOfferNoPageRqt) {
        List<Long> thirdDivisionIds = waitOfferNoPageRqt.getThirdDivisionIds();
        //计算省下级地址id
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(waitOfferNoPageRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.waitOfferNoPage",
                JSONUtil.toJsonStr(waitOfferNoPageRqt));

        //样板城市订单二期项目，根据不同师傅角色查询推单
        List<Integer> tmplCityFlag = Lists.newArrayList();
        Integer tmplCityMasterRole = waitOfferNoPageRqt.getTmplCityMasterRole();
        if (Objects.nonNull(tmplCityMasterRole)) {
            if (tmplCityMasterRole == 1) {
                //主力师傅只查样板城市订单
                tmplCityFlag.add(1);
            } else if (tmplCityMasterRole == 3) {
                //普通师傅只查非样板城市订单+样板城市订单转推普通师傅订单
                tmplCityFlag.add(0);
                tmplCityFlag.add(2);
            }
        }

        WaitOfferNoPageBo waitOfferNoPageBo = WaitOfferNoPageBo
                .builder()
                .masterId(waitOfferNoPageRqt.getMasterId())
                .currentDateTime(new Date())
                .thirdDivisionIds(CollectionUtils.isNotEmpty(thirdDivisionIds) ? thirdDivisionIds : null)
                .provinceNextId(provinceNextIds)
                .tmplCityFlag(tmplCityFlag)
                .build();
        waitOfferNoPageBo.setExclusiveAppointType(4);
        List<WaitOfferNoPageRespBo> waitOfferNoPageRespBos = orderPushListGateway.waitOfferNoPage(waitOfferNoPageBo);
        if (CollectionUtils.isEmpty(waitOfferNoPageRespBos)) {
            return new ArrayList<>();
        }
        return DTOFormatAbilityService.getWaitOfferNoPageRespList(waitOfferNoPageRespBos);

    }


    @Override
    public List<BatchOrderPushResp> getMasterOrderPushList(WaitOfferNoPageRqt waitOfferNoPageRqt) {
        List<Long> thirdDivisionIds = waitOfferNoPageRqt.getThirdDivisionIds();
        //计算省下级地址id
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(waitOfferNoPageRqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.waitOfferNoPage",
                JSONUtil.toJsonStr(waitOfferNoPageRqt));

        //样板城市订单二期项目，根据不同师傅角色查询推单
        List<Integer> tmplCityFlag = Lists.newArrayList();
        Integer tmplCityMasterRole = waitOfferNoPageRqt.getTmplCityMasterRole();
        if (Objects.nonNull(tmplCityMasterRole)) {
            if (tmplCityMasterRole == 1) {
                //主力师傅只查样板城市订单
                tmplCityFlag.add(1);
            } else if (tmplCityMasterRole == 3) {
                //普通师傅只查非样板城市订单+样板城市订单转推普通师傅订单
                tmplCityFlag.add(0);
                tmplCityFlag.add(2);
            }
        }

        WaitOfferNoPageBo waitOfferNoPageBo = WaitOfferNoPageBo
                .builder()
                .masterId(waitOfferNoPageRqt.getMasterId())
                .currentDateTime(new Date())
                .thirdDivisionIds(CollectionUtils.isNotEmpty(thirdDivisionIds) ? thirdDivisionIds : null)
                .provinceNextId(provinceNextIds)
                .tmplCityFlag(tmplCityFlag)
                .build();
        List<OrderPush> orderPushList = orderPushListGateway.selectMasterOrderPushList(waitOfferNoPageBo);

        List<BatchOrderPushResp> orderPushResps = new ArrayList<>();
        orderPushList.forEach(it ->{
            BatchOrderPushResp batchOrderPushResp = BeanEnhanceUtil.copyBean(it, BatchOrderPushResp::new);
            orderPushResps.add(batchOrderPushResp);
        });
        return orderPushResps;

    }

    @Override
    public List<IocWaitOfferFilterResp> getIocWaitOfferList(IocWaitOfferFilterRqt rqt) {
        Integer queryNumber = rqt.getQueryNumber();
        Long masterId = rqt.getMasterId();
        //计算省下级地址id
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(),
                "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.getIocWaitOfferList",
                JSONUtil.toJsonStr(rqt));
        List<OrderPush> orderPushes = orderPushListGateway.selectOrderPushMaxLimit300(provinceNextIds, masterId, queryNumber);
        if (CollectionUtils.isEmpty(orderPushes)) {
            return new ArrayList<>();
        }
        List<Long> orderIds = orderPushes.stream().map(OrderPush::getOrderId).collect(Collectors.toList());
        //批量查询订单信息
        List<BatchOrderGoodsResp> orderGoodsBatch = commonOrderOfferService.getOrderGoodsBatch(orderIds);
        if (CollectionUtils.isEmpty(orderGoodsBatch)) {
            return new ArrayList<>();
        }

        Map<Long, OrderBase> orderBaseMap = new HashMap<>();
        Map<Long,Integer> orderGoodsNumberMap = new HashMap<>();
        Map<Long, OrderPush> orderPushMap = orderPushes.stream().collect(Collectors.toMap(OrderPush::getOrderId, orderPush -> orderPush));
        orderGoodsBatch.forEach(e -> {
            OrderBase orderBase = e.getOrderBase();
            Long orderId = orderBase.getOrderId();
            orderBaseMap.put(orderId,orderBase);
            orderGoodsNumberMap.put(orderId, e.getSumNumber());
        });

        return orderIds.stream().map(orderId -> {
            OrderBase orderBase = orderBaseMap.get(orderId);
            IocWaitOfferFilterResp result = DataUtils.copyObject(orderBase,IocWaitOfferFilterResp.class);
            result.setAppointType(orderPushMap.get(orderId).getAppointType());
            Integer goodsNumber = orderGoodsNumberMap.get(orderId);
            if (goodsNumber != null) {
                result.setGoodsNumber(goodsNumber);
            } else {
                result.setGoodsNumber(0);
            }
            return result;
        }).collect(Collectors.toList());
    }



    @Override
    public List<WaitOfferV2Resp> batchGetOrderWaitOfferInfo(BatchGetOrderWaitOfferInfoReq batchGetOrderWaitOfferInfoReq) {
        return new ArrayList<>();
    }


    @Override
    public List<BatchOrderPushResp> getOrderPushForNotice(Long orderId, Long provinceNextId) {
        //现在业务没有存省下级地址id,业务传的是订单区域id
        List<Long> trueProvinceNextIds = commonAddressService.getProvinceNextIdV2(provinceNextId, "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.getOrderPushForNotice", String.valueOf(orderId));
        List<OrderPush> orderPushForNotice = orderPushListGateway.getOrderPushForNotice(trueProvinceNextIds, orderId);
        if (CollectionUtils.isEmpty(orderPushForNotice)) {
            return new ArrayList<>();
        }
        List<BatchOrderPushResp> orderPushResps = new ArrayList<>();
        orderPushForNotice.forEach(it -> {
            BatchOrderPushResp batchOrderPushResp = BeanEnhanceUtil.copyBean(it, BatchOrderPushResp::new);
            orderPushResps.add(batchOrderPushResp);
        });
        return orderPushResps;
    }

    @Override
    public List<BatchOrderPushResp> getOrderPushList(Long orderId, Long provinceNextId) {
        //现在业务没有存省下级地址id,业务传的是订单区域id
        List<Long> trueProvinceNextId = commonAddressService.getProvinceNextIdV2(provinceNextId, "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.getOrderPushList", String.valueOf(orderId));
        List<OrderPush> orderPushes = orderPushListGateway.selectOrderPushByOrderId(trueProvinceNextId, orderId);
        if (CollectionUtils.isEmpty(orderPushes)) {
            return new ArrayList<>();
        }
        List<BatchOrderPushResp> orderPushResps = new ArrayList<>();
        orderPushes.forEach(it ->{
            BatchOrderPushResp batchOrderPushResp = BeanEnhanceUtil.copyBean(it, BatchOrderPushResp::new);
            orderPushResps.add(batchOrderPushResp);
        });
        return orderPushResps;
    }

    @Override
    public List<MasterAppletListOrderPushResp> unLoginGetOrderPushList(MasterAppletListOrderPushRqt rqt) {

        if (!"on".equals(masterAppletListOrderPushSwitch)) {
            //开关没打开直接返回空数组
            return new ArrayList<>();
        }

        if (Objects.isNull(rqt) || Objects.isNull(rqt.getMasterDivisionId())) {
            return new ArrayList<>();
        }
        //获取省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(), "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.unLoginGetOrderPushList", JSONUtil.toJsonStr(rqt));

        MasterAppletListOrderPushGatewayRqt masterAppletListOrderPushGatewayRqt = new MasterAppletListOrderPushGatewayRqt();
        masterAppletListOrderPushGatewayRqt.setMasterDivisionId(rqt.getMasterDivisionId());
        masterAppletListOrderPushGatewayRqt.setProvinceNextId(provinceNextId);
        masterAppletListOrderPushGatewayRqt.setMasterSourceType(rqt.getMasterSourceType());

        //查询推单信息
        List<OrderPush> orderPushList = orderPushListGateway.listByMasterDivisionIdForAppletUnLogin(masterAppletListOrderPushGatewayRqt);
        if (CollectionUtil.isEmpty(orderPushList)) {
            return new ArrayList<>();
        }

        //查询订单信息
        List<Long> orderIds = orderPushList.stream().map(OrderPush::getOrderId).collect(Collectors.toList());
        OrderInfoBatchComposite orderInfoBatchComposite = commonOrderOfferService.getOrderInfoBatchComposite(orderIds);

        List<MasterAppletListOrderPushResp> orderPushResp = new ArrayList<>();
        orderPushList.forEach(it -> orderPushResp.add(setMasterAppletListOrderPushResp(it, orderInfoBatchComposite)));
        return orderPushResp;
    }

    /**
     * 组装返回信息
     * @param orderPush
     * @param orderInfoBatchComposite
     * @return
     */
    public MasterAppletListOrderPushResp setMasterAppletListOrderPushResp(OrderPush orderPush, OrderInfoBatchComposite orderInfoBatchComposite) {
        Long orderId = orderPush.getOrderId();

        MasterAppletListOrderPushResp masterAppletListOrderPushResp = new MasterAppletListOrderPushResp();
        masterAppletListOrderPushResp.setAppointType(orderPush.getAppointType());
        masterAppletListOrderPushResp.setOrderId(orderId);
        masterAppletListOrderPushResp.setStopOfferTime(orderPush.getStopOfferTime());
        masterAppletListOrderPushResp.setPushDistance(orderPush.getPushDistance());
        masterAppletListOrderPushResp.setPushDistanceType(orderPush.getPushDistanceType());
        masterAppletListOrderPushResp.setFirstPullTime(orderPush.getFirstPullTime());
        masterAppletListOrderPushResp.setPushTime(orderPush.getPushTime());

        OrderBase orderBase = null;
        OrderExtraData orderExtraData = null;
        OrderLogisticsInfo orderLogisticsInfo = null;
        OrderReturnLogistics orderReturnLogistics = null;
        OrderInitFee orderInitFee = null;
        OrderAward orderAward = null;
        OrderEpcGenreData orderEpcGenre = null;
        EmergencyOrder emergencyOrder = null;
        OrderTag orderTag = null;
        OrderTag noticeOrderTag = null;
        OrderGrab orderGrab = null;
        List<OrderExclusiveTagResp> orderTagResp = null;
        OrderRateAward orderRateAward = null;
        Integer imageCount = 0;
        boolean hasVideo = false;
        List<IkeaOrderGoodsComposite> ikeaOrderGoodsCompositeList = new ArrayList<>();
        List<OrderGoodsComposite> orderGoodsCompositeList = new ArrayList<>();
        List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = new ArrayList<>();
        if (orderInfoBatchComposite != null) {
            List<OrderBase> orderBases = orderInfoBatchComposite.getOrderBaseList();
            List<OrderExtraData> orderExtraDataList = orderInfoBatchComposite.getOrderExtraDataList();
            List<OrderLogisticsInfo> orderLogisticsInfos = orderInfoBatchComposite.getOrderLogisticsInfoList();
            List<OrderReturnLogistics> orderReturnLogisticList = orderInfoBatchComposite.getOrderReturnLogisticList();
            List<OrderInitFee> orderInitFeeList = orderInfoBatchComposite.getOrderInitFeeList();
            List<OrderGrab> orderGrabList = orderInfoBatchComposite.getOrderGrabList();

            orderInitFee = orderInitFeeList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElseThrow(() -> new BusException("订单初始费用信息不存在"));
            orderBase = orderBases.stream().filter(order -> order.getOrderId().equals(orderId)).findFirst().orElseThrow(() -> new BusException("订单基础信息不存在"));
            orderExtraData = orderExtraDataList.stream().filter(extra -> extra.getOrderId().equals(orderId)).findFirst().orElseThrow(() -> new BusException("订单扩展信息不存在"));
            orderGrab = orderGrabList.stream().filter(grab -> grab.getOrderId().equals(orderId)).findFirst().orElse(null);
            orderLogisticsInfo = orderLogisticsInfos.stream().filter(logistic -> logistic.getOrderId().equals(orderId)).findFirst().orElse(null);
            orderReturnLogistics = orderReturnLogisticList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getOrderAwardList())) {
                orderAward = orderInfoBatchComposite.getOrderAwardList().stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getOrderEpcGenreDataList())) {
                orderEpcGenre = orderInfoBatchComposite.getOrderEpcGenreDataList().stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getEmergencyOrderList())) {
                emergencyOrder = orderInfoBatchComposite.getEmergencyOrderList().stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getOrderTagList())) {
                orderTag = orderInfoBatchComposite.getOrderTagList().stream().filter(o -> o.getOrderId().equals(orderId) && Objects.equals(o.getTagValue(), OrderTagEnum.AGENT_ORDER.type)).findFirst().orElse(null);
                noticeOrderTag = orderInfoBatchComposite.getOrderTagList().stream().filter(o -> o.getOrderId().equals(orderId) && Objects.equals(o.getTagValue(), OrderTagEnum.LESS_CONTEND.type)).findFirst().orElse(null);
            }

            List<OrderExclusiveTagResp> orderTagResps = orderInfoBatchComposite.getOrderTagResp();
            if(CollectionUtils.isNotEmpty(orderTagResps)){
                Map<Long, List<OrderExclusiveTagResp>> tagMap = orderTagResps.stream().collect(Collectors.groupingBy(OrderExclusiveTagResp::getOrderId));
                if(Objects.nonNull(tagMap)){
                    orderTagResp = tagMap.get(orderId);
                }
            }

            List<OrderRateAward> orderRateAwardList = orderInfoBatchComposite.getOrderRateAwardList();
            if(CollectionUtils.isNotEmpty(orderRateAwardList)){
                orderRateAward = orderRateAwardList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }

            Map<Long, Integer> imageCountMap = orderInfoBatchComposite.getImageCountMap();
            if(Objects.nonNull(imageCountMap)){
                imageCount = imageCountMap.get(orderId);
            }
            Map<Long, Integer> videoNumberMap = orderInfoBatchComposite.getVideoNumberMap();
            if(Objects.nonNull(videoNumberMap) && Objects.nonNull(videoNumberMap.get(orderId))){
                hasVideo = videoNumberMap.get(orderId) > 0;
            }


            if (OrderFrom.IKEA.valueEn.equals(orderBase.getOrderFrom())) {
                List<IkeaOrderGoodsComposite> ikeaOrderGoodsComposites = orderInfoBatchComposite.getIkeaOrderGoodsComposites();
                if(CollectionUtils.isNotEmpty(ikeaOrderGoodsComposites)){
                    ikeaOrderGoodsCompositeList = ikeaOrderGoodsComposites.stream().filter(o -> orderId.equals(o.getOrderIkeaGoods().getOrderId())).collect(Collectors.toList());
                }
            }else {
                if (orderBase.getOrderServeVersion() == 0) {
                    List<OrderGoodsComposite> orderGoodsComposites = orderInfoBatchComposite.getOrderGoodsComposites();
                    if(CollectionUtils.isNotEmpty(orderGoodsComposites)){
                        orderGoodsCompositeList = orderGoodsComposites.stream().filter(o -> orderId.equals(o.getOrderGoods().getOrderId())).collect(Collectors.toList());
                    }
                } else {
                    List<OrderServiceAttributeInfo> orderServiceAttributeInfos = orderInfoBatchComposite.getOrderServiceAttributeInfos();
                    if(CollectionUtils.isNotEmpty(orderServiceAttributeInfos)){
                        orderServiceAttributeInfoList = orderServiceAttributeInfos.stream().filter(o -> orderId.equals(o.getOrderId())).collect(Collectors.toList());
                    }
                }
            }

        }


        masterAppletListOrderPushResp.setOrderBaseDTO(copyObject(orderBase, OrderBaseDTO::new));
        masterAppletListOrderPushResp.setOrderExtraData(copyObject(orderExtraData, OrderExtraDataDTO::new));
        masterAppletListOrderPushResp.setOrderLogisticsInfoDTO(copyObject(orderLogisticsInfo, OrderLogisticsInfoDTO::new));
        masterAppletListOrderPushResp.setOrderReturnLogisticsDTO(copyObject(orderReturnLogistics, OrderReturnLogisticsDTO::new));
        masterAppletListOrderPushResp.setOrderInitFeeDTO(copyObject(orderInitFee, OrderInitFeeDTO::new));

        masterAppletListOrderPushResp.setOrderAwardDTO(copyObject(orderAward, OrderAwardDTO::new));
        masterAppletListOrderPushResp.setOrderEpcGenreDataDTO(copyObject(orderEpcGenre,OrderEpcGenreDataDTO::new));
        masterAppletListOrderPushResp.setEmergencyOrderDTO(copyObject(emergencyOrder, EmergencyOrderDTO::new));
        masterAppletListOrderPushResp.setOrderExclusiveTagDTOS(DTOFormatAbilityService.buildOrderExclusiveTagDTOS(orderTagResp));
        //返回订单支付状态
        if (ObjectUtil.isNotNull(orderGrab) && AppointType.DEFINITE_PRICE.value.equals(orderGrab.getAppointType()) && orderGrab.getOrderPayStatus() == 1) {
            masterAppletListOrderPushResp.setOrderPayStatus(orderGrab.getOrderPayStatus());
        }

        //接单易4.7,好评返现
        if (ObjectUtil.isNotNull(orderRateAward)) {
            masterAppletListOrderPushResp.setRateAwardFee(orderRateAward.getRateAwardFee());
        }

        masterAppletListOrderPushResp.setHasVideo(hasVideo);
        masterAppletListOrderPushResp.setImageCount(imageCount);
        masterAppletListOrderPushResp.setIkeaOrderGoodsCompositeDTOS(DTOFormatAbilityService.buildIkeaOrderGoodsCompositeDTOS(ikeaOrderGoodsCompositeList));
        masterAppletListOrderPushResp.setOrderGoodsCompositeDTOS(DTOFormatAbilityService.buildOrderGoodsCompositeDTOS(orderGoodsCompositeList));
        masterAppletListOrderPushResp.setOrderServiceAttributeInfoDTOS(DTOFormatAbilityService.buildOrderServiceAttributeInfoDTOS(orderServiceAttributeInfoList));

        masterAppletListOrderPushResp.setIsAgentOrder(orderTag != null ? 1 : 0);
        Optional.ofNullable(noticeOrderTag).ifPresent(it-> masterAppletListOrderPushResp.setPushNoticeLabel(OrderTagEnum.LESS_CONTEND.name));

        return masterAppletListOrderPushResp;
    }

    /**
     * 组装返回信息
     * @param orderPush
     * @param orderInfoBatchComposite
     * @return
     */
    private TmplCityOrderPushResp setTmplCityOrderPushResp(OrderPush orderPush, OrderInfoBatchComposite orderInfoBatchComposite) {
        Long orderId = orderPush.getOrderId();

        TmplCityOrderPushResp tmplCityOrderPushResp = new TmplCityOrderPushResp();
        tmplCityOrderPushResp.setIsIntention(orderPush.getIsIntention());
        tmplCityOrderPushResp.setAppointType(orderPush.getAppointType());
        tmplCityOrderPushResp.setOrderId(orderId);
        tmplCityOrderPushResp.setStopOfferTime(orderPush.getStopOfferTime());
        tmplCityOrderPushResp.setPushDistance(orderPush.getPushDistance());
        tmplCityOrderPushResp.setPushDistanceType(orderPush.getPushDistanceType());
        tmplCityOrderPushResp.setFirstPullTime(orderPush.getFirstPullTime());
        tmplCityOrderPushResp.setPushTime(orderPush.getPushTime());

        OrderBase orderBase = null;
        OrderExtraData orderExtraData = null;
        OrderLogisticsInfo orderLogisticsInfo = null;
        OrderReturnLogistics orderReturnLogistics = null;
        OrderInitFee orderInitFee = null;
        OrderAward orderAward = null;
        OrderEpcGenreData orderEpcGenre = null;
        EmergencyOrder emergencyOrder = null;
        OrderTag orderTag = null;
        OrderTag noticeOrderTag = null;
        OrderGrab orderGrab = null;
        List<OrderExclusiveTagResp> orderTagResp = null;
        OrderRateAward orderRateAward = null;
        Integer imageCount = 0;
        boolean hasVideo = false;
        List<IkeaOrderGoodsComposite> ikeaOrderGoodsCompositeList = new ArrayList<>();
        List<OrderGoodsComposite> orderGoodsCompositeList = new ArrayList<>();
        List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = new ArrayList<>();
        List<OrderAddItemServiceInfo> orderAddItemServiceInfoList = new ArrayList<>();
        if (orderInfoBatchComposite != null) {
            List<OrderBase> orderBases = orderInfoBatchComposite.getOrderBaseList();
            List<OrderExtraData> orderExtraDataList = orderInfoBatchComposite.getOrderExtraDataList();
            List<OrderLogisticsInfo> orderLogisticsInfos = orderInfoBatchComposite.getOrderLogisticsInfoList();
            List<OrderReturnLogistics> orderReturnLogisticList = orderInfoBatchComposite.getOrderReturnLogisticList();
            List<OrderInitFee> orderInitFeeList = orderInfoBatchComposite.getOrderInitFeeList();
            List<OrderGrab> orderGrabList = orderInfoBatchComposite.getOrderGrabList();

            orderInitFee = orderInitFeeList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElseThrow(() -> new BusException("订单初始费用信息不存在"));
            orderBase = orderBases.stream().filter(order -> order.getOrderId().equals(orderId)).findFirst().orElseThrow(() -> new BusException("订单基础信息不存在"));
            orderExtraData = orderExtraDataList.stream().filter(extra -> extra.getOrderId().equals(orderId)).findFirst().orElseThrow(() -> new BusException("订单扩展信息不存在"));
            orderGrab = orderGrabList.stream().filter(grab -> grab.getOrderId().equals(orderId)).findFirst().orElse(null);
            orderLogisticsInfo = orderLogisticsInfos.stream().filter(logistic -> logistic.getOrderId().equals(orderId)).findFirst().orElse(null);
            orderReturnLogistics = orderReturnLogisticList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getOrderAwardList())) {
                orderAward = orderInfoBatchComposite.getOrderAwardList().stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getOrderEpcGenreDataList())) {
                orderEpcGenre = orderInfoBatchComposite.getOrderEpcGenreDataList().stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getEmergencyOrderList())) {
                emergencyOrder = orderInfoBatchComposite.getEmergencyOrderList().stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }
            if (CollUtil.isNotEmpty(orderInfoBatchComposite.getOrderTagList())) {
                orderTag = orderInfoBatchComposite.getOrderTagList().stream().filter(o -> o.getOrderId().equals(orderId) && Objects.equals(o.getTagValue(), OrderTagEnum.AGENT_ORDER.type)).findFirst().orElse(null);
                noticeOrderTag = orderInfoBatchComposite.getOrderTagList().stream().filter(o -> o.getOrderId().equals(orderId) && Objects.equals(o.getTagValue(), OrderTagEnum.LESS_CONTEND.type)).findFirst().orElse(null);
            }

            List<OrderExclusiveTagResp> orderTagResps = orderInfoBatchComposite.getOrderTagResp();
            if(CollectionUtils.isNotEmpty(orderTagResps)){
                Map<Long, List<OrderExclusiveTagResp>> tagMap = orderTagResps.stream().collect(Collectors.groupingBy(OrderExclusiveTagResp::getOrderId));
                if(Objects.nonNull(tagMap)){
                    orderTagResp = tagMap.get(orderId);
                }
            }

            List<OrderRateAward> orderRateAwardList = orderInfoBatchComposite.getOrderRateAwardList();
            if(CollectionUtils.isNotEmpty(orderRateAwardList)){
                orderRateAward = orderRateAwardList.stream().filter(o -> o.getOrderId().equals(orderId)).findFirst().orElse(null);
            }

            Map<Long, Integer> imageCountMap = orderInfoBatchComposite.getImageCountMap();
            if(Objects.nonNull(imageCountMap)){
                imageCount = imageCountMap.get(orderId);
            }
            Map<Long, Integer> videoNumberMap = orderInfoBatchComposite.getVideoNumberMap();
            if(Objects.nonNull(videoNumberMap) && Objects.nonNull(videoNumberMap.get(orderId))){
                hasVideo = videoNumberMap.get(orderId) > 0;
            }


            if (OrderFrom.IKEA.valueEn.equals(orderBase.getOrderFrom())) {
                List<IkeaOrderGoodsComposite> ikeaOrderGoodsComposites = orderInfoBatchComposite.getIkeaOrderGoodsComposites();
                if(CollectionUtils.isNotEmpty(ikeaOrderGoodsComposites)){
                    ikeaOrderGoodsCompositeList = ikeaOrderGoodsComposites.stream().filter(o -> orderId.equals(o.getOrderIkeaGoods().getOrderId())).collect(Collectors.toList());
                }
            }else {
                if (orderBase.getOrderServeVersion() == 0) {
                    List<OrderGoodsComposite> orderGoodsComposites = orderInfoBatchComposite.getOrderGoodsComposites();
                    if(CollectionUtils.isNotEmpty(orderGoodsComposites)){
                        orderGoodsCompositeList = orderGoodsComposites.stream().filter(o -> orderId.equals(o.getOrderGoods().getOrderId())).collect(Collectors.toList());
                    }
                } else {
                    List<OrderServiceAttributeInfo> orderServiceAttributeInfos = orderInfoBatchComposite.getOrderServiceAttributeInfos();
                    if(CollectionUtils.isNotEmpty(orderServiceAttributeInfos)){
                        orderServiceAttributeInfoList = orderServiceAttributeInfos.stream().filter(o -> orderId.equals(o.getOrderId())).collect(Collectors.toList());
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(orderInfoBatchComposite.getOrderAddItemServiceInfoList())){
                orderAddItemServiceInfoList = orderInfoBatchComposite.getOrderAddItemServiceInfoList().stream().filter(o -> orderId.equals(o.getOrderId())).collect(Collectors.toList());
            }

        }


        tmplCityOrderPushResp.setOrderBaseDTO(copyObject(orderBase, OrderBaseDTO::new));
        tmplCityOrderPushResp.setOrderExtraData(copyObject(orderExtraData, OrderExtraDataDTO::new));
        tmplCityOrderPushResp.setOrderLogisticsInfoDTO(copyObject(orderLogisticsInfo, OrderLogisticsInfoDTO::new));
        tmplCityOrderPushResp.setOrderReturnLogisticsDTO(copyObject(orderReturnLogistics, OrderReturnLogisticsDTO::new));
        tmplCityOrderPushResp.setOrderInitFeeDTO(copyObject(orderInitFee, OrderInitFeeDTO::new));

        tmplCityOrderPushResp.setOrderAwardDTO(copyObject(orderAward, OrderAwardDTO::new));
        tmplCityOrderPushResp.setOrderEpcGenreDataDTO(copyObject(orderEpcGenre,OrderEpcGenreDataDTO::new));
        tmplCityOrderPushResp.setEmergencyOrderDTO(copyObject(emergencyOrder, EmergencyOrderDTO::new));
        tmplCityOrderPushResp.setOrderExclusiveTagDTOS(DTOFormatAbilityService.buildOrderExclusiveTagDTOS(orderTagResp));
        tmplCityOrderPushResp.setOrderAddItemServiceInfoDTOList(DTOFormatAbilityService.buildOrderAddItemServiceInfoDTOList(orderAddItemServiceInfoList));
        //返回订单支付状态
        if (ObjectUtil.isNotNull(orderGrab) && AppointType.DEFINITE_PRICE.value.equals(orderGrab.getAppointType()) && orderGrab.getOrderPayStatus() == 1) {
            tmplCityOrderPushResp.setOrderPayStatus(orderGrab.getOrderPayStatus());
        }

        //接单易4.7,好评返现
        if (ObjectUtil.isNotNull(orderRateAward)) {
            tmplCityOrderPushResp.setRateAwardFee(orderRateAward.getRateAwardFee());
        }

        tmplCityOrderPushResp.setHasVideo(hasVideo);
        tmplCityOrderPushResp.setImageCount(imageCount);
        tmplCityOrderPushResp.setIkeaOrderGoodsCompositeDTOS(DTOFormatAbilityService.buildIkeaOrderGoodsCompositeDTOS(ikeaOrderGoodsCompositeList));
        tmplCityOrderPushResp.setOrderGoodsCompositeDTOS(DTOFormatAbilityService.buildOrderGoodsCompositeDTOS(orderGoodsCompositeList));
        tmplCityOrderPushResp.setOrderServiceAttributeInfoDTOS(DTOFormatAbilityService.buildOrderServiceAttributeInfoDTOS(orderServiceAttributeInfoList));

        tmplCityOrderPushResp.setIsAgentOrder(orderTag != null ? 1 : 0);
        Optional.ofNullable(noticeOrderTag).ifPresent(it-> tmplCityOrderPushResp.setPushNoticeLabel(OrderTagEnum.LESS_CONTEND.name));

        return tmplCityOrderPushResp;
    }



    private  <B>B copyObject(Object o1 , Supplier<B> supplier) {
        if (Objects.isNull(o1)) {
            return null;
        }
        B b = supplier.get();
        BeanUtils.copyProperties(o1,b);
        return b;
    }


    @Override
    public List<BatchOrderPushResp> getMasterOrderPushByAccount(Long provinceNextId,Long masterId,Long accountId,String accountType) {

        if(!"on".equals(masterUserOrderPushListSwitch)){
            return new ArrayList<>();
        }
        //现在业务没有存省下级地址id,业务传的是订单区域id
        List<Long> trueProvinceNextId = commonAddressService.getProvinceNextIdV2(provinceNextId, "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.getOrderPushList", String.valueOf(masterId));
        List<OrderPush> orderPushes = orderPushListGateway.selectMasterOrderPushByAccount(trueProvinceNextId, masterId,accountId,accountType);
        if (CollectionUtils.isEmpty(orderPushes)) {
            return new ArrayList<>();
        }
        List<BatchOrderPushResp> orderPushResps = new ArrayList<>();
        orderPushes.forEach(it ->{
            BatchOrderPushResp batchOrderPushResp = BeanEnhanceUtil.copyBean(it, BatchOrderPushResp::new);
            orderPushResps.add(batchOrderPushResp);
        });
        return orderPushResps;
    }


    @Override
    public List<BatchOrderPushResp> getOrderPushForAiNotice(GetOrderPushForAiNoticeRqt rqt) {

        if(!"on".equals(aiOrderPushListSwitch)){
            return new ArrayList<>();
        }
        //现在业务没有存省下级地址id,业务传的是订单区域id
        List<Long> trueProvinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(), "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.getOrderPushList", JSON.toJSONString(rqt));
        List<OrderPush> orderPushes = orderPushListGateway.selectOrderPushList(trueProvinceNextId, rqt.getOrderId(),rqt.getLimitCount());
        if (CollectionUtils.isEmpty(orderPushes)) {
            return new ArrayList<>();
        }
        List<BatchOrderPushResp> orderPushResps = new ArrayList<>();
        orderPushes.forEach(it ->{
            BatchOrderPushResp batchOrderPushResp = BeanEnhanceUtil.copyBean(it, BatchOrderPushResp::new);
            orderPushResps.add(batchOrderPushResp);
        });
        return orderPushResps;
    }

    @Override
    public List<PushMasterListResp> getPushMasterList(GetPushMasterListRqt rqt) {

        List<PushMasterListResp> respList = new ArrayList<>();
        if(!"on".equals(getPushMasterListSwitch)){
            return respList;
        }
        OrderBase orderBase = commonOrderOfferService.getOrderBase(null, rqt.getGlobalOrderTraceId());
        if(Objects.isNull(orderBase)){
            return respList;
        }

        //现在业务没有存省下级地址id,业务传的是订单区域id
        List<Long> trueProvinceNextId = commonAddressService.getProvinceNextIdV2(orderBase.getThirdDivisionId(), "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.getPushMasterList", JSON.toJSONString(rqt));
        List<OrderPush> orderPushes = orderPushListGateway.selectPushMasterList(trueProvinceNextId, orderBase.getOrderId(),rqt.getDistance(),rqt.getPageNum(),rqt.getPageSize());
        if (CollectionUtils.isEmpty(orderPushes)) {
            return respList;
        }
        orderPushes.forEach(it ->{
            PushMasterListResp resp = new PushMasterListResp();
            resp.setMasterId(it.getMasterId());
            resp.setMasterLongitude(it.getMasterLongitude());
            resp.setMasterLatitude(it.getMasterLatitude());
            resp.setDistance(it.getPushDistance());
            resp.setIsViewOrder(Objects.nonNull(it.getFirstViewTime()) ? 1 : 0);
            respList.add(resp);
        });
        return respList;
    }



    @Override
    public List<BatchOrderPushResp> getFamilyOrderPushList(GetFamilyOrderPushListRqt rqt){

        if(!"on".equals(aiOrderPushListSwitch)){
            return new ArrayList<>();
        }
        //现在业务没有存省下级地址id,业务传的是订单区域id
        List<Long> trueProvinceNextId = commonAddressService.getProvinceNextIdV2(rqt.getProvinceNextId(), "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.getFamilyOrderPushList", JSON.toJSONString(rqt));
        trueProvinceNextId.remove(99999L);
        if(CollectionUtils.isEmpty(trueProvinceNextId)){
            return new ArrayList<>();
        }
        List<OrderPush> orderPushes = orderPushListGateway.selectFamilyOrderPushList(trueProvinceNextId, rqt.getMasterId(),rqt.getLimitCount());
        if (CollectionUtils.isEmpty(orderPushes)) {
            return new ArrayList<>();
        }
        List<BatchOrderPushResp> orderPushResps = new ArrayList<>();
        orderPushes.forEach(it ->{
            BatchOrderPushResp batchOrderPushResp = BeanEnhanceUtil.copyBean(it, BatchOrderPushResp::new);
            orderPushResps.add(batchOrderPushResp);
        });
        return orderPushResps;

    }






    @Override
    public List<BatchOrderPushResp> getPushedExperiencedMasterList(GetPushedExperiencedMasterListRqt rqt) {

        List<BatchOrderPushResp> respList = new ArrayList<>();
        if(!"on".equals(getPushedExperiencedMasterListSwitch)){
            return respList;
        }
        OrderBase orderBase = commonOrderOfferService.getOrderBase(null, rqt.getGlobalOrderTraceId());
        if(Objects.isNull(orderBase) || StringUtils.isBlank(orderBase.getServeIds())){
            return respList;
        }


        //现在业务没有存省下级地址id,业务传的是订单区域id
        List<Long> trueProvinceNextId = commonAddressService.getProvinceNextIdV2(orderBase.getThirdDivisionId(), "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.getPushMasterList", JSON.toJSONString(rqt));
        List<OrderPush> orderPushes = orderPushListGateway.selectOrderPushListByPushScore(trueProvinceNextId, orderBase.getOrderId(),1,getPushedExperiencedMasterListLimitCount);
        if (CollectionUtils.isEmpty(orderPushes)) {
            return respList;
        }

        List<String> masterSet = orderPushes.stream().map(OrderPush::getMasterId).map(String::valueOf).distinct().collect(Collectors.toList());


        String serveIds = orderBase.getServeIds();
        Set<Long> serveIdSet = Arrays.asList(serveIds.split(",")).stream().map(Long::valueOf).collect(Collectors.toSet());

        List<ServeBaseInfoResp> serveBaseInfoRespList = serveServiceApi.getServeBaseInfo(serveIdSet);

        if(CollectionUtils.isEmpty(serveBaseInfoRespList)){
            return respList;
        }

        List<Long> goodsSet = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getGoodsId).distinct().collect(Collectors.toList());


        List<Goods> goodsList = goodsServiceApi.queryBatch(goodsSet);

        if(CollectionUtils.isEmpty(goodsList)){
            return respList;
        }

        List<String> lv2GoodsIdList = goodsList.stream().map(Goods::getLevel2Id).distinct().map(String::valueOf).collect(Collectors.toList());


        GetMstServeCompleteInfoV3Rqt getMstServeCompleteInfoV3Rqt = new GetMstServeCompleteInfoV3Rqt();
        getMstServeCompleteInfoV3Rqt.setMasterId(String.join(",",masterSet));
        getMstServeCompleteInfoV3Rqt.setServeTypeId(String.valueOf(orderBase.getServeTypeId()));
        getMstServeCompleteInfoV3Rqt.setGoodsLevelOneId(String.valueOf(orderBase.getCategoryId()));
        getMstServeCompleteInfoV3Rqt.setGoodsLevelTwoId(String.join(",",lv2GoodsIdList));
        getMstServeCompleteInfoV3Rqt.setGoodsLevelThreeId("-1");
        List<GetMstServeCompleteInfoV3Resp> getMstServeCompleteInfoV3RespList = callGateway.catchLog(() -> bigdataOpenServiceApi.getMstServeCompleteInfoV3(getMstServeCompleteInfoV3Rqt), "getMstServeCompleteInfoV3",getMstServeCompleteInfoV3Rqt);


        if(CollectionUtils.isNotEmpty(getMstServeCompleteInfoV3RespList)){
            Set<Long> finalMasterSet = getMstServeCompleteInfoV3RespList.stream().filter(resp -> Objects.nonNull(resp.getServeCompleteCnt()) && resp.getServeCompleteCnt() > pushedExperiencedMasterServeCompleteCnt).
                    map(GetMstServeCompleteInfoV3Resp::getMasterId).collect(Collectors.toSet());

            if(CollectionUtils.isEmpty(finalMasterSet)){
                return respList;
            }

            orderPushes.forEach(it ->{
                if(finalMasterSet.contains(it.getMasterId())){
                    BatchOrderPushResp batchOrderPushResp = BeanEnhanceUtil.copyBean(it, BatchOrderPushResp::new);
                    respList.add(batchOrderPushResp);
                }

            });
        }
        return respList;
    }

    @Override
    public SiteOrderDetailPushMasterCountResp getPushMasterCountBySiteOrderDetail(SiteOrderDetailPushMasterCountReq rqt) {
        if (Objects.isNull(rqt)
                || Objects.isNull(rqt.getGlobalOrderId())
                || rqt.getGlobalOrderId() == 0L) {
            log.error("getPushMasterCountBySiteOrderDetail params error!globalOrderId is invalid!");
            return null;
        }
        Integer pushMasterCount = orderPushListGateway.selectFirstPushMasterCount(rqt.getGlobalOrderId());
        if (Objects.isNull(pushMasterCount)) {
            return null;
        }
        SiteOrderDetailPushMasterCountResp siteOrderDetailPushMasterCountResp = new SiteOrderDetailPushMasterCountResp();
        siteOrderDetailPushMasterCountResp.setPushMasterCount(pushMasterCount);

        return siteOrderDetailPushMasterCountResp;
    }

    @Override
    public SimplePageInfo<SiteOrderDetailMasterListResp> getSiteOrderDetailMasterList(SiteOrderDetailMasterListReq rqt) {
        if (!"on".equals(getSiteOrderDetailMasterListSwitch)) {
            return null;
        }
        Long globalOrderId = rqt.getGlobalOrderId();
        if (Objects.isNull(rqt.getGlobalOrderId()) || rqt.getGlobalOrderId() == 0L) {
            return null;
        }
        OrderBase orderBase = null;
        try {
            orderBase = commonOrderOfferService.getOrderBase(null, globalOrderId);

        } catch (Exception e) {
            log.error("getSiteOrderDetailMasterList commonOrderOfferService.getOrderBase error!globalOrderId:{}", globalOrderId);
            return null;
        }
        if(Objects.isNull(orderBase)){
            return null;
        }
        Long thirdDivisionId = orderBase.getThirdDivisionId();
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(thirdDivisionId,
                "com.wanshifu.domain.push.serviceimpl.OrderPushListServiceImpl.getSiteOrderDetailMasterList",
                thirdDivisionId.toString());
        Long orderId = orderBase.getOrderId();

        SimplePageInfo<SiteOrderDetailMasterListBo> sortCombinationsListMasterListPageInfo = orderPushListGateway
                .getSiteOrderDetailMasterSortCombinationsList(provinceNextIds, orderId,
                        thirdDivisionId,
                        orderBase.getServeIds(),
                        orderBase.getAccountId(),
                        rqt.getPageNum(), 6);

        SimplePageInfo<SiteOrderDetailMasterListResp> pageInfo = new SimplePageInfo<>();

        if (Objects.isNull(sortCombinationsListMasterListPageInfo) || CollectionUtils.isEmpty(sortCombinationsListMasterListPageInfo.getList())) {
            return pageInfo;
        }

        List<SiteOrderDetailMasterListResp> siteOrderDetailMasterListRespList = sortCombinationsListMasterListPageInfo.getList().stream().map(DTOFormatAbilityService::getSiteOrderDetailMasterListResp).collect(Collectors.toList());
        pageInfo.setList(siteOrderDetailMasterListRespList);
        pageInfo.setTotal(sortCombinationsListMasterListPageInfo.getTotal());
        pageInfo.setPageNum(sortCombinationsListMasterListPageInfo.getPageNum());
        pageInfo.setPageSize(sortCombinationsListMasterListPageInfo.getPageSize());
        pageInfo.setPages(sortCombinationsListMasterListPageInfo.getPages());
        return pageInfo;
    }

}
