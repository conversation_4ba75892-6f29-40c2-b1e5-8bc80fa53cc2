package com.wanshifu.domain.push;

import com.wanshifu.domain.push.model.*;
import com.wanshifu.order.push.request.push.*;
import com.wanshifu.order.push.response.push.BatchUpdatePushDistanceResp;
import com.wanshifu.order.push.response.push.PushedToMasterResp;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/28 18:00
 */
public interface OrderPushOperateService {

    /**
     * 订单推送给师傅（再次推单，手动推单）
     * @param pushOrderRqt
     * @return
     */
    PushedToMasterResp pushedToMaster(PushedToMasterRqt pushOrderRqt);

    /**
     * 订单推送给师傅（智能推单、直接指派推单、直接推单）
     * @return
     */
    PushedToMasterResp pushedToMaster(OrderPushRqt orderPushRqt);


    /**
     * 通用的推单给师傅（包含手动推单、mq推单）
     *
     * @param
     * @return
     */
    public PushedToMasterResp commonPushedToMaster(List<Long> provinceNextId, PushedToMasterRqt pushedToMasterRqt,
                                                   OrderPushCompositeRqt orderPushRqt);


    /**
     * 信息订单推送给师傅（智能推单-mq消费）
     * @param rqt
     * @return
     */
    InnovateBusinessPushResp innovateBusinessPush(OrderPushRqt rqt);

    /**
     * 师傅更新服务地区服务类型重新计算推单
     * @param resendOrderPushRqt
     * @return
     */
    int resendOrderPushByMaster(ResendOrderPushRqt resendOrderPushRqt);

    /**
     * 清理原表过期的推送记录
     * @param clearExpiredPushRqt
     * @return
     */
    Integer clearExpiredOrderPush(ClearExpiredPushRqt clearExpiredPushRqt);

    /**
     * 清理分表过期的推送记录
     * 分表清理
     * @param clearExpiredPushRqt
     */
    void clearExpiredOrderPushV2(ClearExpiredPushRqt clearExpiredPushRqt);

    /**
     * 批量更新师傅推单距离和经纬度
     * @param batchUpdatePushDistanceRqt
     * @return
     */
    @Deprecated
    BatchUpdatePushDistanceResp batchUpdatePushDistance(BatchUpdatePushDistanceRqt batchUpdatePushDistanceRqt);

    /**
     * 批量更新师傅推单距离和经纬度
     * 待报价列表数据架构升级项目V2版本，去除根据主键pushId操作逻辑，增加省下级地址id参数传递
     * @param batchUpdatePushDistanceV2Rqt
     * @return
     */
    BatchUpdatePushDistanceResp batchUpdatePushDistanceV2(BatchUpdatePushDistanceV2Rqt batchUpdatePushDistanceV2Rqt);

    /**
     * 手动删除订单推送记录
     * @param orderId
     * @param masterId
     * @return
     */
    @Deprecated
    int manualClearPush(Long orderId, Long masterId);

    /**
     * 修改推单记录菜单类别
     * @param updateOrderPushMenuCategoryRqt
     * @return
     */
    Integer updateOrderPushMenuCategory(UpdateOrderPushMenuCategoryRqt updateOrderPushMenuCategoryRqt);

    /**
     * 更新推送竞争少订单
     * @param pushLessContendOrderRqt
     */
    void pushLessContendOrder(PushLessContendOrderRqt pushLessContendOrderRqt);

    /**
     * 清除orderPush记录
     * @param clearOrderPushRqt
     * @return
     */
    Integer clearOrderPush(ClearOrderPushRqt clearOrderPushRqt);

    /**
     * 更新orderPush的offerTime
     * @param updateOrderPushOfferTimeRqt
     * @return
     */
    Integer updateOrderPushOfferTime(UpdateOrderPushOfferTimeRqt updateOrderPushOfferTimeRqt);

    /**
     * 更新orderPush的firstViewTime
     * @param updateFirstViewTimeRqt
     * @return
     */
    Integer updateOrderPushFirstViewTime(UpdateFirstViewTimeRqt updateFirstViewTimeRqt);

    /**
     * 更新推单记录的 订单距离拉取状态
     * @param updatePullOrderDistanceRqt
     * @return
     */
    Integer updatePullOrderDistanceByPushId(UpdatePullOrderDistanceRqt updatePullOrderDistanceRqt);

    /**
     * 更新推单记录的 订单距离拉取状态
     * @param updatePullOrderDistanceRqt
     * @return
     */
    Integer updatePullOrderDistanceByPushIdV2(UpdatePullOrderDistanceRqtV2 updatePullOrderDistanceRqt);

    /**
     * 更新物流到货状态--用于排序
     * @param updateIsArrivedRqt
     * @return
     */
    Integer updateIsArrivedByOrderId(UpdateIsArrivedRqt updateIsArrivedRqt);


    /**
     * 更新orderPush的pullViewTime
     * @param updatePullViewTimeRqt
     * @return
     */
    Integer updatePullViewTime(UpdatePullViewTimeRqt updatePullViewTimeRqt);

    /**
     * 查看订单详情后续逻辑
     * @param afterOrderDetailRqt
     */
    void afterOrderDetail(AfterOrderDetailRqt afterOrderDetailRqt);

}
