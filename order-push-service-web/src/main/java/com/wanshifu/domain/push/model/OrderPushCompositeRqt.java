package com.wanshifu.domain.push.model;

import com.wanshifu.master.order.domains.po.ServeStop;
import com.wanshifu.order.offer.domains.api.response.offer.PushingOrderCompositeResp;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-03-06 15:07
 * @Description
 * @Version v1
 **/
@Data
public class OrderPushCompositeRqt extends OrderPushRqt{
    /**
     * 订单聚合信息
     */
    private PushingOrderCompositeResp orderComposite;
    /**
     * 订单服务停止信息
     */
    private List<ServeStop> serveStops;
    /**
     * 技能类型
     */
    private String techniqueTypeIds;

}
