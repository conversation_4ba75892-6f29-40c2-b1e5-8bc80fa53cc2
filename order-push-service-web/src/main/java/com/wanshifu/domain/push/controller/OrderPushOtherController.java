package com.wanshifu.domain.push.controller;

import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.order.push.api.OrderPushOtherApi;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/1 10:54
 */
@RestController
@RequestMapping("/orderPush/other")
public class OrderPushOtherController implements OrderPushOtherApi {

    @Resource
    private ApolloSwitchUtils apolloSwitchUtils;

    /**
     * 获取下沉开关
     * @return
     */
    @PostMapping("/getSinkIterationSwitch")
    @Override
    public Boolean getSinkIterationSwitch() {
        return apolloSwitchUtils.isOpenIterationSwitch();
    }

    /**
     * 获取推单结果下沉控制开关
     * @param thirdDivisionId
     * @return
     */
    @Override
    @PostMapping("getPushDockingHandoffTag")
    public String getPushDockingHandoffTag(@RequestParam("thirdDivisionId")  Long  thirdDivisionId) {
        return apolloSwitchUtils.getPushDockingHandoffTag(thirdDivisionId);
    }
}
