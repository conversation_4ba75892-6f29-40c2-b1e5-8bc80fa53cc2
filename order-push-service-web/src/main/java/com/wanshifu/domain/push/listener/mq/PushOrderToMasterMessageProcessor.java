package com.wanshifu.domain.push.listener.mq;

import com.alibaba.fastjson.JSON;
import com.wanshifu.domain.base.handler.AbstractMatchMasterTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.push.OrderPushOperateService;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.model.OrderPushRqt;
import com.wanshifu.domain.push.model.PushOrderToMasterRqt;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.order.push.request.push.MasterAddressInfo;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-03-06 11:21
 * @Description 匹配结果消息(推单结果通知）
 * @Version v1
 **/
@ConsumeTag(ConsumeTagEnum.PUSH_ORDERS_TO_MASTER)
@Slf4j
public class PushOrderToMasterMessageProcessor extends AbstractMatchMasterTopicMessageTag<PushOrderToMasterRqt> {

    @Resource
    protected OrderPushOperateService orderPushOperateService;

    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;

    @Resource
    private CommonAddressService commonAddressService;


    @Override
    public void postHandler(PushOrderToMasterRqt pushOrderToMasterRqt) {

        log.info("PushOrderToMasterMessageProcessor message : " + JSON.toJSONString(pushOrderToMasterRqt));

        Long masterId = pushOrderToMasterRqt.getMasterId();
        List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(pushOrderToMasterRqt.getCityDivisionId(), null, null);
        List<Long> orderIdList = pushOrderToMasterRqt.getPushOrderDetailInfoList().stream().map(PushOrderToMasterRqt.PushOrderDetailInfo::getOrderId).collect(Collectors.toList());
        List<OrderPush> orderPushList = orderPushManagerGateway.selectByMasterIdAndOrderIds(provinceNextIds,masterId,orderIdList);

        List<PushOrderToMasterRqt.PushOrderDetailInfo> pushOrderDetailInfoList;
        if(CollectionUtils.isNotEmpty(orderPushList)){
            List<Long> pushedOrderIdList = orderPushList.stream().map(OrderPush::getOrderId).collect(Collectors.toList());
            pushOrderDetailInfoList = pushOrderToMasterRqt.getPushOrderDetailInfoList().stream().filter(pushOrderDetailInfo ->
                    (!pushedOrderIdList.contains(pushOrderDetailInfo.getOrderId()))).collect(Collectors.toList());
        }else{
            pushOrderDetailInfoList = pushOrderToMasterRqt.getPushOrderDetailInfoList();
        }


        if(CollectionUtils.isEmpty(pushOrderDetailInfoList)){
            return ;
        }


        log.info("PushOrderToMasterMessageProcessor size : " + pushOrderDetailInfoList.size());


        pushOrderDetailInfoList.forEach(pushOrderDetailInfo -> {
            OrderPushRqt orderPushRqt = new OrderPushRqt();
            orderPushRqt.setOrderId(pushOrderDetailInfo.getOrderId());
            orderPushRqt.setGlobalOrderTraceId(pushOrderDetailInfo.getGlobalOrderTraceId());
            orderPushRqt.setPushScenarioType(pushOrderToMasterRqt.getPushScenarioType());
            orderPushRqt.setBusinessLineId(pushOrderDetailInfo.getBusinessLineId());
            orderPushRqt.setPushMode(pushOrderToMasterRqt.getPushMode());
            MasterAddressInfo masterAddressInfo = new MasterAddressInfo();
            masterAddressInfo.setMasterId(pushOrderToMasterRqt.getMasterId());
            masterAddressInfo.setMasterLatitude(pushOrderToMasterRqt.getMasterLatitude());
            masterAddressInfo.setMasterLongitude(pushOrderToMasterRqt.getMasterLongitude());
            masterAddressInfo.setPushDistance(pushOrderDetailInfo.getPushDistance());
            masterAddressInfo.setPushDistanceType(pushOrderDetailInfo.getPushDistanceType());
            masterAddressInfo.setAccordingTechnologyPushFlag(pushOrderDetailInfo.getAccordingTechnologyPushFlag());
            masterAddressInfo.setCrossCityPush(pushOrderDetailInfo.getCrossCityPush());
            masterAddressInfo.setMasterSourceType(pushOrderToMasterRqt.getMasterSourceType());
            orderPushRqt.setMasterAddressInfoList(Collections.singletonList(masterAddressInfo));
            orderPushRqt.setFirstPush(pushOrderDetailInfo.getFirstPush());
            orderPushRqt.setFirstTimeValidPush(pushOrderDetailInfo.getFirstTimeValidPush());
            orderPushRqt.setPushDivisionLevel(pushOrderDetailInfo.getPushDivisionLevel());
            orderPushRqt.setPushFlag(pushOrderToMasterRqt.getPushFlag());
            orderPushRqt.setAccordingDistancePushFlag(pushOrderToMasterRqt.getAccordingDistancePushFlag());
            orderPushRqt.setMasterSourceType(pushOrderToMasterRqt.getMasterSourceType());
            orderPushRqt.setMatchSceneCode(pushOrderToMasterRqt.getMatchSceneCode());
            orderPushOperateService.pushedToMaster(orderPushRqt);
        });


    }


}
