package com.wanshifu.domain.push.gateway;

import com.wanshifu.domain.push.model.*;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.infrastructure.OrderPush;
import com.wanshifu.infrastructure.gatewayreq.push.MasterAppletListOrderPushGatewayRqt;
import com.wanshifu.order.offer.domains.api.response.OrderInfoBatchComposite;
import com.wanshifu.order.offer.domains.api.response.infoorder.InfoOrderBaseComposite;
import com.wanshifu.order.push.request.WaitOfferSpecialListRqt;
import com.wanshifu.order.push.request.push.TmplCityOrderPushRqt;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/22 18:44
 */
public interface OrderPushListGateway {

    /**
     * 师傅待报价订单列表--特殊列表
     *
     * @param rqt
     * @return
     */
    SimplePageInfo<WaitOfferV2RespBo> waitOfferSpecialList(List<Long> provinceNextIds, WaitOfferSpecialListRqt rqt);


    /**
     * 师傅待报价订单列表V2
     *
     * @param waitOfferRqt
     * @return
     */
    SimplePageInfo<WaitOfferV2RespBo> waitOfferV2(WaitOfferToV2 waitOfferRqt, Long cityId,
                                                  Integer pageNumber, Integer pageSize);

    /**
     * 家庭模板城市订单待报价列表
     * @param rqt TmplCityOrderPushRqt
     * @return
     */
    SimplePageInfo<OrderPush> tmplCityOrderPushList(TmplCityOrderPushRqt rqt);


    /**
     * 组装信息订单列表数据
     * @param orderPush
     * @param infoOrderBaseCompositeList
     * @return
     */
    public WaitOfferV2RespBo setReceivingListV2Resp(OrderPush orderPush, List<InfoOrderBaseComposite> infoOrderBaseCompositeList);


    /**
     * 组装非信息订单数据
     *
     * @param provinceNextIds 省下级地址id
     * @param orderPush
     * @param orderInfoBatchComposite
     * @param cityId
     * @return
     */
    public WaitOfferV2RespBo setWaitOfferV2Resp(List<Long> provinceNextIds, OrderPush orderPush, OrderInfoBatchComposite orderInfoBatchComposite, Long cityId);
    /**
     * 待报价列表，不分页
     *
     * @param waitOfferNoPageBo
     * @return
     */
    List<WaitOfferNoPageRespBo> waitOfferNoPage(WaitOfferNoPageBo waitOfferNoPageBo);

    /**
     * 查询师傅的推单记录信息
     *
     * @param provinceNextIds 省下级地址id
     * @param masterId 师傅id
     * @param limit 查询条数限制
     * @return
     */
    List<OrderPush> selectOrderPushMaxLimit300(List<Long> provinceNextIds, Long masterId, Integer limit);

    /**
     * 获取订单推单消息（master-notice-service调用）
     *
     * @param provinceNextIds 省下级地址id
     * @param orderId 订单id
     * @return
     */
    List<OrderPush> getOrderPushForNotice(List<Long> provinceNextIds, Long orderId);

    /**
     * 获取订单推单列表（master-order-distribute-service调用）
     *
     * @param provinceNextId 省下级地址id
     * @param orderId 订单id
     * @return
     */
    List<OrderPush> selectOrderPushByOrderId(List<Long> provinceNextId, Long orderId);

    /**
     * 师傅小程序游客模式获取推单数据
     * @param rqt MasterAppletListOrderPushGatewayRqt
     * @return List<OrderPush>
     */
    List<OrderPush> listByMasterDivisionIdForAppletUnLogin(MasterAppletListOrderPushGatewayRqt rqt);


    List<OrderPush> selectMasterOrderPushList(WaitOfferNoPageBo waitOfferNoPageBo);

    List<OrderPush> selectMasterOrderPushByAccount(List<Long> provinceNextId, Long masterId,Long accountId,String accountType);

    /**
     * 根据orderId和订单是否有报价状态查询
     *
     * @param provinceNextId 省下级地址id
     * @param orderId
     * @param offer
     * @return
     */
    OrderPush getOrderPushByOrderIdAndOffer(List<Long> provinceNextId, Long orderId, Integer offer);


    List<OrderPush> selectOrderPushList(List<Long> provinceNextId, Long orderId,Integer limitCount);

    List<OrderPush> selectFamilyOrderPushList(List<Long> provinceNextId, Long masterId,Integer limitCount);

    List<OrderPush> selectPushMasterList(List<Long> provinceNextId, Long orderId,Integer distance,Integer pageNum,Integer pageSize);


    List<OrderPush> selectOrderPushListByPushScore(List<Long> provinceNextId, Long orderId,Integer pageNum,Integer pageSize);

    /**
     * 查询普通推单首次有效师傅推送匹配的可推单师傅数
     * @param globalOrderId
     * @return
     */
    Integer selectFirstPushMasterCount(Long globalOrderId);

    /**
     * 网站订单详情获取附近推单师傅,不同标签师傅排序组合列表
     * @param provinceNextId
     * @param orderId
     * @param pageNum
     * @param pageSize
     * @return
     */
    SimplePageInfo<SiteOrderDetailMasterListBo> getSiteOrderDetailMasterSortCombinationsList(List<Long> provinceNextId,
                                                                                             Long orderId,
                                                                                             Long thirdDivisionId,
                                                                                             String serveIds,
                                                                                             Long userId,
                                                                                             Integer pageNum, Integer pageSize);
}
