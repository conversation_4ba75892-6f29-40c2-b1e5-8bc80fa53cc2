package com.wanshifu.domain.push.listener.mq;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.wanshifu.domain.base.handler.AbstractMatchMasterTopicMessageTag;
import com.wanshifu.domain.base.handler.AbstractOrderOfferTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.listener.mq.message.EnterpriseDirectAppointMessage;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.enterprise.CommonEnterpriseOrderService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.tmplcity.TmplCityOperateService;
import com.wanshifu.domain.tmplcity.listener.mq.message.RecommendMatchResultMessage;
import com.wanshifu.enterprise.order.domain.infoQuery.api.response.GetOrderBaseByGlobalIdRsp;
import com.wanshifu.enterprise.order.domain.po.OrderBase;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.push.request.push.MasterAddressInfo;
import com.wanshifu.order.push.request.tmplcity.SaveTmplCityOrderRecommendMasterRqt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 推单的推荐师傅通知,家庭样板城市项目后台调度手动指派师傅功能新加
 * @date 2024/6/25 11:16
 */
@ConsumeTag(value = ConsumeTagEnum.ENTERPRIRSE_DIRECT_APPOINT, maxReconsumeTime = 6)
@Slf4j
public class EnterpriseDirectAppointMasterMessageProcessor extends AbstractOrderOfferTopicMessageTag<EnterpriseDirectAppointMessage> {

    @Resource
    private CommonEnterpriseOrderService commonEnterpriseOrderService;

    @Resource
    private CommonAddressService commonAddressService;


    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;


    @Override
    public void postHandler(EnterpriseDirectAppointMessage enterpriseDirectAppointMessage) {
        log.info("enterpriseDirectAppoint,params:{}", JSONUtil.toJsonStr(enterpriseDirectAppointMessage));
        Long globalOrderTraceId = enterpriseDirectAppointMessage.getGlobalOrderTraceId();
        Long masterId = enterpriseDirectAppointMessage.getMasterId();

        if (Objects.isNull(globalOrderTraceId) || globalOrderTraceId == 0L) {
            log.error("enterpriseDirectAppoint error,globalOrderTraceId is null or 0L!");
            return;
        }

        List<String> orderLabels = enterpriseDirectAppointMessage.getOrderLabels();
       if(CollectionUtils.isNotEmpty(orderLabels) && orderLabels.contains("skill_task_order_grab")){
           GetOrderBaseByGlobalIdRsp getOrderBaseByGlobalIdRsp = commonEnterpriseOrderService.getOrderBase(enterpriseDirectAppointMessage.getGlobalOrderTraceId());
           if(Objects.isNull(getOrderBaseByGlobalIdRsp) || Objects.isNull(getOrderBaseByGlobalIdRsp.getOrderBase())){
               return ;
           }

           OrderBase orderBase = getOrderBaseByGlobalIdRsp.getOrderBase();

           Long categoryId = Long.valueOf(orderBase.getCategoryId());

           List<Long> provinceNextIds = commonAddressService.getProvinceNextIdV2(orderBase.getThirdDivisionId(), null, null);

           orderPushManagerGateway.clearTechniqueVerifyOrderForGrabDefinite(provinceNextIds,masterId,categoryId);
       }
    }
}
