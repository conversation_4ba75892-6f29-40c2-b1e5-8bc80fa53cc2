package com.wanshifu.domain.push.model;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.domains.po.ServeStop;
import com.wanshifu.order.offer.domains.api.response.offer.PushingOrderCompositeResp;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.push.request.push.PushedToMasterRqt;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-09-20 17:39
 * @Description
 * @Version v1
 **/
@Data
public class PushedToMasterGatewayRqt extends PushedToMasterRqt {

    /**
     * 推单的聚合信息
     */
    private PushingOrderCompositeResp orderComposite;

    /**
     * 订单服务停止信息
     */
    private List<ServeStop> serveStops;

    /**
     * 无人报价时间配置(不是null，就代表是代理商推单，需要记录到代理商推单表)
     */
    private Integer nobodyOfferHour;

    /**
     * 代理商信息
     */
    private List<AgentPushMaster> agentPushMasterList;

    /**
     * 多个用英文逗号隔开
     * 技能类型集合,1:清洁/保养/治理/美缝,4:配送并安装,5:维修服务,6:家装施工,8:安装服务,10:测量服务,13:定制家具/门类/测量/安装,16:管道疏通,17:搬运服务,18:拆旧服务,19:房屋维修
     */
    private String techniqueTypeIds;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 专属订单标记:101:系统报价,102:抢单报价单,103成品报价,104家庭,105总包,106系统报价转抢单报价单
     */
    private Integer exclusiveFlag = 0;

    /**
     * 代理商标签
     */
    private Integer agentOrderFlag = 0;

}
