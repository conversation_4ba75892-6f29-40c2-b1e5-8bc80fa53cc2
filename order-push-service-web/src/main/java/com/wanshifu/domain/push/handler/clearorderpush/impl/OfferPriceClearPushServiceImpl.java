package com.wanshifu.domain.push.handler.clearorderpush.impl;

import com.wanshifu.domain.push.context.ClearPushStrategyContext;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.handler.clearorderpush.OrderPushClearService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.order.push.enums.OrderPushClearType;
import com.wanshifu.order.push.request.push.ClearOrderPushRqt;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/4 18:55
 */
@Component
public class OfferPriceClearPushServiceImpl implements OrderPushClearService, InitializingBean {

    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;

    @Override
    public int clear(ClearOrderPushRqt clearOrderPushRqt) {
        List<String> businessParamList = clearOrderPushRqt.getBusinessParams();
        if(CollectionUtils.isNotEmpty(businessParamList) && businessParamList.contains(OrderPushClearType.LAST_OFFER_PRICE.type)){
            orderPushManagerGateway.lastOfferPrice(clearOrderPushRqt.getProvinceNextIds(), clearOrderPushRqt.getOrderId(), clearOrderPushRqt.getAppendNote());
        }else{
            orderPushManagerGateway.offerPrice(clearOrderPushRqt.getProvinceNextIds(), clearOrderPushRqt.getOrderId(), clearOrderPushRqt.getMasterId(), clearOrderPushRqt.getOfferTime(), clearOrderPushRqt.getAppendNote());

        }


        return 1;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ClearPushStrategyContext.register(OrderPushClearType.OFFER_PRICE, this);
    }
}
