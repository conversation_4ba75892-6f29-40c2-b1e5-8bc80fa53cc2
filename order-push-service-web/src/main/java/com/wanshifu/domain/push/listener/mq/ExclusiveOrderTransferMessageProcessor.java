package com.wanshifu.domain.push.listener.mq;


import cn.hutool.json.JSONUtil;
import com.wanshifu.domain.base.handler.AbstractOfferPlatformTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.push.model.ExclusiveOrderTransferMessage;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.infrastructure.repository.OrderPushRepository;
import com.wanshifu.order.offer.domains.po.OrderBase;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2023-08-25 17:29
 * @Description 专属订单转单
 * @Version v1
 **/

@ConsumeTag(value = ConsumeTagEnum.EXCLUSIVE_ORDER_TRANSFER)
@Slf4j
public class ExclusiveOrderTransferMessageProcessor extends AbstractOfferPlatformTopicMessageTag<ExclusiveOrderTransferMessage> {
    @Resource
    private OrderPushRepository orderPushRepository;
    @Resource
    private CommonOrderOfferService commonOrderOfferService;
    @Resource
    private CommonAddressService commonAddressService;


    @Override
    public void postHandler(ExclusiveOrderTransferMessage exclusiveOrderTransferMessage) {
        OrderBase orderBase = commonOrderOfferService.getOrderBase(null, exclusiveOrderTransferMessage.getGlobalOrderTraceId());

        if(Objects.isNull(orderBase)){
            return;
        }
        Long thirdDivisionId = orderBase.getThirdDivisionId();
        if (Objects.isNull(thirdDivisionId) || thirdDivisionId == 0L) {
            return;
        }

        if(Objects.nonNull(exclusiveOrderTransferMessage.getIsUpdateExclusiveFlag())
                && exclusiveOrderTransferMessage.getIsUpdateExclusiveFlag()
                && Objects.nonNull(exclusiveOrderTransferMessage.getExclusiveFlag())){
            //计算省下级地址id
            List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(thirdDivisionId,
                    "wanshifu_order_offer_service_common_order_topic.exclusive_order_transfer",
                    JSONUtil.toJsonStr(exclusiveOrderTransferMessage));
            orderPushRepository.updateOrderExclusiveFlagByOrderId(provinceNextId, orderBase.getOrderId(), exclusiveOrderTransferMessage.getExclusiveFlag());
        }

    }
}
