package com.wanshifu.domain.push.listener.mq;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.domain.base.handler.AbstractMasterOrderApiCommonTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.base.model.PushProducerTagEnum;
import com.wanshifu.domain.base.tools.TopicHelper;
import com.wanshifu.domain.push.gateway.OrderPushOperateGateway;
import com.wanshifu.domain.push.listener.mq.message.MasterBountyMessage;
import com.wanshifu.domain.push.model.OrderPutTagDTO;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 师傅悬赏逻辑处理
 * @date 2024/10/30 16:05
 */
@ConsumeTag(value = ConsumeTagEnum.MASTER_BOUNTY)
@Slf4j
public class MasterBountyMessageProcessor extends AbstractMasterOrderApiCommonTopicMessageTag<MasterBountyMessage> {


    @Resource
    private OrderPushOperateGateway orderPushOperateGateway;

    @Resource
    private CommonAddressService commonAddressService;

    @Resource
    private MessageSenderService mqSendGateway;

    @Override
    public void postHandler(MasterBountyMessage masterBountyMessage) {
        log.info("masterBountyMessage:{}", JSONUtil.toJsonStr(masterBountyMessage));
        if (Objects.isNull(masterBountyMessage)
                || Objects.isNull(masterBountyMessage.getMasterOrderId())
                || Objects.isNull(masterBountyMessage.getGlobalOrderTraceId())) {
            return;
        }
        Long thirdDivisionId = masterBountyMessage.getThirdDivisionId();
        if (Objects.isNull(thirdDivisionId) || thirdDivisionId == 0L) {
            return;
        }

        //计算省下级地址id
        List<Long> provinceNextId = commonAddressService.getProvinceNextIdV2(thirdDivisionId,
                "master_order_api_common_offer_topic.master_bounty",
                JSONUtil.toJsonStr(masterBountyMessage));

        if(masterBountyMessage.getEventType() == 1){
            //标记进入悬赏分区
            orderPushOperateGateway.updateMenuCategoryBatch(provinceNextId, masterBountyMessage.getMasterOrderId(), 2);
        }else if(masterBountyMessage.getEventType() == 2){
            //订单打上悬赏标签
            putOrderTag(masterBountyMessage.getGlobalOrderTraceId(), masterBountyMessage.getMasterOrderId());
        }


    }


    /**
     * 悬赏订单打标签
     * @param globalOrderTraceId
     * @param orderId
     */
    private void putOrderTag(Long globalOrderTraceId, Long orderId){
        OrderPutTagDTO rqt = new OrderPutTagDTO();
        rqt.setGlobalOrderTraceId(globalOrderTraceId);
        rqt.setOrderId(orderId);
        rqt.setDimension("order");
        rqt.setTagType("bounty_order");
        rqt.setTagName("悬赏");
        rqt.setIsDynamicTag(0);
        mqSendGateway.sendSyncMessage(TopicHelper.ORDER_PUSH_NOTICE_TOPIC, PushProducerTagEnum.ORDER_TAG.tag, JSON.toJSONString(rqt),true);
    }
}
