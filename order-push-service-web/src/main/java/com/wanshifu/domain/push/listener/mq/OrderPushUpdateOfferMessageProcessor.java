package com.wanshifu.domain.push.listener.mq;

import com.wanshifu.domain.base.handler.AbstractNormalBusinessTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.push.gateway.OrderPushListGateway;
import com.wanshifu.domain.push.gateway.OrderPushOperateGateway;
import com.wanshifu.domain.push.listener.mq.message.OrderPushUpdateOfferMessage;
import com.wanshifu.infrastructure.OrderPush;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/17 16:28
 */
@ConsumeTag(value = ConsumeTagEnum.ORDER_PUSH_UPDATE_OFFER, isSupportLog = false, maxReconsumeTime = 3)
@Slf4j
public class OrderPushUpdateOfferMessageProcessor extends AbstractNormalBusinessTopicMessageTag<OrderPushUpdateOfferMessage> {

    @Resource
    private OrderPushOperateGateway orderPushOperateGateway;

    @Resource
    private OrderPushListGateway orderPushListGateway;

    @Resource
    private ApolloSwitchUtils apolloSwitchUtils;

    @Override
    public void postHandler(OrderPushUpdateOfferMessage message) {
        log.info("OrderPushUpdateOfferMessageProcessor.postHandler:{}", message);
        List<Long> provinceNextId = message.getProvinceNextId();
        if (apolloSwitchUtils.isOuterCitySwitch(provinceNextId.get(0))) {
            return;
        }

        Long orderId = message.getOrderId();
        OrderPush orderPush = orderPushListGateway.getOrderPushByOrderIdAndOffer(provinceNextId, orderId, 1);
        if (Objects.isNull(orderPush)) {
            //首次报价更新
            orderPushOperateGateway.updateOfferBatch(provinceNextId, orderId);
        }
    }
}
