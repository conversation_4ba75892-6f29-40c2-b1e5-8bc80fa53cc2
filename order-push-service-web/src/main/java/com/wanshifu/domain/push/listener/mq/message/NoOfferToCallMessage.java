package com.wanshifu.domain.push.listener.mq.message;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 14:40
 */
@Data
public class NoOfferToCallMessage {

    /**
     * 订单id
     */
    @NotNull
    private Long masterOrderId;

    /**
     * 全局订单id
     */
    @NotNull
    private Long globalOrderTraceId;

    /**
     * 师傅ids
     */
    @NotEmpty
    private List<Long> masterIds;

    /**
     * 订单3级地址
     */
    @NotNull
    private Long thirdDivisionId;
}
