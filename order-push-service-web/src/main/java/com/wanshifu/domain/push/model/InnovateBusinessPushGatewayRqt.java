package com.wanshifu.domain.push.model;

import com.wanshifu.order.offer.domains.po.InfoOrderBase;
import com.wanshifu.order.push.request.push.MasterAddressInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2021/01/10
 */
@Data
public class InnovateBusinessPushGatewayRqt {

    /**
     * 师傅ID集合
     */
    @NotNull
    @Size(min = 1, max = 100)
    private List<Long> masterIdList;

    /**
     * 订单ID
     */
    @NotNull
    private Long orderId;


    /**
     * 推送时间
     */
    @NotNull
    private Date pushTime;

    /**
     * 多个用英文逗号隔开
     * 技能类型集合,1:清洁/保养/治理/美缝,4:配送并安装,5:维修服务,6:家装施工,8:安装服务,10:测量服务,13:定制家具/门类/测量/安装,16:管道疏通,17:搬运服务,18:拆旧服务,19:房屋维修
     */
    @NotEmpty
    private String techniqueTypeIds;

    /**
     * 推单标识 0：正常推单 1：附近推单
     */
    private Integer pushFlag = 0;

    /**
     * 信息订单信息
     */
    private InfoOrderBase infoOrderBase;

    /**
     * 省下级地址id
     */
    private Long provinceNextId;

    private List<MasterAddressInfo> masterAddressInfoList;

    /**
     * 师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    private String masterSourceType;
}
