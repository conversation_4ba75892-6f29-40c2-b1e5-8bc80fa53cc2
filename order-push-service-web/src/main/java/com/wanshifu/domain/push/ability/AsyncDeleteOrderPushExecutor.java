package com.wanshifu.domain.push.ability;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/21 11:09
 */
@Component
@Slf4j
public class AsyncDeleteOrderPushExecutor {

    @Resource(name = "retryDeleteOrderPushExecutor")
    private ScheduledThreadPoolExecutor scheduler;

    /**
     * 单位：秒
     */
    private final int[] delayTimes = {10, 60, 300};

    public void executeWithRetry(Long orderId, Supplier<Integer> task) {
        scheduleTask(orderId, task, 0, 0);
    }

    private void scheduleTask(Long orderId, Supplier<Integer> task, int retryCount, int delaySeconds) {
        scheduler.schedule(() -> attemptTask(orderId, task, retryCount), delaySeconds, TimeUnit.SECONDS);
    }

    private void attemptTask(Long orderId, Supplier<Integer> task, int retryCount) {
        try {
            task.get();
        } catch (Exception e) {
            log.error("asyncDeleteOrderPush maybeHappenedDeadLockException!, orderId:{},executing {} times.ex:", orderId, retryCount, e);
            if (retryCount >= delayTimes.length) {
                log.warn("asyncDeleteOrderPush max retries reached, orderId:{}", orderId);
            } else {
                log.error("asyncDeleteOrderPush asyncDeleteOrderPushExecutorTaskFailed, orderId:{},retrying in {} seconds.", orderId, delayTimes[retryCount]);
                scheduleTask(orderId, task, retryCount + 1, delayTimes[retryCount]);
            }
        }


    }
}
