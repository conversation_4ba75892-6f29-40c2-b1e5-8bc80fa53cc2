package com.wanshifu.domain.push.handler.clearorderpush.impl;

import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.push.context.ClearPushStrategyContext;
import com.wanshifu.domain.push.gateway.OrderPushManagerGateway;
import com.wanshifu.domain.push.handler.clearorderpush.OrderPushClearService;
import com.wanshifu.order.push.enums.OrderPushClearType;
import com.wanshifu.order.push.request.push.ClearOrderPushRqt;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/4 18:55
 */
@Component
public class LastOfferPriceClearPushServiceImpl implements OrderPushClearService, InitializingBean {

    @Resource
    private OrderPushManagerGateway orderPushManagerGateway;

    @Override
    public int clear(ClearOrderPushRqt clearOrderPushRqt) {
        orderPushManagerGateway.lastOfferPrice(clearOrderPushRqt.getProvinceNextIds(), clearOrderPushRqt.getOrderId(), clearOrderPushRqt.getAppendNote());
        return 1;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ClearPushStrategyContext.register(OrderPushClearType.LAST_OFFER_PRICE, this);
    }
}
