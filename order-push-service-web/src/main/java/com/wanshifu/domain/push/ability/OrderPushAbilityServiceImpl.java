package com.wanshifu.domain.push.ability;

import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 16:39
 */
@Service
public class OrderPushAbilityServiceImpl implements OrderPushAbilityService{

    @Value("${order.push.docking.cityList:old}")
    private String dockingCityList;

    @Resource
    private CallGateway callGateway;

    @Resource
    private CommonAddressService commonAddressService;


    @Override
    public String getPushDockingHandoffTag(Long thirdDivisionId) {
        //配置 all 则意味全部城市开放接入新系统
        if (StringUtils.isEmpty(dockingCityList)) {
            return "old";
        }
        if (dockingCityList.equals("old")) {
            return "old";
        }
        if (dockingCityList.equals("all") || dockingCityList.contains("all")) {
            return "new";
        }

        if (Objects.nonNull(thirdDivisionId)) {
            Long cityIdByDivisionId = callGateway.catchNoLog(()-> commonAddressService.getCityIdByDivisionId(thirdDivisionId));
            List<String> cityIdList = StringUtils.splitCommaToList(dockingCityList);
            if (Objects.nonNull(cityIdByDivisionId) && CollectionUtils.isNotEmpty(cityIdList)) {
                if (cityIdList.contains(cityIdByDivisionId.toString())) {
                    return "new";
                }
            }
        }
        return "old";
    }
}
