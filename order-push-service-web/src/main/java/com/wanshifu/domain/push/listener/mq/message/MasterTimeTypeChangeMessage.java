package com.wanshifu.domain.push.listener.mq.message;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/8 14:40
 */
@Data
public class MasterTimeTypeChangeMessage {

    /**
     * 师傅id
     */
    @NotNull
    private Long masterId;



    /**
     * 变更前类型
     */
    private Integer oldMasterTimeType;

    /**
     * 变更后类型
     */
    @NotNull
    private Integer masterTimeType;



}
