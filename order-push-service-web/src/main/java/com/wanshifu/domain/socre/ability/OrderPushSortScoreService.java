package com.wanshifu.domain.socre.ability;

import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderExtraDataDTO;
import com.wanshifu.order.push.domains.dto.OrderGrabDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-09-21 14:28
 * @Description 订单推单排序能力
 * @Version v1
 **/

public interface OrderPushSortScoreService {

    /**
     * 订单推单成功后，通知排序
     *
     * @param provinceNextId 省下级地址id
     * @param orderBase
     * @param orderExtraData
     * @param orderGrab
     * @param pushMode
     * @param recruitTagName
     * @param artificialPush
     * @param masterIdsList
     */
    public void orderPushSuccessSortNotice(List<Long> provinceNextId, OrderBaseDTO orderBase, OrderExtraDataDTO orderExtraData,
                                           OrderGrabDTO orderGrab, String pushMode, String recruitTagName,
                                           Integer artificialPush, List<Long> masterIdsList,String matchSceneCode);


    /**
     * 师傅报价成功重新排序通知
     *
     * @param provinceNextId 省下级地址id
     * @param orderBase
     * @param orderExtraData
     * @param orderGrab
     */
    public void masterOfferSuccessSortNotice(List<Long> provinceNextId, OrderBaseDTO orderBase,
                                             OrderExtraDataDTO orderExtraData,
                                             OrderGrabDTO orderGrab, Long masterId);

}

