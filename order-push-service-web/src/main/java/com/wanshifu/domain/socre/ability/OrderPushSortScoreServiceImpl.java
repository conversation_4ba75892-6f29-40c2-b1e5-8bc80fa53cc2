package com.wanshifu.domain.socre.ability;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.domain.base.ability.CommonBooleanValueService;
import com.wanshifu.domain.base.ability.CommonService;
import com.wanshifu.domain.base.tools.FeiShuUtils;
import com.wanshifu.domain.push.OrderPushResourceService;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.domain.sdk.config.CommonOrderConfigService;
import com.wanshifu.domain.sdk.enterprise.CommonEnterpriseOrderService;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.domain.socre.model.OrderDelaySortBo;
import com.wanshifu.domain.socre.model.OrderSortBo;
import com.wanshifu.domain.socre.model.PushSortProducerTagEnum;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderByGlobalIdRqt;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.push.api.OrderPushResourceApi;
import com.wanshifu.order.push.domains.dto.OrderBaseDTO;
import com.wanshifu.order.push.domains.dto.OrderExtraDataDTO;
import com.wanshifu.order.push.domains.dto.OrderGrabDTO;
import com.wanshifu.order.push.request.GetMasterOrderCategoryRateRqt;
import com.wanshifu.order.push.request.GetWaitOfferMasterIdsByOrderIdRqt;
import com.wanshifu.order.push.response.GetMasterOrderCategoryRateResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023-09-21 14:31
 * @Description
 * @Version v1
 **/
@Slf4j
@Service
public class OrderPushSortScoreServiceImpl implements OrderPushSortScoreService {

    /**
     * 计算订单排序分值开放城市
     */
    @Value("${wanshifu.orderSortCalculate.openCity:close}")
    private String openCity;
    @Resource
    private CommonAddressService commonAddressService;
    @Resource
    private CommonService commonService;
    @Resource
    private CommonOrderConfigService orderConfigService;
    @Resource
    private MessageSenderService messageSenderService;
    @Resource
    private OrderPushResourceService orderPushResourceService ;
    @Resource
    private CommonEnterpriseOrderService commonEnterpriseOrderService;
    @Resource
    private CommonOrderOfferService commonOrderOfferService;

    /**
     * 计算订单排序分值topic
     */
    @Value("${wanshifu.rocketMQ.order-sort-topic}")
    private String orderSortTopic;
    /**
     * 是否计算订单一级类目占比（1:开启 0:关闭）
     */
    @Value("${wanshifu.orderSortCalculate.orderCategoryRate:1}")
    private String calculateOrderCategoryRate;


    @Override
    public void orderPushSuccessSortNotice(List<Long> provinceNextId, OrderBaseDTO orderBase, OrderExtraDataDTO orderExtraData, OrderGrabDTO orderGrab,
                                           String pushMode, String recruitTagName, Integer artificialPush, List<Long> masterIdsList,String matchSceneCode) {

        if (CollectionUtils.isEmpty(masterIdsList)) {
            return;
        }

        if("toc_master_at_tob_app_offer_limit".equals(matchSceneCode)){
            return ;
        }

        try {
            OrderSortBo orderSortVo = buildOrderSortBo(provinceNextId, orderBase, orderExtraData, orderGrab, pushMode, recruitTagName,
                    artificialPush, masterIdsList, null);
            if (Objects.isNull(orderSortVo)) {
                return;
            }
            //延迟10s计算,transformers同步order_push -> order_push_score可能存在延迟
            messageSenderService.sendDelayMessage(orderSortTopic, PushSortProducerTagEnum.ORDER_SORT.tag, JSON.toJSONString(orderSortVo), 10 * 1000L, true);
        } catch (Exception e) {
            FeiShuUtils.sendTempMsg("订单推单排序通知", null, e);
            log.error("orderId:{} send order sort mq is error!", orderBase.getOrderId());
        }
    }


    @Override
    public void masterOfferSuccessSortNotice(List<Long> provinceNextId, OrderBaseDTO orderBase, OrderExtraDataDTO orderExtraData,
                                             OrderGrabDTO orderGrab, Long masterId) {

        try {
            if (Objects.isNull(masterId)) {
                return;
            }
            OrderExclusiveTagResp masterDimensionalityTag = commonOrderOfferService.getOrderExclusiveTag(orderBase.getOrderId(), masterId, "exclusive");
            String recruitTagName = Objects.nonNull(masterDimensionalityTag) && StringUtils.isNotEmpty(masterDimensionalityTag.getTagName()) ? masterDimensionalityTag.getTagName() : "";
            OrderSortBo orderSortVo = buildOrderSortBo(provinceNextId, orderBase, orderExtraData, orderGrab, "", recruitTagName,
                    0, null, masterId);
            if (Objects.isNull(orderSortVo)) {
                //FeiShuUtils.sendTempMsg("订单推单排序通知",null,"组装排序通知参数失败" );
                return;
            }
            log.info("interface.simplify switch send sortDelay message!");
            OrderDelaySortBo orderDelaySortBo = new OrderDelaySortBo();
            orderDelaySortBo.setOrderSortRqt(orderSortVo);
            orderDelaySortBo.setScoreItemSet(Sets.newHashSet("offer_price_master_num", "no_master_offer_price"));
            messageSenderService.sendDelayMessage(orderSortTopic, PushSortProducerTagEnum.ORDER_DELAY_SORT.tag, JSON.toJSONString(orderDelaySortBo), 10 * 1000L, true);
        } catch (Exception e) {
            FeiShuUtils.sendTempMsg("订单推单排序通知", null, e);
            log.error("orderId:{} send order sort mq is error!", orderBase.getOrderId());
        }
    }

    private OrderSortBo buildOrderSortBo(List<Long> provinceNextId, OrderBaseDTO orderBase, OrderExtraDataDTO orderExtraData,
                                         OrderGrabDTO orderGrab, String pushMode,
                                         String recruitTagName, Integer artificialPush,
                                         List<Long> masterIdsList, Long masterId) {

        if (StringUtils.isBlank(openCity) || StringUtils.equals("close", openCity)) {
            return null;
        }
        Long cityId = commonAddressService.getCityIdByDivisionId(orderBase.getThirdDivisionId());
        Set<String> cityIdSet = Arrays.stream(openCity.split(",")).collect(Collectors.toSet());
        if (!("all".equals(openCity) || cityIdSet.contains(String.valueOf(cityId)))) {
            return null;
        }

        OrderSortBo orderSortVo = new OrderSortBo();
        orderSortVo.setGlobal_order_trace_id(orderBase.getGlobalOrderTraceId());
        orderSortVo.setMaster_order_id(orderBase.getOrderId());
        orderSortVo.setAccount_type(orderBase.getAccountType());
        orderSortVo.setServe_type(orderBase.getServeType());
        orderSortVo.setSecond_division_id(cityId);
        orderSortVo.setThird_division_id(orderBase.getThirdDivisionId());
        orderSortVo.setFourth_division_id(orderBase.getFourthDivisionId());
        orderSortVo.setCategory_id(orderBase.getCategoryId());
        //orderSortVo.setExpect_door_in_start_date();
        orderSortVo.setServe_level_1_id(orderBase.getServeLevel1Ids());
        orderSortVo.setServe_ids(orderBase.getServeIds());
        orderSortVo.setBusiness_line_id(orderBase.getBusinessLineId());
        Integer appointType = Objects.nonNull(orderGrab) ? orderGrab.getAppointType() : 0;
        orderSortVo.setAppoint_type(appointType);
        orderSortVo.setAccount_id(orderBase.getAccountId());
        orderSortVo.setUser_id(orderBase.getAccountId());
        orderSortVo.setOrder_from(orderBase.getOrderFrom());
        orderSortVo.setPush_mode(pushMode);
        orderSortVo.setRecruit_tag_name(recruitTagName);
        orderSortVo.setEmergency_order_flag(orderExtraData.getEmergencyOrderFlag());
        orderSortVo.setOn_time_order_flag(orderExtraData.getOnTimeOrderFlag());
        orderSortVo.setTimer_flag(orderExtraData.getOnTimeOrderFlag());
        orderSortVo.setTimer_flag(orderExtraData.getTimerFlag());
        orderSortVo.setArtificial_push(artificialPush);
        orderSortVo.setOrder_create_time(DateUtils.formatDate(orderBase.getOrderCreateTime(), DateUtils.YToSec));
        orderSortVo.setGeneral_field(orderExtraData.getGeneralField());
        if (commonService.isEnterpriseOrder(orderBase.getAccountType())) {
            com.wanshifu.enterprise.order.domain.po.OrderBase enterpriseOrderBase = commonEnterpriseOrderService.getOrderBase(orderBase.getGlobalOrderTraceId(), orderBase.getAccountId());
            Long userId = 0L;
            if (Objects.nonNull(enterpriseOrderBase) && enterpriseOrderBase.getFromAccountType().equals(AccountType.USER.code)) {
                userId = enterpriseOrderBase.getFromAccountId();
            }
            orderSortVo.setUser_id(userId);
        }
        JSONObject orderPosition = new JSONObject()
                .fluentPut("buyerAddressLatitude", orderExtraData.getBuyerAddressLatitude())
                .fluentPut("buyerAddressLongitude", orderExtraData.getBuyerAddressLongitude());

        orderSortVo.setOrder_lng_lat(orderPosition.toJSONString());
        if (StringUtils.isNotBlank(orderBase.getServeIds())) {
            List<ServeBaseInfoResp> serveBaseInfoRespList = orderConfigService.getServeList(orderBase.getServeIds(), orderBase.getBusinessLineId());
            if (CollectionUtils.isNotEmpty(serveBaseInfoRespList)) {
                List<Long> lv2ServeIdList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lv2ServeIdList)) {
                    orderSortVo.setServe_level_2_ids(Joiner.on(",").join(lv2ServeIdList));
                }
            }
        }

        List<OrderSortBo.MasterExtendData> masterExtendDataList = getMasterExtendData(provinceNextId, orderBase, masterIdsList, masterId);
        if (masterExtendDataList == null) {
            return null;
        }
        orderSortVo.setMaster_info_list(masterExtendDataList);
        return orderSortVo;
    }

    private List<OrderSortBo.MasterExtendData> getMasterExtendData(List<Long> provinceNextId, OrderBaseDTO orderBase, List<Long> masterIdsList,
                                                                   Long masterId) {
        List<OrderSortBo.MasterExtendData> masterExtendDataList = null;
        if (CollectionUtils.isNotEmpty(masterIdsList)) {
            List<GetMasterOrderCategoryRateResp> orderCategoryRateResps = this.getMasterOrderCategoryRateResps(provinceNextId, masterIdsList);
            Map<Long, Integer> masterOrderNumMap = orderCategoryRateResps.stream()
                    .collect(Collectors.groupingBy(GetMasterOrderCategoryRateResp::getMasterId,
                            Collectors.mapping(GetMasterOrderCategoryRateResp::getOrderNum, Collectors.reducing(0, Integer::sum))));
            Map<String, Integer> masterCategoryOrderNumMap = orderCategoryRateResps.stream().collect(Collectors.toMap(it -> it.getMasterId() + "_" + it.getCategoryId(), GetMasterOrderCategoryRateResp::getOrderNum));

            Integer categoryId = orderBase.getCategoryId();

            masterExtendDataList = masterIdsList.stream().map(it -> {

                OrderSortBo.MasterExtendData masterExtendData = new OrderSortBo.MasterExtendData();
                masterExtendData.setMaster_id(it.toString());
                BigDecimal order_category_rate = new BigDecimal(100);
                Integer totalOrderNum = masterOrderNumMap.get(it);
                if (totalOrderNum != null && totalOrderNum >= 0) {
                    Integer categoryOrderNum = masterCategoryOrderNumMap.get(it + "_" + categoryId);
                    if (categoryOrderNum != null && categoryOrderNum >= 0) {
                        order_category_rate = new BigDecimal(categoryOrderNum).divide(new BigDecimal(totalOrderNum), 2, RoundingMode.DOWN).multiply(new BigDecimal(100));

                    }
                }
                masterExtendData.setOrder_category_rate(order_category_rate);
                return masterExtendData;

            }).collect(Collectors.toList());

        } else {
            List<Long> waitOfferMasterIdsByOrderId = this.getWaitOfferMasterIdsByOrderId(provinceNextId, orderBase.getOrderId());
            waitOfferMasterIdsByOrderId.remove(masterId);
            if (CollectionUtils.isEmpty(waitOfferMasterIdsByOrderId)) {
                return null;
            }
            masterExtendDataList = waitOfferMasterIdsByOrderId.stream().distinct().map(it -> new OrderSortBo.MasterExtendData(String.valueOf(it)))
                    .collect(Collectors.toList());

        }
        return masterExtendDataList;
    }


    private List<GetMasterOrderCategoryRateResp> getMasterOrderCategoryRateResps(List<Long> provinceNextId, List<Long> masterIdList) {

        List<GetMasterOrderCategoryRateResp> resps = Lists.newArrayList();
        if (StringUtils.equals(calculateOrderCategoryRate, "0")) {
            return resps;
        }
        List<List<Long>> masterIdsList = CollUtil.splitList(masterIdList, 20);

        return masterIdsList.stream().flatMap(it -> {
                    List<GetMasterOrderCategoryRateResp> orderCategoryRateResps = new ArrayList<>();
                    List<GetMasterOrderCategoryRateResp> masterOrderCategoryRate = orderPushResourceService.getMasterOrderCategoryRate(new GetMasterOrderCategoryRateRqt(it, provinceNextId.get(0)));
                    if (CollectionUtils.isNotEmpty(masterOrderCategoryRate)) {
                        orderCategoryRateResps = masterOrderCategoryRate.stream().map(getMasterOrderCategoryRateResp -> {
                            GetMasterOrderCategoryRateResp getMasterOrderCategoryRateResp1 = new GetMasterOrderCategoryRateResp();
                            getMasterOrderCategoryRateResp1.setMasterId(getMasterOrderCategoryRateResp.getMasterId());
                            getMasterOrderCategoryRateResp1.setCategoryId(getMasterOrderCategoryRateResp.getCategoryId());
                            getMasterOrderCategoryRateResp1.setOrderNum(getMasterOrderCategoryRateResp.getOrderNum());
                            return getMasterOrderCategoryRateResp1;
                        }).collect(Collectors.toList());
                    }
                    return Optional.ofNullable(orderCategoryRateResps)
                            .map(Collection::stream)
                            .orElse(Stream.empty());
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * @param orderId
     * @return
     */
    private List<Long> getWaitOfferMasterIdsByOrderId(List<Long> provinceNextId, Long orderId) {
        List<Long> masterIds = Lists.newArrayList();
        Integer pageNum = 1;
        Integer pageSize = 500;
        for (; ; ) {
            GetWaitOfferMasterIdsByOrderIdRqt getWaitOfferMasterIdsByOrderIdRqt = new GetWaitOfferMasterIdsByOrderIdRqt();
            getWaitOfferMasterIdsByOrderIdRqt.setOrderId(orderId);
            getWaitOfferMasterIdsByOrderIdRqt.setPageNum(pageNum);
            getWaitOfferMasterIdsByOrderIdRqt.setPageSize(pageSize);
            getWaitOfferMasterIdsByOrderIdRqt.setProvinceNextId(provinceNextId.get(0));
            List<Long> waitOfferMasterIdsByOrderId = orderPushResourceService.getWaitOfferMasterIdsByOrderId(getWaitOfferMasterIdsByOrderIdRqt);

            if (CollectionUtils.isEmpty(waitOfferMasterIdsByOrderId)) {
                return masterIds;
            }
            masterIds.addAll(waitOfferMasterIdsByOrderId);
            if (waitOfferMasterIdsByOrderId.size() == pageSize) {
                pageNum++;
            } else {
                return masterIds;
            }
        }
    }
}
