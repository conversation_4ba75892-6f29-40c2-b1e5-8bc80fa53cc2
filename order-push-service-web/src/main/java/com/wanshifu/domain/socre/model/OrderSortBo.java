package com.wanshifu.domain.socre.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 :  订单排序通知mq.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-06-30 14:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderSortBo {

    /**
     * 全局订单id
     */
    private Long global_order_trace_id;

    /**
     * 师傅订单id
     */
    private Long master_order_id;


    /**
     * 账号类型
     */
    private String account_type;

    /**
     * 服务类型
     */
    private Integer serve_type;


    /**
     * 订单二级地址（城市）
     */
    private Long second_division_id;

    /**
     * 订单三级地址
     */
    private Long third_division_id;

    /**
     * 订单四级地址（街道/乡镇）
     */
    private Long fourth_division_id;

    /**
     * 类目id
     */
    private Integer category_id;


    /**
     * 期待上门开始时间
     */
    private String expect_door_in_start_date;

    /**
     * 期望上门结束时间
     */
    private String expect_door_in_end_date;

    /**
     * 一级服务id
     */
    private String serve_level_1_id;


    /**
     * 二级服务id
     */
    private String serve_level_2_ids;

    /**
     * 三级服务id
     */
    private String serve_ids;

    /**
     * 业务线id
     */
    private Integer business_line_id;

    /**
     * 订单客户地址经纬度
     */
    private String order_lng_lat;

    /**
     * 指派模式
     */
    private Integer appoint_type;

    /**
     * 账号id
     */
    private Long account_id;

    /**
     * 用户id
     */
    private Long user_id;

    /**
     * 订单来源
     */
    private String order_from;

    /**
     * 推单模式
     */
    private String push_mode;

    /**
     * 订单标签名
     */
    private String recruit_tag_name;


    /**
     * 加急单标识：
     */
    private Integer emergency_order_flag;


    /**
     * 准时单标识 0、普通订单 1、准时单
     */
    private Integer on_time_order_flag;


    /**
     * 定时单标记
     */
    private Integer timer_flag;


    /**
     * 是否人工推单
     */
    private Integer artificial_push;


    /**
     * 发布时间
     */
    private String order_create_time;

    /**
     * 订单通用字段
     */
    private String general_field;

    /**
     * 推单师傅
     */
    private List<MasterExtendData> master_info_list;

    @Data
    public static class MasterExtendData {
        /**
         * 师傅id
         */
        private String master_id;
        /**
         * 订单列表一级类目占比
         */
        private BigDecimal order_category_rate;

        public MasterExtendData() {
        }

        public MasterExtendData(String masterId) {
            this.master_id = masterId;
            this.order_category_rate = BigDecimal.ZERO;
        }
    }
}