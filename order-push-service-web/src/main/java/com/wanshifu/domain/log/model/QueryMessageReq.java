package com.wanshifu.domain.log.model;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-08-28 16:29
 * @Description
 * @Version v1
 **/
@Data
public class QueryMessageReq {

    /**
     * 开始偏移量
     */
    @NotNull
    private Long startOffsetId;

    /**
     * 结束偏移量
     */
    @NotNull
    private Long endOffsetId;

    /**
     * 来源类型 send-发送 accept-接收
     */
    @ValueIn(value = "send,accept",required = true)
    private String sourceType;

    /**
     * 消息topic（注意消息顺序 建议保证一致，特别是修改订单场景）
     */
    private String topic;

    /**
     * 消息tag
     */
    private String tag;


    /**
     * 消息id 集合
     */
    private List<String> messageIds;


    /**
     * 处理状态
     */
    @ValueIn(value = "success,fail")
    private String dealStatus;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 当前页
     */
    private Integer page = 1;

    /**
     * 当前条数
     */
    private Integer size = 10;


    /**
     * 是否回放
     */
    private Boolean isPlayback = false;


    /**
     * 延迟消息时间 （单位毫秒）
     */
    private Long delayTime;

    /**
     * 替换消息中旧值
     */
    private String oldValue;

    /**
     * 替换消息中新值
     */
    private String newValue;




}
