package com.wanshifu.domain.log.ability;

import com.wanshifu.domain.log.model.QueryMessageReq;
import com.wanshifu.domain.log.model.OrderMqLogVo;
import com.wanshifu.infrastructure.OrderMqLog;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-09-05 09:56
 * @Description 领域能力
 * @Version v1
 **/
public interface OrderMqDomainService {


    /**
     * 写入发送mq消息
     *
     * @param orderMqLog
     * @return
     */
    void insertSendMq(OrderMqLogVo orderMqLog);


    /**
     * 写入接收mq消息
     *
     * @param orderMqLog
     * @return
     */
    void insertAcceptMq (OrderMqLogVo orderMqLog);


    /**
     * 通过消息id, 查询mq消息日志。
     *
     * @param messageId
     * @param sourceType
     * @return
     */
    OrderMqLog selectByMessageId(String messageId, String sourceType);


    /**
     * 更新失败原因
     *
     * @param messageId
     * @param sourceType
     * @param failReason
     * @return
     */
    void updateMessageFail(String messageId, String sourceType, String failReason);


    /**
     * 更新回放状态
     * @param offsetId
     * @param messageId
     * @param status
     */
    public void updatePlaybackStatus (Long offsetId, String messageId, String status);


    /**
     * 分页查询
     * @param queryMessageReq
     * @return
     */
    public List<OrderMqLog> pageQueryMessage(QueryMessageReq queryMessageReq);
}




