package com.wanshifu.domain.log.serviceimpl;

import com.aliyun.openservices.ons.api.SendResult;
import com.github.pagehelper.PageHelper;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.domain.log.OrderMqLogService;
import com.wanshifu.domain.log.ability.OrderMqDomainService;
import com.wanshifu.domain.log.model.QueryMessageReq;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.OrderMqLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-08-21 14:41
 * @Description
 * @Version v1
 **/

@Slf4j
@Service
public class OrderMqLogServiceImpl implements OrderMqLogService {

    @Resource
    private MessageSenderService messageSenderService;
    @Resource
    private OrderMqDomainService orderMqDomainService;

    @Override
    public SimplePageInfo<Long> pageQueryMessage (QueryMessageReq queryMessageReq) {

        List<Long> offsetId = new ArrayList<>();
        PageHelper.startPage(queryMessageReq.getPage(), queryMessageReq.getSize());
        List<OrderMqLog> orderMqLogs = orderMqDomainService.pageQueryMessage(queryMessageReq);
        if (CollectionUtils.isEmpty(orderMqLogs)) {
            return new SimplePageInfo<>(offsetId);
        }
        Boolean isPlayback = queryMessageReq.getIsPlayback();
        String oldValue = queryMessageReq.getOldValue();
        String newValue = queryMessageReq.getNewValue();
        //是否支持替换
        Boolean isSupportReplace = StringUtils.isNotEmpty(oldValue) && StringUtils.isNotEmpty(newValue) && isPlayback ? true : false;
        //消息延迟时间
        Long delayTime = queryMessageReq.getDelayTime();

        for (OrderMqLog orderMqLog : orderMqLogs) {
            if (isPlayback) {

                //支持mq重发
                String dataDetails = orderMqLog.getDataDetails();
                String topic = orderMqLog.getTopic();
                String tag = orderMqLog.getTag();

                if (StringUtils.isEmpty(dataDetails) || StringUtils.isEmpty(topic) || StringUtils.isEmpty(tag)) {
                    continue;
                }

                if (isSupportReplace) {
                    dataDetails =  dataDetails.replace(oldValue,newValue);
                }

                SendResult sendResult = null;

                if (Objects.nonNull(delayTime) && delayTime > 0) {
                    sendResult = messageSenderService.sendDelayMessage(topic, tag, dataDetails, delayTime);
                }else {
                    sendResult = messageSenderService.sendSyncMessage(topic, tag, dataDetails);
                }

                if (Objects.nonNull(sendResult)) {
                    orderMqDomainService.updatePlaybackStatus(orderMqLog.getLogOffsetId(),sendResult.getMessageId(),"成功");
                }else {
                    orderMqDomainService.updatePlaybackStatus(orderMqLog.getLogOffsetId(),sendResult.getMessageId(),"失败");
                }

            }

            offsetId.add(orderMqLog.getLogOffsetId());
        }

        return  new SimplePageInfo<>(offsetId);
    }


}
