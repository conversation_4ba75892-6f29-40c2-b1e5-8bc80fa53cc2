package com.wanshifu.domain.log.ability;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.domain.base.tools.FeiShuTokenHelper;
import com.wanshifu.domain.base.tools.FeiShuUtils;
import com.wanshifu.domain.log.model.QueryMessageReq;
import com.wanshifu.domain.log.model.OrderMqLogVo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.repository.OrderMqLogRepository;
import com.wanshifu.infrastructure.OrderMqLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @Date 2023-09-05 09:57
 * @Description
 * @Version v1
 **/
@Service
@Slf4j
public class OrderMqDomainServiceImpl  implements OrderMqDomainService {

    @Resource
    private OrderMqLogRepository orderMqLogRepository;

    @Resource
    private AsyncConfigurer asyncConfigurer;

    @Value("${recordLog.switch:on}")
    private String recordLogSwitch;

    @Resource
    private Executor otherBusDiscardPolicyExecutor;



    @Override
    public void insertSendMq (OrderMqLogVo orderMqLogVo) {

        if("off".equals(recordLogSwitch)){
            return;
        }

        CompletableFuture.runAsync(
                ()->{
                    String messageId = orderMqLogVo.getMessageId();
                    if (StringUtils.isEmpty(messageId)) {
                        return ;
                    }
                    //查询messageId是否已存在消息
                    OrderMqLog dbOrderMqLog = selectByMessageId (messageId, "send");
                    if (Objects.nonNull(dbOrderMqLog)) {
                        return ;
                    }
                    Long globalOrderTraceId = getGlobalOrderTraceId(orderMqLogVo.getDataDetails(),orderMqLogVo.getGlobalOrderTraceId());
                    OrderMqLog orderMqLog = orderMqLogVo.toPo();
                    orderMqLog.setSourceType("send");
                    orderMqLog.setDealStatus("success");
                    orderMqLog.setGlobalOrderTraceId(globalOrderTraceId);

                    try {
                        orderMqLogRepository.insertSelective(orderMqLog);
                    }catch (Exception e) {
                        if (e instanceof  DuplicateKeyException) {
                            return;
                        }
                        log.error("sourceType:【send】 ,messageId:【{}】 insert order mq log error :{} ",messageId,e);
                        FeiShuUtils.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_ORDER_CENTER_BUSINESS_URL,
                                String.format("服务来源:【order-push-service】\n" +
                                                "sourceType:【send】 \n" +
                                                "topic:【%s】\n " +
                                                "tag:【%s】 \n" +
                                                "messageId:【%s】\n" +
                                                "insert order mq log error :{%s}",
                                        orderMqLog.getTopic(),orderMqLog.getTag(),messageId, e.getMessage()));
                    }



                }, otherBusDiscardPolicyExecutor );



    }

    @Override
    public void insertAcceptMq(OrderMqLogVo orderMqLogVo) {

        if("off".equals(recordLogSwitch)){
            return;
        }

        CompletableFuture.runAsync(()->{
            String messageId = orderMqLogVo.getMessageId();
            if (StringUtils.isEmpty(messageId)) {
                return ;
            }
            //查询messageId是否已存在消息
            OrderMqLog dbOrderMqLog = selectByMessageId(messageId, "accept");
            if (Objects.nonNull(dbOrderMqLog)) {
                return ;
            }
            Long globalOrderTraceId = getGlobalOrderTraceId(orderMqLogVo.getDataDetails(), orderMqLogVo.getGlobalOrderTraceId());
            OrderMqLog orderMqLog = orderMqLogVo.toPo();
            orderMqLog.setSourceType("accept");
            orderMqLog.setDealStatus("success");
            orderMqLog.setGlobalOrderTraceId(globalOrderTraceId);
            try {
                orderMqLogRepository.insertSelective(orderMqLog);
            }catch (Exception e) {
                if (e instanceof DuplicateKeyException) {
                    return;
                }

                log.error("sourceType:【accept】 ,messageId:【{}】 insert order mq log error :{} ",messageId,e);
                FeiShuUtils.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_ORDER_CENTER_BUSINESS_URL,
                        String.format(  "来源服务:【order-push-service】\n" +
                                        "sourceType:【send】 \n" +
                                        "topic:【%s】\n " +
                                        "tag:【%s】 \n" +
                                        "messageId:【%s】\n" +
                                        "insert order mq log error :{%s}",
                                orderMqLog.getTopic(),orderMqLog.getTag(),messageId, e.getMessage()));
            }

        },otherBusDiscardPolicyExecutor);



    }


    @Override
    public OrderMqLog selectByMessageId(String messageId, String sourceType) {
        if (StringUtils.isEmpty(messageId) || StringUtils.isEmpty(sourceType)) {
            return null;
        }
        Example example = new Example(OrderMqLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("messageId",messageId);
        criteria.andEqualTo("sourceType",sourceType);
        List<OrderMqLog> orderMqLogs = orderMqLogRepository.selectByExample(example);
        return CollectionUtils.getFirstSafety(orderMqLogs);
    }



    @Override
    public void updateMessageFail(String messageId, String sourceType, String failReason) {

        if("off".equals(recordLogSwitch)){
            return;
        }


        CompletableFuture.runAsync(()->{
            //延迟500毫秒。避免查询数据还没写入
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                log.error("updateMessageFail is InterruptedException:{} ", e.getMessage());
            }
            List<OrderMqLog> orderMqLogs = orderMqLogRepository.selectMasterByMessageId(messageId, sourceType);
            if (CollectionUtils.isEmpty(orderMqLogs)) {
                return;
            }
            OrderMqLog orderMqLog = CollectionUtils.getFirstSafety(orderMqLogs);
            OrderMqLog updateOrderMqLog = new OrderMqLog();
            updateOrderMqLog.setLogOffsetId(orderMqLog.getLogOffsetId());
            updateOrderMqLog.setDealStatus("fail");
            updateOrderMqLog.setFailReason(failReason);
            orderMqLogRepository.updateByPrimaryKeySelective(updateOrderMqLog);
        },asyncConfigurer.getAsyncExecutor());

    }


    @Override
    public void updatePlaybackStatus(Long offsetId, String messageId, String status) {
        orderMqLogRepository.updatePlaybackStatus(offsetId, messageId, status);
    }


    @Override
    public List<OrderMqLog> pageQueryMessage(QueryMessageReq queryMessageReq) {
        return orderMqLogRepository.pageQueryMessage(queryMessageReq);
    }

    public Long getGlobalOrderTraceId (String jsonData, Long fromGlobalOrderTraceId) {
        if (Objects.nonNull(fromGlobalOrderTraceId) && fromGlobalOrderTraceId > 0L) {
            return fromGlobalOrderTraceId;
        }

        Long globalOrderTraceId = 0L;
        try {
            if (StringUtils.isNotEmpty(jsonData) && ( jsonData.contains("globalOrderTraceId") ||
                    jsonData.contains("globalOrderId") || jsonData.contains("global_order_trace_id"))) {
                JSONObject jsonObject = JSON.parseObject(jsonData);
                if (jsonObject.containsKey("globalOrderTraceId")) {
                    globalOrderTraceId = jsonObject.getLong("globalOrderTraceId");
                }else if (jsonObject.containsKey("globalOrderId")) {
                    globalOrderTraceId = jsonObject.getLong("globalOrderId");
                }else if (jsonObject.containsKey("global_order_trace_id")) {
                    globalOrderTraceId = jsonObject.getLong("global_order_trace_id");
                }
            }
        }catch (Exception e)  {
            log.error("json mq data error:{}",e.getMessage());
        }
        return globalOrderTraceId;
    }
}
