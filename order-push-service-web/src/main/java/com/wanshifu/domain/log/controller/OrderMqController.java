package com.wanshifu.domain.log.controller;
import com.wanshifu.domain.log.OrderMqLogService;
import com.wanshifu.domain.log.model.QueryMessageReq;
import com.wanshifu.framework.core.page.SimplePageInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023-08-28 16:26
 * @Description
 * @Version v1
 **/
@RestController
@RequestMapping("order/mq")
public class OrderMqController {

    @Resource
    private OrderMqLogService orderMqLogService;

    @PostMapping("pageQueryMessage")
    public SimplePageInfo<Long> pageQueryMessage(@Validated  @RequestBody QueryMessageReq queryMessageReq){
        return orderMqLogService.pageQueryMessage(queryMessageReq);
    }

}
