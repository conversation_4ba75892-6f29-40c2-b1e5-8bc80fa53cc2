package com.wanshifu.domain.log.model;

import com.wanshifu.infrastructure.OrderMqLog;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023-08-21 14:47
 * @Description
 * @Version v1
 **/
@Data
public class OrderMqLogVo {


    /**
     * 消息topic
     */
    private String topic;

    /**
     * 消息tag
     */
    private String tag;

    /**
     * 消息内容描述
     */
    private String messageDesc;

    /**
     * 消息id
     */
    private String messageId;
    /**
     * 数据内容
     */
    private String dataDetails;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 全局id
     */
    private Long globalOrderTraceId;


    public OrderMqLogVo() {
    }

    public OrderMqLogVo (String topic, String tag, String messageId, String dataDetails) {
        this.topic = topic;
        this.tag = tag;
        this.messageId = messageId;
        this.dataDetails = dataDetails;
    }

    public OrderMqLog toPo(){
        OrderMqLog orderMqLog = new OrderMqLog();
        orderMqLog.setTopic(this.topic);
        orderMqLog.setTag(this.tag);
        orderMqLog.setMessageDesc(this.messageDesc);
        orderMqLog.setMessageId(this.messageId);
        orderMqLog.setDataDetails(this.dataDetails);
        orderMqLog.setRemark(this.remark);
        return orderMqLog;
    }

}
