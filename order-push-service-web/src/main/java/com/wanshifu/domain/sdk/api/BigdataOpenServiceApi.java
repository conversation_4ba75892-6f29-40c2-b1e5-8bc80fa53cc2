package com.wanshifu.domain.sdk.api;

import com.wanshifu.domain.sdk.decoder.BigDataOpenApiDecoder;
import com.wanshifu.domain.sdk.resp.GetMstServeCompleteInfoV3Resp;
import com.wanshifu.domain.sdk.rqt.GetMstServeCompleteInfoV3Rqt;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "bigdata-open-service",
        url = "${wshifu.bigdata-open-service.url}",
        configuration = {DefaultEncoder.class, BigDataOpenApiDecoder.class, BigDataOpenApiDecoder.ApiErrorDecoder.class}
)
public interface BigdataOpenServiceApi {

    /**
     * 【人群-离线】由商家ID(单个商家)查询人群所属的ID
     * <br/>
     * http://dev-api-manage.wanshifu.com:3000/project/1190/interface/api/66302
     *
     * @param rqt
     * @return
     */
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    @PostMapping("/dataApi/getData/getMstServeCompleteInfoV3")
    List<GetMstServeCompleteInfoV3Resp> getMstServeCompleteInfoV3(@RequestBody GetMstServeCompleteInfoV3Rqt rqt);

}