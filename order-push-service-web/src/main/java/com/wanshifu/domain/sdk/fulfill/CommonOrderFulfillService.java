package com.wanshifu.domain.sdk.fulfill;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.domains.api.request.fulfillment.GetMasterBatchNewOrderServeListRqt;
import com.wanshifu.master.order.domains.api.response.fulfillment.GetMasterBatchNewOrderServeListResp;
import com.wanshifu.master.order.domains.api.response.serveInfo.ServeDetailResp;
import com.wanshifu.master.order.domains.enums.ServeInfoNodeType;
import com.wanshifu.master.order.domains.fulfillment.po.OrderServeTransferInfo;
import com.wanshifu.master.order.domains.po.ServeStop;
import com.wanshifu.master.order.search.domains.api.request.list.ServingOrderRqt;
import com.wanshifu.master.order.search.domains.api.response.list.ServingOrderResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-02-21 15:21
 * @Description 订单履约服务sdk
 * @Version v1
 **/
public interface CommonOrderFulfillService {
    /**
     * 批量获取服务节点信息，依据服务ids
     * @param orderServeIds
     * @param serveInfoNodeType
     * @return
     */
    List<ServeDetailResp> batchGetServeInfoByServeIds(List<Long> orderServeIds, ServeInfoNodeType serveInfoNodeType);

    /**
     * 查询师傅履约服务中订单列表(order_search)
     * @param servingOrderRqt
     * @return
     */
    SimplePageInfo<ServingOrderResp> getMasterServingOrderSearchList(ServingOrderRqt servingOrderRqt);

    /**
     * 查询师傅批量订单最新履约服务列表
     * @param getMasterBatchNewOrderServeListRqt
     * @return
     */
    List<GetMasterBatchNewOrderServeListResp> getMasterBatchNewOrderServeList(GetMasterBatchNewOrderServeListRqt getMasterBatchNewOrderServeListRqt);


    /**
     * 通过订单id。获取服务取消记录
     * @param orderId
     * @return
     */
    List<ServeStop> getOrderServeStopRecording(Long orderId);

    /**
     * 查询师傅转单记录
     * @param globalOrderTraceId
     * @return
     */
    OrderServeTransferInfo getOrderServeTransferInfo(Long globalOrderTraceId);

}

