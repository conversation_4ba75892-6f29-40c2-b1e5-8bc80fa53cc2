package com.wanshifu.domain.sdk.fulfill.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024-01-12 10:14
 * @Description
 * @Version v1
 **/
@Data
public class FulfillmentServeStatusBo {


    /**
     * 订单id
     */
    @NotNull
    private Long orderId;


    /**
     * 订单服务id
     */
    @NotNull
    private Long orderServeId;

    /**
     * 订单服务状态（prepare:待服务,serving:服务中,stop:服务停止、finish:交易完成)
     */
    @NotEmpty
    private String serveStatus;


    /**
     * 订单全局id
     */
    private Long globalOrderTraceId;

}
