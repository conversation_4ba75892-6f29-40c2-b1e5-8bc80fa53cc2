package com.wanshifu.domain.sdk.master.serviceimpl;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.sdk.master.CommonMasterService;
import com.wanshifu.master.manage.config.domain.api.request.release.skillType.QuerySkillTypeIdListBySkillIdsRqt;
import com.wanshifu.master.manage.config.service.api.release.SkillTypeServiceApi;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-03-01 14:04
 * @Description
 * @Version v1
 **/
@Service
public class CommonMasterServiceImpl implements CommonMasterService {

    @Resource
    private CallGateway callGateway;
    @Resource
    private SkillTypeServiceApi skillTypeServiceApi;
    /**
     * 通过订单绑定技能ID获取技能类型ID
     *
     * @param bindingTechnologyIds
     * @return java.util.List<java.lang.String>
     * @author: <PERSON><PERSON><PERSON>ao
     * @date: 2022/1/26
     */
    public List<String> getTechniqueTypeIdByOrderBindingTechnologyIds(String bindingTechnologyIds) {
        if (StrUtil.isNotBlank(bindingTechnologyIds)) {
            String[] str = bindingTechnologyIds.split("\\|");
            bindingTechnologyIds = String.join(",", str);
            QuerySkillTypeIdListBySkillIdsRqt techniqueQueryReq = new QuerySkillTypeIdListBySkillIdsRqt();
            techniqueQueryReq.setSkillIds(bindingTechnologyIds);

            List<Long> skillTypeIdList = callGateway.catchNoLog(
                    ()->skillTypeServiceApi.querySkillTypeIdListBySkillIds(techniqueQueryReq)
                    ,new ArrayList<>(), "技能id查询技能类型",techniqueQueryReq);
            return skillTypeIdList.stream().map(String::valueOf).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
