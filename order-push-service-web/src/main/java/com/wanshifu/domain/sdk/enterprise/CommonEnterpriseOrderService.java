package com.wanshifu.domain.sdk.enterprise;

import com.wanshifu.enterprise.order.domain.infoQuery.api.response.GetOrderBaseByGlobalIdRsp;
import com.wanshifu.enterprise.order.domain.po.OrderBase;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024-03-01 18:29
 * @Description
 * @Version v1
 **/
public interface CommonEnterpriseOrderService {

    /**
     * 获取订单信息
     * @param globalOrderTraceId
     * @param enterpriseId
     * @return
     */
    OrderBase getOrderBase( Long globalOrderTraceId,Long enterpriseId );

    GetOrderBaseByGlobalIdRsp getOrderBase(Long globalOrderTraceId);

    }
