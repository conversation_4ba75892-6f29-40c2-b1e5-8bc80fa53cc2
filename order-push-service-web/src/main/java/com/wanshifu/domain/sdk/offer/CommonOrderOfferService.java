package com.wanshifu.domain.sdk.offer;


import com.wanshifu.order.offer.domains.api.request.BatchGetOrderParam;

import com.wanshifu.order.offer.domains.api.request.infoorder.BatchInsertInfoMasterOrderCloseRqt;
import com.wanshifu.order.offer.domains.api.request.offer.ClearPriceEndRqt;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.*;
import com.wanshifu.order.offer.domains.api.response.infoorder.GetInfoOrderBaseCompositeResp;
import com.wanshifu.order.offer.domains.api.response.infoorder.InfoOrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.offer.PushingOrderCompositeResp;

import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;

import java.util.List;

/**
 * <AUTHOR>
 * @description 公用Offer服务接口
 * @date 2024/2/21 14:33
 */
public interface CommonOrderOfferService {

    /**
     * 获取单个OrderBase
     * @param globalOrderTraceId
     * @return
     */
    OrderBase getOrderBase(Long orderId, Long globalOrderTraceId);

    /**
     * 批量获取OrderBase-最大20条-慎用！！！！！
     * @return
     */
    List<OrderBase> getOrderBaseBatch(List<Long> orderIds, List<String> orderNos, List<Long> globalOrderTraceIds);

    /**
     * 批量获取OrderBase-最大200条
     * @return
     */
    List<OrderBase> getOrderBaseBatchForLimit(List<Long> orderIds, List<String> orderNos, List<Long> globalOrderTraceIds);

    /**
     * 批量获取订单基础信息
     * @param orderIds
     * @return
     */
    List<OrderBaseComposite> getOrderBaseCompositeBatch(List<Long> orderIds);

    /**
     * 批量获取订单基础信息
     * @param orderIds
     * @return
     */
    List<OrderBaseComposite> getOrderBaseCompositeBatch(List<Long> orderIds,Long masterId);

    /**
     * 批量获取订单配件信息列表
     * @param orderIds
     * @return
     */
    List<BranchGetOrderPartsResp> branchGetOrderPartsList(List<Long> orderIds);

    /**
     * 获取订单聚合信息
     * @param orderId
     * @return
     */
    OrderBaseComposite getOrderBaseComposite(Long orderId);


    /**
     * 通过订单id 获取简易版订单信息
     * @param orderId
     * @return
     */
    OrderBaseSampleComposite orderSampleDetailByOrderId(Long orderId);


    /**
     * 根据orderId获取简化订单指派信息
     *
     * @param orderId orderId
     * @return
     */
    SimpleOrderGrab getSimpleOrderGrabByOrderId(Long orderId);

    /**
     * 批量获取订单指派信息
     * @param batchGetOrderParam
     * @return
     */
    List<SimpleOrderGrab> getSimpleOrderGrabByOrderId(BatchGetOrderParam batchGetOrderParam);

    /**
     * 根据globalOrderTraceId获取简化订单指派信息
     *
     * @param globalOrderTraceId globalOrderTraceId
     * @return
     */
    SimpleOrderGrab getSimpleOrderGrabByGlobalId(Long globalOrderTraceId);

    /**
     * 通过全局id 获取简易版订单信息
     * @param globalOrderTraceId
     * @return
     */
    OrderBaseSampleComposite orderSampleDetailByGlobalId(Long globalOrderTraceId);

    /**
     * 批量查询订单信息 (限制10个订单）
     * @param orderIds
     * @param masterId
     * @return
     */
    OrderInfoBatchComposite getOrderInfoBatchComposite(List<Long> orderIds, Long masterId);

    /**
     * 批量查询订单信息
     * @param orderIds
     * @return
     */
    OrderInfoBatchComposite getOrderInfoBatchComposite(List<Long> orderIds);

    /**
     * 批量查询意向单
     * @param orderIds
     * @return
     */
    List<InfoOrderBaseComposite> batchGetInfoOrderBaseComposite(List<Long> orderIds);

    /**
     * 批量查询订单-首个图片aid
     * @param orderIds
     * @param masterId
     * @return
     */
    List<BatchGetOrderInfoExtraResp> getBatchGetOrderInfoExtraResp(List<Long> orderIds, Long masterId);

    /**
     * 批量查询订单商品信息
     * @param orderIds
     * @return
     */
    List<BatchOrderGoodsResp> getOrderGoodsBatch (List<Long> orderIds);

    /**
     * 查询dayNumber(取值1~3)天内的附近单
     *
     * @param orderId
     * @return
     */
    List<OrderBase> selectNearbyOrderListByThreeDays(Long orderId, Integer dayNumber);

    /**
     * 批量查询订单额外信息
     * @param orderIds
     * @return
     */
    List<OrderExtraData> batchGetOrderExtraData(List<Long> orderIds);

    /**
     * 获取推动订单
     * @param orderId
     * @return
     */
    PushingOrderCompositeResp getPushingOrderCompositeResp(Long orderId);

    /**
     * 查询订单标签信息
     * @param orderId
     * @param masterId
     * @return
     */
    List<OrderExclusiveTagResp> getOrderExclusiveTag(Long orderId, Long masterId);

    /**
     * 查询订单标签信息
     * @param orderId
     * @param masterId
     * @param tagType
     * @return
     */
    OrderExclusiveTagResp getOrderExclusiveTag(Long orderId, Long masterId,String tagType);

    /**
     * 获取信息订单基础信息
     * @param orderId
     * @return
     */
    GetInfoOrderBaseCompositeResp getInfoOrderBase(Long orderId);


    /**
     * 更新信息订单首次推单数量
     * @param orderId
     * @param number
     * @return
     */
    Integer updateInfoOrderFirstPushNumber(Long orderId,Integer number);

    /**
     * 批量插入OrderOfferPriceEnd
     * @param clearPriceEndRqt
     * @return
     */
    void clearOrderOfferPriceEndList(ClearPriceEndRqt clearPriceEndRqt);

    /**
     * 批量插入信息订单masterClose表
     * @param batchInsertInfoMasterOrderCloseRqt
     */
    void batchInsertInfoMasterOrderCloseInfo(BatchInsertInfoMasterOrderCloseRqt batchInsertInfoMasterOrderCloseRqt);

}
