package com.wanshifu.domain.sdk.config.serviceimpl;

import com.wanshifu.domain.sdk.config.CommonOrderConfigService;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.dto.serve.ServeIdSetReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-03-01 18:54
 * @Description
 * @Version v1
 **/
@Service
public class CommonOrderConfigServiceImpl implements CommonOrderConfigService {

    @Resource
    private ServeServiceApi serveServiceApi;

    @Override
    public List<ServeBaseInfoResp> getServeList(String serveIds, Integer businessLineId) {
        ServeIdSetReq serveIdSetReq = new ServeIdSetReq();
        serveIdSetReq.setServeIdSet(new HashSet(Arrays.asList(serveIds.split(","))));
        if (businessLineId != null) {
            serveIdSetReq.setBusinessLineId(businessLineId.longValue());
        }
        return serveServiceApi.getServeBaseInfo(serveIdSetReq);
    }
}
