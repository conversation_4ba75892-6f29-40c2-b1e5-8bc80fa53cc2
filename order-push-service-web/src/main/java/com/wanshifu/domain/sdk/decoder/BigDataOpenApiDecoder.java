package com.wanshifu.domain.sdk.decoder;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import feign.Response;
import feign.codec.Decoder;
import feign.codec.ErrorDecoder;
import lombok.Data;
import org.apache.commons.io.IOUtils;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class BigDataOpenApiDecoder implements Decoder {

    private static final Map<Type, ParameterizedTypeImpl> TYPE_PARAMETERIZED_CACHE = new HashMap<>();
    private static final Object LOCK = new Object();

    public BigDataOpenApiDecoder() {

    }

    @Override
    public Object decode(Response response, Type type) {
        ParameterizedTypeImpl parameterizedType = this.getParameterizedType(type);
        if (response.body() == null) {
            return null;
        } else {
            String content;

            ResponseEntity responseEntity;
            try {
                InputStream inputStream = response.body().asInputStream();
                content = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
                responseEntity = this.doDecode(content, parameterizedType);
            } catch (Exception var7) {
                throw new ApiAccessException(response.request().url(), var7.getMessage());
            }

            if (responseEntity == null) {
                throw new ApiAccessException(response.request().url(), "");
            } else if (responseEntity.getErrCode() != 0) {
                throw new ApiAccessException(response.request().url(), content);
            } else {
                return responseEntity.getData();
            }
        }
    }

    private ParameterizedTypeImpl getParameterizedType(Type type) {
        ParameterizedTypeImpl parameterizedType = TYPE_PARAMETERIZED_CACHE.get(type);
        if (parameterizedType == null) {
            synchronized (LOCK) {
                parameterizedType = TYPE_PARAMETERIZED_CACHE.get(type);
                if (parameterizedType == null) {
                    parameterizedType = ParameterizedTypeImpl.make(ResponseEntity.class, new Type[]{type}, (Type) null);
                    TYPE_PARAMETERIZED_CACHE.put(type, parameterizedType);
                }
            }
        }

        return parameterizedType;
    }

    private ResponseEntity doDecode(String content, ParameterizedTypeImpl parameterizedType) throws Exception {
        return (ResponseEntity) JSONObject.parseObject(content, parameterizedType, new Feature[0]);
    }

    @Data
    static class ResponseEntity<T> {

        static final ResponseEntity EMPTY = new ResponseEntity();

        private Integer errCode;

        private T data;

        private String errMessage;

        private String traceId;


    }


    /**
     * 大数据错误解析器
     */
    public static class ApiErrorDecoder implements ErrorDecoder {

        public ApiErrorDecoder() {

        }

        @Override
        public Exception decode(String s, Response response) {
            String content = "";
            try {
                int status = response.status();
                String reason = response.reason();
                if (response.body() != null) {
                    InputStream inputStream = response.body().asInputStream();
                    content = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
                } else {
                    content = "http_status:" + status + " " + reason;
                }
                return new ApiAccessException(response.request().url(), content);
            } catch (IOException var6) {
                return new ApiAccessException(response.request().url(), "");
            }
        }
    }
}