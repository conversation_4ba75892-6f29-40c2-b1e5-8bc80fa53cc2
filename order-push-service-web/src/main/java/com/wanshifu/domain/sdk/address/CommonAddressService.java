package com.wanshifu.domain.sdk.address;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 16:43
 */
public interface CommonAddressService {

    /**
     * 根据divisionId获取城市id
     * @param divisionId
     * @return
     */
    Long getCityIdByDivisionId(Long divisionId);

    /**
     * 根据订单区域id或师傅区域id获取省下级地址id
     *
     * @param divisionId   订单区域id或师傅区域id
     * @param sourceMethod 上游方法，用于后续排查哪个接口未传参或传参有误
     * @param params 上游方法参数
     * @return
     */
    List<Long> getProvinceNextIdV2(Long divisionId, String sourceMethod, String params);
}
