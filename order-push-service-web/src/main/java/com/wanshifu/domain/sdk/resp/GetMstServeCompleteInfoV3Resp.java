package com.wanshifu.domain.sdk.resp;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


@Data
public class GetMstServeCompleteInfoV3Resp {

    /**
     * 全局订单i
     */
    @NotNull
    @Min(1L)
    private Long serveTypeId;


    /**
     * 师傅id
     */
    private Long masterId;


    /**
     * 三级商品id
     */
    private Long goodsLevel3Id;

    private Long sumServeCompleteCnt;

    private Long serveCompleteCnt;

    /**
     * 二级商品id
     */
    private Long goodsLevel2Id;

    /**
     * 一级商品id
     */
    private Long goodsLevel1Id;

}
