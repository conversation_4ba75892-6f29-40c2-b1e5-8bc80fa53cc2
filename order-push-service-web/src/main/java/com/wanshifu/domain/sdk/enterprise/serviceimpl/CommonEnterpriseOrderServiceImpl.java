package com.wanshifu.domain.sdk.enterprise.serviceimpl;

import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.sdk.enterprise.CommonEnterpriseOrderService;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderBaseByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.response.GetOrderBaseByGlobalIdRsp;
import com.wanshifu.enterprise.order.domain.po.OrderBase;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024-03-01 18:30
 * @Description
 * @Version v1
 **/
@Service
public class CommonEnterpriseOrderServiceImpl implements CommonEnterpriseOrderService {
    @Resource
    private CallGateway callGateway;
    @Resource
    private InfoQueryApi infoQueryApi;

    @Override
    public OrderBase getOrderBase( Long globalOrderTraceId,Long enterpriseId) {
        GetOrderByGlobalIdRqt getOrderByGlobalIdRqt = new GetOrderByGlobalIdRqt();
        getOrderByGlobalIdRqt.setGlobalOrderTraceId(globalOrderTraceId);
        getOrderByGlobalIdRqt.setEnterpriseId(enterpriseId);
        //是否更换成enterprise-order-service 服务
        return callGateway.catchLog(() -> infoQueryApi.getOnlyOrderBaseByGlobalId(getOrderByGlobalIdRqt));
    }


    @Override
    public GetOrderBaseByGlobalIdRsp getOrderBase(Long globalOrderTraceId) {
        GetOrderBaseByGlobalIdRqt getOrderByGlobalIdRqt = new GetOrderBaseByGlobalIdRqt();
        getOrderByGlobalIdRqt.setGlobalOrderTraceId(globalOrderTraceId);
        //是否更换成enterprise-order-service 服务
        return callGateway.catchLog(() -> infoQueryApi.getOrderBaseByGlobalId(getOrderByGlobalIdRqt));


    }
}
