package com.wanshifu.domain.sdk.fulfill.serviceimpl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.sdk.fulfill.CommonOrderFulfillService;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.domains.api.request.fulfillment.GetMasterBatchNewOrderServeListRqt;
import com.wanshifu.master.order.domains.api.request.fulfillment.GetOrderBatchServeListRqt;
import com.wanshifu.master.order.domains.api.request.serveInfo.BatchGetServeInfoRqt;
import com.wanshifu.master.order.domains.api.response.fulfillment.GetMasterBatchNewOrderServeListResp;
import com.wanshifu.master.order.domains.api.response.fulfillment.GetOrderBatchServeListResp;
import com.wanshifu.master.order.domains.api.response.serveInfo.ServeDetailResp;
import com.wanshifu.master.order.domains.enums.ServeInfoNodeType;
import com.wanshifu.master.order.domains.fulfillment.po.OrderServeTransferInfo;
import com.wanshifu.master.order.domains.po.ServeStop;
import com.wanshifu.master.order.domains.sink.api.request.orderList.GetBatchOrderServeTransferListRqt;
import com.wanshifu.master.order.domains.sink.api.response.orderList.GetBatchOrderServeTransferListResp;
import com.wanshifu.master.order.search.domains.api.request.list.ServingOrderRqt;
import com.wanshifu.master.order.search.domains.api.response.list.ServingOrderResp;
import com.wanshifu.master.order.search.service.api.ListServiceApi;
import com.wanshifu.master.order.service.api.OrderServeCommonApi;
import com.wanshifu.master.order.service.api.ServeInfoServiceApi;
import com.wanshifu.master.order.serviceSink.api.OrderListServiceSinkApi;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-02-21 15:26
 * @Description
 * @Version v1
 **/
@Service
public class CommonOrderFulfillServiceImpl implements CommonOrderFulfillService {

    @Resource
    private ServeInfoServiceApi serveInfoServiceApi;

    @Resource
    private ListServiceApi searchListServiceApi;

    @Resource
    private CallGateway callGateway;

    @Resource
    private OrderServeCommonApi orderServeCommonApi;

    @Resource
    private OrderListServiceSinkApi orderListServiceSinkApi;


    @Override
    public List<ServeDetailResp> batchGetServeInfoByServeIds(List<Long> orderServeIds, ServeInfoNodeType serveInfoNodeType) {
        if (CollectionUtils.isEmpty(orderServeIds)) {
            return new ArrayList<>();
        }
        if (Objects.isNull(serveInfoNodeType)) {
            throw new BusException("缺失服务节点类型");
        }

        BatchGetServeInfoRqt batchGetServeInfoRqt = new BatchGetServeInfoRqt();
        batchGetServeInfoRqt.setOrderServeIds(orderServeIds);
        batchGetServeInfoRqt.setServeNodeType(serveInfoNodeType.code);
        List<ServeDetailResp> serveDetailResps = callGateway.catchNoLog(() -> serveInfoServiceApi.batchGetServeInfoByServeIds(batchGetServeInfoRqt));
        if(CollectionUtils.isEmpty(serveDetailResps)){
            return new ArrayList<>();
        }
        return serveDetailResps;
    }

    @Override
    public SimplePageInfo<ServingOrderResp> getMasterServingOrderSearchList(ServingOrderRqt servingOrderRqt) {
        return callGateway.catchLogThrow(() -> searchListServiceApi.servingOrder(servingOrderRqt));
    }

    @Override
    public List<GetMasterBatchNewOrderServeListResp> getMasterBatchNewOrderServeList(GetMasterBatchNewOrderServeListRqt getMasterBatchNewOrderServeListRqt) {
        return callGateway.catchLogThrow(() -> orderServeCommonApi.getMasterBatchNewOrderServeList(getMasterBatchNewOrderServeListRqt));
    }


    @Override
    public List<ServeStop> getOrderServeStopRecording(Long orderId) {
        GetOrderBatchServeListRqt getOrderBatchServeListRqt = new GetOrderBatchServeListRqt();
        getOrderBatchServeListRqt.setOrderId(orderId);
        List<GetOrderBatchServeListResp> batchServeList  = callGateway.catchLog(() -> orderServeCommonApi.getOrderBatchServeList(getOrderBatchServeListRqt), "批量查询订单服务信息", getOrderBatchServeListRqt);
        if (CollectionUtils.isEmpty(batchServeList)) {
            return null;
        }

        return batchServeList.stream()
                .filter(it -> Objects.nonNull(it.getOrderServeInfo()) && Objects.nonNull(it.getServeStop()))
                .map(GetOrderBatchServeListResp::getServeStop)
                .collect(Collectors.toList());
    }

    @Override
    public OrderServeTransferInfo getOrderServeTransferInfo(Long globalOrderTraceId) {
        GetBatchOrderServeTransferListRqt getBatchOrderServeTransferListRqt = new GetBatchOrderServeTransferListRqt();
        getBatchOrderServeTransferListRqt.setGlobalOrderTraceIdList(Lists.newArrayList(globalOrderTraceId));
        GetBatchOrderServeTransferListResp getBatchOrderServeTransferListResp = callGateway.catchLogThrow(() -> orderListServiceSinkApi.getBatchOrderServeTransferList(getBatchOrderServeTransferListRqt));
        if(Objects.nonNull(getBatchOrderServeTransferListResp) && CollectionUtils.isNotEmpty(getBatchOrderServeTransferListResp.getOrderInfoVoList())){
            GetBatchOrderServeTransferListResp.OrderInfoVo orderInfoVo = CollUtil.getFirst(getBatchOrderServeTransferListResp.getOrderInfoVoList());
            if(Objects.nonNull(orderInfoVo)){
                return orderInfoVo.getOrderServeTransferInfo();
            }
        }
        return null;
    }

}
