package com.wanshifu.domain.sdk.offer.serviceimpl;

import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.sdk.offer.CommonOrderOfferService;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.order.offer.api.NormalOrderDetailApi;
import com.wanshifu.order.offer.api.NormalOrderListApi;
import com.wanshifu.order.offer.api.NormalOrderOperationApi;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.infoorder.InfoOrderOperationApi;
import com.wanshifu.order.offer.api.infoorder.InfoOrderResourceApi;
import com.wanshifu.order.offer.api.offer.OfferModuleOperateApi;
import com.wanshifu.order.offer.domains.api.request.*;
import com.wanshifu.order.offer.api.offer.OfferModuleResourceApi;
import com.wanshifu.order.offer.domains.api.request.*;
import com.wanshifu.order.offer.domains.api.request.infoorder.BatchGetInfoOrderBaseRqt;
import com.wanshifu.order.offer.domains.api.request.infoorder.BatchInsertInfoMasterOrderCloseRqt;
import com.wanshifu.order.offer.domains.api.request.infoorder.GetInfoOrderBaseCompositeRqt;
import com.wanshifu.order.offer.domains.api.request.offer.ClearPriceEndRqt;
import com.wanshifu.order.offer.domains.api.request.offer.GetPushingOrderCompositeReq;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.*;
import com.wanshifu.order.offer.domains.api.response.infoorder.GetInfoOrderBaseCompositeResp;
import com.wanshifu.order.offer.domains.api.response.infoorder.InfoOrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.offer.PushingOrderCompositeResp;
import com.wanshifu.order.offer.domains.po.InfoOrderBase;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/21 14:37
 */
@Service
public class CommonOrderOfferServiceImpl implements CommonOrderOfferService {
    @Resource
    private NormalOrderListApi normalOrderListApi;
    @Resource
    private NormalOrderDetailApi normalOrderDetailApi;
    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;
    @Resource
    private InfoOrderResourceApi infoOrderResourceApi;
    @Resource
    private InfoOrderOperationApi infoOrderOperationApi;
    @Resource
    private OfferModuleResourceApi offerModuleResourceApi;
    @Resource
    private NormalOrderOperationApi normalOrderOperationApi;
    @Resource
    private OfferModuleOperateApi offerModuleOperateApi;

    @Resource
    private CallGateway callGateway;


    @Override
    public OrderBase getOrderBase(Long orderId, Long globalOrderTraceId) {
        if(Objects.nonNull(orderId)){
            return callGateway.catchLog(() -> normalOrderResourceApi.getOrderBase(orderId), "getOrderBase", orderId);
        }else if(Objects.nonNull(globalOrderTraceId)){
            GetOrderBaseInfoRqt getOrderBaseInfoRqt = new GetOrderBaseInfoRqt();
            getOrderBaseInfoRqt.setGlobalOrderTraceId(globalOrderTraceId);
            return callGateway.catchLog(() -> normalOrderResourceApi.getOrderBaseInfo(getOrderBaseInfoRqt), "getOrderBaseInfo", getOrderBaseInfoRqt);

        }
        return null;
    }

    @Override
    public List<OrderBase> getOrderBaseBatch(List<Long> orderIds, List<String> orderNos, List<Long> globalOrderTraceIds) {

        if(CollectionUtils.isEmpty(orderIds) && CollectionUtils.isEmpty(orderNos) && CollectionUtils.isEmpty(globalOrderTraceIds)){
            return new ArrayList<>();
        }
        OrderBaseBatchRqt orderBaseBatchRqt = new OrderBaseBatchRqt();
        orderBaseBatchRqt.setOrderIds(orderIds);
        orderBaseBatchRqt.setOrderNos(orderNos);
        orderBaseBatchRqt.setGlobalOrderTraceIds(globalOrderTraceIds);
        return callGateway.catchLog(() -> normalOrderListApi.getOrderBaseBatch(orderBaseBatchRqt), "getOrderBaseBatch",orderBaseBatchRqt);
    }

    @Override
    public List<OrderBase> getOrderBaseBatchForLimit(List<Long> orderIds, List<String> orderNos, List<Long> globalOrderTraceIds) {
        if(CollectionUtils.isEmpty(orderIds) && CollectionUtils.isEmpty(orderNos) && CollectionUtils.isEmpty(globalOrderTraceIds)){
            return new ArrayList<>();
        }
        BatchOrderBaseForLimitRqt orderBaseBatchRqt = new BatchOrderBaseForLimitRqt();
        orderBaseBatchRqt.setOrderIds(orderIds);
        orderBaseBatchRqt.setOrderNos(orderNos);
        orderBaseBatchRqt.setGlobalOrderTraceIds(globalOrderTraceIds);
        return callGateway.catchLog(() -> normalOrderListApi.batchGetOrderBaseForLimit(orderBaseBatchRqt), "batchGetOrderBaseForLimit",orderBaseBatchRqt);
    }

    @Override
    public List<OrderBaseComposite> getOrderBaseCompositeBatch(List<Long> orderIds) {
        return getOrderBaseCompositeBatch(orderIds, null);
    }


    @Override
    public List<OrderBaseComposite> getOrderBaseCompositeBatch(List<Long> orderIds, Long masterId) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        OrderBaseBatchRqt orderBaseBatchRqt = new OrderBaseBatchRqt();
        orderBaseBatchRqt.setOrderIds(orderIds);
        List<OrderBaseComposite> orderBaseComposites = callGateway.catchLogThrow(() -> normalOrderListApi.getOrderBaseCompositeBatch(orderBaseBatchRqt));
        if (CollectionUtils.isEmpty(orderBaseComposites)) {
            return new ArrayList<>();
        }
        return orderBaseComposites;
    }

    @Override
    public List<BranchGetOrderPartsResp> branchGetOrderPartsList(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        BranchGetOrderPartsReq branchGetOrderPartsReq = new BranchGetOrderPartsReq();
        branchGetOrderPartsReq.setOrderIds(orderIds);
        List<BranchGetOrderPartsResp> orderPartsRespList = callGateway.catchLogThrow(() -> normalOrderListApi.branchGetOrderPartsList(branchGetOrderPartsReq));
        if (CollectionUtils.isEmpty(orderPartsRespList)) {
            return new ArrayList<>();
        }
        return orderPartsRespList;
    }

    @Override
    public OrderBaseComposite getOrderBaseComposite(Long orderId) {
        return callGateway.catchLogThrow(()->normalOrderResourceApi.getOrderBaseComposite(orderId,null));
    }

    @Override
    public OrderBaseSampleComposite orderSampleDetailByOrderId(Long orderId) {
        if (Objects.isNull(orderId)) {
            return null;
        }
        return callGateway.catchLogThrow(() -> normalOrderDetailApi.orderSampleDetail(null, orderId));
    }

    @Override
    public SimpleOrderGrab getSimpleOrderGrabByOrderId(Long orderId) {
        if (Objects.isNull(orderId)) {
            return null;
        }
        return callGateway.catchLogThrow(() -> normalOrderResourceApi.getSimpleOrderGrab(orderId, null, null));
    }

    @Override
    public List<SimpleOrderGrab> getSimpleOrderGrabByOrderId(BatchGetOrderParam batchGetOrderParam) {
        if (Objects.isNull(batchGetOrderParam.getOrderIds())) {
            return null;
        }
        return callGateway.catchLogThrow(() -> normalOrderListApi.getBatchGetSimpleOrderGrab(batchGetOrderParam));
    }

    @Override
    public SimpleOrderGrab getSimpleOrderGrabByGlobalId(Long globalOrderTraceId) {
        if (Objects.isNull(globalOrderTraceId)) {
            return null;
        }
        return callGateway.catchLogThrow(() -> normalOrderResourceApi.getSimpleOrderGrab(null, null, globalOrderTraceId));
    }

    @Override
    public OrderBaseSampleComposite orderSampleDetailByGlobalId(Long globalOrderTraceId) {
        if (Objects.isNull(globalOrderTraceId)) {
            return null;
        }
        return callGateway.catchLogThrow(() -> normalOrderDetailApi.orderSampleDetail(globalOrderTraceId, null));
    }

    @Override
    public OrderInfoBatchComposite getOrderInfoBatchComposite(List<Long> orderIds, Long masterId) {
        if(CollectionUtils.isEmpty(orderIds) || Objects.isNull(masterId)){
            return null;
        }
        BatchGetOrderInfoRqt batchGetOrderInfoRqt = new BatchGetOrderInfoRqt();
        batchGetOrderInfoRqt.setMasterId(masterId);
        batchGetOrderInfoRqt.setOrderIds(orderIds);
        return callGateway.catchLogThrow(() -> normalOrderListApi.getOrderInfoBatchComposite(batchGetOrderInfoRqt));
    }

    @Override
    public OrderInfoBatchComposite getOrderInfoBatchComposite(List<Long> orderIds) {
        if(CollectionUtils.isEmpty(orderIds)){
            return null;
        }

        if (orderIds.size() > 20) {
            throw new BusException("最大查询20条订单信息！");
        }
        BatchGetOrderInfoRqt batchGetOrderInfoRqt = new BatchGetOrderInfoRqt();
        batchGetOrderInfoRqt.setOrderIds(orderIds);
        return callGateway.catchLogThrow(() -> normalOrderListApi.getOrderInfoBatchComposite(batchGetOrderInfoRqt));
    }

    @Override
    public List<InfoOrderBaseComposite> batchGetInfoOrderBaseComposite(List<Long> orderIds) {
        BatchGetInfoOrderBaseRqt batchGetInfoOrderBaseRqt = new BatchGetInfoOrderBaseRqt();
        batchGetInfoOrderBaseRqt.setOrderIds(orderIds);
        return callGateway.catchLogThrow(() -> infoOrderResourceApi.batchGetInfoOrderBaseComposite(batchGetInfoOrderBaseRqt));
    }

    @Override
    public List<BatchGetOrderInfoExtraResp> getBatchGetOrderInfoExtraResp(List<Long> orderIds, Long masterId) {
        BatchGetOrderInfoRqt batchGetInfoOrderBaseRqt = new BatchGetOrderInfoRqt();
        batchGetInfoOrderBaseRqt.setMasterId(masterId);
        batchGetInfoOrderBaseRqt.setOrderIds(orderIds);
        return callGateway.catchLogThrow(() -> normalOrderListApi.getBatchGetOrderInfoExtraResp(batchGetInfoOrderBaseRqt));
    }

    @Override
    public List<BatchOrderGoodsResp> getOrderGoodsBatch(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        String orderIdStr = orderIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        return callGateway.catchLogThrow(() -> normalOrderListApi.getOrderGoodsBatch(orderIdStr));
    }

    @Override
    public List<OrderBase> selectNearbyOrderListByThreeDays(Long orderId, Integer dayNumber) {
        if (Objects.isNull(orderId)) {
            return new ArrayList<>();
        }
        GetNearByOrderListRqt getNearByOrderListRqt = new GetNearByOrderListRqt();
        getNearByOrderListRqt.setOrderId(orderId);
        getNearByOrderListRqt.setDayNumber(dayNumber);
        return callGateway.catchLogThrow(() -> normalOrderListApi.getNearbyOrderListByThreeDays(getNearByOrderListRqt));
    }

    @Override
    public List<OrderExtraData> batchGetOrderExtraData(List<Long> orderIds) {
        String orderIdStr = orderIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        return callGateway.catchLogThrow(() -> normalOrderListApi.batchGetOrderExtraData(orderIdStr));
    }

    @Override
    public PushingOrderCompositeResp getPushingOrderCompositeResp(Long orderId) {
        GetPushingOrderCompositeReq req = new GetPushingOrderCompositeReq();
        req.setOrderId(orderId);
        return callGateway.catchLogThrow(()->offerModuleResourceApi.getPushingOrderComposite(req));
    }


    @Override
    public List<OrderExclusiveTagResp> getOrderExclusiveTag(Long orderId, Long masterId) {
        return callGateway.catchLog(()-> normalOrderResourceApi.getOrderExclusiveTag(orderId,masterId));
    }

    @Override
    public OrderExclusiveTagResp getOrderExclusiveTag(Long orderId, Long masterId, String tagType) {
        List<OrderExclusiveTagResp> orderExclusiveTag = getOrderExclusiveTag(orderId, masterId);
        if (CollectionUtils.isEmpty(orderExclusiveTag)) {
            return null;
        }
        if (StringUtils.isEmpty(tagType)) {
            return null;
        }
        return orderExclusiveTag.stream().filter(it-> it.getTagType().equals(tagType)).findFirst().orElse(null);
    }

    @Override
    public GetInfoOrderBaseCompositeResp getInfoOrderBase(Long orderId) {
        GetInfoOrderBaseCompositeRqt rqt = new GetInfoOrderBaseCompositeRqt();
        rqt.setOrderId(orderId);
        GetInfoOrderBaseCompositeResp infoOrderBaseComposite = callGateway.catchLogThrow(()-> infoOrderResourceApi.getInfoOrderBaseComposite(rqt));
        if (Objects.isNull(infoOrderBaseComposite)) {
            throw new BusException("获取订单信息失败");
        }
        return infoOrderBaseComposite;
    }

    @Override
    public Integer updateInfoOrderFirstPushNumber(Long orderId, Integer number) {
        UpdateOrderPushedNumberReq req = new UpdateOrderPushedNumberReq();
        req.setOrderId(orderId);
        req.setPushNumber(number);
        return callGateway.catchLog( ()->infoOrderOperationApi.updateInfoOrderFirstPushNumber(req),"updateInfoOrderFirstPushNumber",req);
    }

    @Override
    @Async
    public void clearOrderOfferPriceEndList(ClearPriceEndRqt clearPriceEndRqt) {
        if(CollectionUtils.isEmpty(clearPriceEndRqt.getOrderOfferPriceEndList()) && CollectionUtils.isEmpty(clearPriceEndRqt.getOrderOfferPriceFailReasonList())){
            return;
        }
        callGateway.catchLog(() -> offerModuleOperateApi.clearPriceEnd(clearPriceEndRqt), "师傅修改服务地区：清除orderPush插入OrderOfferPriceEnd，method=clearPriceEnd", clearPriceEndRqt);
    }

    @Override
    public void batchInsertInfoMasterOrderCloseInfo(BatchInsertInfoMasterOrderCloseRqt batchInsertInfoMasterOrderCloseRqt) {
        if(CollectionUtils.isEmpty(batchInsertInfoMasterOrderCloseRqt.getInfoMasterOrderCloseList())){
            return;
        }
        callGateway.catchLog( ()->infoOrderOperationApi.batchInsertInfoMasterOrderCloseInfo(batchInsertInfoMasterOrderCloseRqt),"batchInsertInfoMasterOrderCloseInfo",batchInsertInfoMasterOrderCloseRqt);
    }
}
