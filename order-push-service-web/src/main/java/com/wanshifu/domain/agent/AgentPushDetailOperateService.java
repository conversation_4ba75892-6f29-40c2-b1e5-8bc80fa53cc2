package com.wanshifu.domain.agent;

import com.wanshifu.order.push.request.agent.UpdateAgentByDisinterestOrderRqt;
import com.wanshifu.order.push.request.agent.UpdateAgentByGrabRqt;
import com.wanshifu.order.push.request.agent.UpdateAgentByOfferPriceRqt;
import com.wanshifu.order.push.request.agent.ViewOrderRqt;


/**
 * <AUTHOR>
 * @description
 * @date 2024/3/11 14:53
 */
public interface AgentPushDetailOperateService {

    /**
     * 查看订单
     * @param viewOrderRqt
     */
    void viewOrder(ViewOrderRqt viewOrderRqt);


    /**
     * 报价
     * @param updateAgentByOfferPriceRqt
     */
    void offerPrice(UpdateAgentByOfferPriceRqt updateAgentByOfferPriceRqt);

    /**
     * 一口价抢单
     * @param updateAgentByGrabRqt
     */
    void grabOrder(UpdateAgentByGrabRqt updateAgentByGrabRqt);


    /**
     * 师傅不感兴趣订单
     * @param updateAgentByDisinterestOrderRqt
     */
    void disinterestOrder(UpdateAgentByDisinterestOrderRqt updateAgentByDisinterestOrderRqt);
}
