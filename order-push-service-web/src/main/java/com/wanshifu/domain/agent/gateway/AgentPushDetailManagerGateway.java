package com.wanshifu.domain.agent.gateway;

import com.wanshifu.domain.push.model.AgentPushMaster;
import org.springframework.scheduling.annotation.Async;

import java.util.Date;
import java.util.List;

public interface AgentPushDetailManagerGateway {


    /**
     * 添加代理商推送记录表
     * @param orderId
     * @param agentPushMasterSet
     * @param nobodyOfferHour
     * @param pushTime
     */

    void addPushRecording(Long orderId, List<AgentPushMaster> agentPushMasterSet,
                          Integer nobodyOfferHour, Date pushTime);


    /**
     * 查看订单
     * @param orderId
     * @param masterId
     * @param viewTime
     */
    void viewOrder(Long orderId,Long masterId,Date viewTime);


    /**
     * 报价
     * @param orderId
     * @param masterId
     * @param offerTime
     */
    void offerPrice(Long orderId,Long masterId,Date offerTime);

    /**
     * 一口价抢单
     * @param orderId
     * @param masterId
     * @param grabTime
     */
    void grabOrder(Long orderId,Long masterId,Date grabTime);


    /**
     * 师傅不感兴趣订单
     * @param orderId
     * @param masterId
     * @param abandonTime
     */
    void disinterestOrder(Long orderId,Long masterId,Date abandonTime);
}
