package com.wanshifu.domain.agent.serviceImpl;

import com.wanshifu.domain.agent.AgentPushDetailOperateService;
import com.wanshifu.domain.agent.gateway.AgentPushDetailManagerGateway;
import com.wanshifu.order.push.request.agent.UpdateAgentByDisinterestOrderRqt;
import com.wanshifu.order.push.request.agent.UpdateAgentByGrabRqt;
import com.wanshifu.order.push.request.agent.UpdateAgentByOfferPriceRqt;
import com.wanshifu.order.push.request.agent.ViewOrderRqt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/11 14:59
 */
@Service
public class AgentPushDetailOperateServiceImpl implements AgentPushDetailOperateService {


    @Resource
    private AgentPushDetailManagerGateway agentPushDetailManagerGateway;

    @Resource
    private Executor otherBusDiscardPolicyExecutor;

    @Override
    public void viewOrder(ViewOrderRqt viewOrderRqt) {
        CompletableFuture.runAsync(() ->   agentPushDetailManagerGateway.viewOrder(viewOrderRqt.getOrderId(), viewOrderRqt.getMasterId(), viewOrderRqt.getViewTime()), otherBusDiscardPolicyExecutor);
    }

    @Override
    public void offerPrice(UpdateAgentByOfferPriceRqt updateAgentByOfferPriceRqt) {
        CompletableFuture.runAsync(() ->agentPushDetailManagerGateway.offerPrice(updateAgentByOfferPriceRqt.getOrderId(), updateAgentByOfferPriceRqt.getMasterId(), updateAgentByOfferPriceRqt.getOfferTime()), otherBusDiscardPolicyExecutor);
    }

    @Override
    public void grabOrder(UpdateAgentByGrabRqt updateAgentByGrabRqt) {
        CompletableFuture.runAsync(() ->agentPushDetailManagerGateway.grabOrder(updateAgentByGrabRqt.getOrderId(), updateAgentByGrabRqt.getMasterId(), updateAgentByGrabRqt.getGrabTime()), otherBusDiscardPolicyExecutor);
    }

    @Override
    public void disinterestOrder(UpdateAgentByDisinterestOrderRqt updateAgentByDisinterestOrderRqt) {
        CompletableFuture.runAsync(() ->agentPushDetailManagerGateway.disinterestOrder(updateAgentByDisinterestOrderRqt.getOrderId(), updateAgentByDisinterestOrderRqt.getMasterId(), updateAgentByDisinterestOrderRqt.getAbandonTime()), otherBusDiscardPolicyExecutor);
    }
}
