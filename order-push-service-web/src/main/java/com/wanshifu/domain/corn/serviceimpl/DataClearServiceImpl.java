package com.wanshifu.domain.corn.serviceimpl;

import com.wanshifu.domain.corn.DataClearService;
import com.wanshifu.domain.corn.gateway.OrderMqLogGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @<PERSON> <PERSON><PERSON><PERSON>@wshifu.com
 * @Date 2023-11-01 10:19
 * @Description
 * @Version v1
 **/
@Slf4j
@Service
public class DataClearServiceImpl implements DataClearService {

    @Resource
    private OrderMqLogGateway orderMqLogGateway;
    //每次清理1万条
    @Value("${order.clear.branch_number:10000}")
    private Long branch_number;
    @Value("${order.clear.every_time_sum_number:1000000}")
    private Long every_time_sum_number;
    @Value("${order.clear.max_log_Number:2000000}")
    private Long max_log_Number;


    @Override
    public Integer clearOrderMqLog() {
        log.info("清除参数配置:branch_number:{},every_time_sum_number:{},max_log_Number:{}",branch_number,every_time_sum_number,max_log_Number);
        return  orderMqLogGateway.clear(branch_number,every_time_sum_number,max_log_Number);
    }
}
