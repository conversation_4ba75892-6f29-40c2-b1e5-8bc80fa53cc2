package com.wanshifu.domain.corn.controller;

import com.wanshifu.domain.corn.DataClearService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023-11-01 10:15
 * @Description 计划任务定期数据清理
 * @Version v1
 **/

@RestController
@RequestMapping("data/clear")
public class DataClearController {

    @Resource
    private DataClearService dataClearService;


    @PostMapping("clearOrderMqLog")
    public Integer clearOrderMqLog(){
        return dataClearService.clearOrderMqLog();
    }
}
