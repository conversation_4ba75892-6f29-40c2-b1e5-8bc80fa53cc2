package com.wanshifu.domain.base.ability;

import com.alibaba.fastjson.JSON;
import com.wanshifu.domain.base.tools.BeanEnhanceUtil;
import com.wanshifu.domain.push.model.SiteOrderDetailMasterListBo;
import com.wanshifu.domain.push.model.WaitOfferNoPageRespBo;
import com.wanshifu.domain.push.model.WaitOfferV2RespBo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.IkeaOrderGoodsComposite;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.OrderGoodsComposite;
import com.wanshifu.order.offer.domains.po.*;
import com.wanshifu.order.offer.domains.vo.infoorder.InfoOrderGoodsComposite;
import com.wanshifu.order.push.domains.dto.*;
import com.wanshifu.order.push.enums.SiteOrderDetailMasterListMasterMainLabelEnum;
import com.wanshifu.order.push.enums.SiteOrderDetailMasterListMasterSubLabelEnum;
import com.wanshifu.order.push.response.SiteOrderDetailMasterListResp;
import com.wanshifu.order.push.response.WaitOfferNoPageResp;
import com.wanshifu.order.push.response.WaitOfferV2Resp;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/23 15:58
 */

public class DTOFormatAbilityService {

    public static WaitOfferV2Resp getWaitOfferV2Resp(WaitOfferV2RespBo waitOfferV2RespBo) {
        WaitOfferV2Resp waitOfferV2Resp = new WaitOfferV2Resp();
        waitOfferV2Resp.setAppointType(waitOfferV2RespBo.getAppointType());
        waitOfferV2Resp.setExposurePositionFlag(waitOfferV2RespBo.getExposurePositionFlag());
        waitOfferV2Resp.setOfferNumber(waitOfferV2RespBo.getMaxOfferNumber());
        waitOfferV2Resp.setImageCount(waitOfferV2RespBo.getImageCount());
        waitOfferV2Resp.setHasVideo(waitOfferV2RespBo.isHasVideo());
        waitOfferV2Resp.setIsIntention(waitOfferV2RespBo.getIsIntention());
        waitOfferV2Resp.setRemainOfferNumber(waitOfferV2RespBo.getRemainOfferNumber());
        waitOfferV2Resp.setStopOfferTime(waitOfferV2RespBo.getStopOfferTime());
        waitOfferV2Resp.setMaxOfferNumber(waitOfferV2RespBo.getMaxOfferNumber());
        waitOfferV2Resp.setRateAwardFee(waitOfferV2RespBo.getRateAwardFee());
        waitOfferV2Resp.setPushDistance(waitOfferV2RespBo.getPushDistance());
        waitOfferV2Resp.setPushDistanceType(waitOfferV2RespBo.getPushDistanceType());
        waitOfferV2Resp.setFirstPullTime(waitOfferV2RespBo.getFirstPullTime());
        waitOfferV2Resp.setPushTime(waitOfferV2RespBo.getPushTime());
        waitOfferV2Resp.setOrderBaseDTO(copyObject(waitOfferV2RespBo.getOrderBase(), OrderBaseDTO::new));
        waitOfferV2Resp.setOrderExtraData(copyObject(waitOfferV2RespBo.getOrderExtraData(), OrderExtraDataDTO::new));
        waitOfferV2Resp.setOrderAwardDTO(copyObject(waitOfferV2RespBo.getOrderAward(), OrderAwardDTO::new));
        waitOfferV2Resp.setOrderEpcGenreDataDTO(copyObject(waitOfferV2RespBo.getOrderEpcGenreData(), OrderEpcGenreDataDTO::new));
        waitOfferV2Resp.setOrderReturnLogisticsDTO(copyObject(waitOfferV2RespBo.getOrderReturnLogistics(), OrderReturnLogisticsDTO::new));
        waitOfferV2Resp.setOrderLogisticsInfoDTO(copyObject(waitOfferV2RespBo.getOrderLogisticsInfo(), OrderLogisticsInfoDTO::new));
        waitOfferV2Resp.setOrderInitFeeDTO(copyObject(waitOfferV2RespBo.getOrderInitFee(), OrderInitFeeDTO::new));
        waitOfferV2Resp.setInfoOrderBaseDTO(copyObject(waitOfferV2RespBo.getInfoOrderBase(), InfoOrderBaseDTO::new));
        waitOfferV2Resp.setInfoOrderExtraDataDTO(copyObject(waitOfferV2RespBo.getInfoOrderExtraData(), InfoOrderExtraDataDTO::new));
        waitOfferV2Resp.setInfoOrderAttachmentDTOS(buildInfoOrderAttachmentDTOS(waitOfferV2RespBo.getInfoOrderAttachment()));
        waitOfferV2Resp.setInfoOrderGoodsCompositeDTOS(buildInfoOrderGoodsCompositeDTOS(waitOfferV2RespBo.getInfoOrderGoodsComposite()));
        waitOfferV2Resp.setIkeaOrderGoodsCompositeDTOS(buildIkeaOrderGoodsCompositeDTOS(waitOfferV2RespBo.getIkeaOrderGoodsComposites()));
        waitOfferV2Resp.setOrderGoodsCompositeDTOS(buildOrderGoodsCompositeDTOS(waitOfferV2RespBo.getOrderGoodsComposites()));
        waitOfferV2Resp.setEmergencyOrderDTO(copyObject(waitOfferV2RespBo.getEmergencyOrder(), EmergencyOrderDTO::new));
        waitOfferV2Resp.setOrderServiceAttributeInfoDTOS(buildOrderServiceAttributeInfoDTOS(waitOfferV2RespBo.getOrderServiceAttributeInfos()));
        waitOfferV2Resp.setIsAgentOrder(waitOfferV2RespBo.getIsAgentOrder());
        waitOfferV2Resp.setPushNoticeLabel(waitOfferV2RespBo.getPushNoticeLabel());
        waitOfferV2Resp.setIsPullOrderDistance(waitOfferV2RespBo.getIsPullOrderDistance());
        waitOfferV2Resp.setIsClearOrderDistance(waitOfferV2RespBo.getIsClearOrderDistance());
        waitOfferV2Resp.setOrderPayStatus(waitOfferV2RespBo.getOrderPayStatus());
        waitOfferV2Resp.setLessContendFlag(waitOfferV2RespBo.getLessContendFlag());
        waitOfferV2Resp.setOrderExclusiveTagDTOS(buildOrderExclusiveTagDTOS(waitOfferV2RespBo.getOrderTagResp()));
        waitOfferV2Resp.setOrderAddItemServiceInfoDTOList(buildOrderAddItemServiceInfoDTOList(waitOfferV2RespBo.getOrderAddItemServiceInfoList()));
        waitOfferV2Resp.setOrderAutoGrabDTOList(buildOrderAutoGrabDTOList(waitOfferV2RespBo.getOrderAutoGrabList()));
        waitOfferV2Resp.setOrderPartsDTOList(buildOrderPartsDTOList(waitOfferV2RespBo.getOrderPartsList()));
        waitOfferV2Resp.setIsPrePayOrderBeforeAppoint(waitOfferV2RespBo.getIsPrePayOrderBeforeAppoint());
        waitOfferV2Resp.setSecondDivisionId(waitOfferV2RespBo.getSecondDivisionId());
        waitOfferV2Resp.setRemainRecommendOfferNum(waitOfferV2RespBo.getRemainRecommendOfferNum());
        waitOfferV2Resp.setMenuCategory(waitOfferV2RespBo.getMenuCategory());
        waitOfferV2Resp.setOrderBountyInfo(copyObject(waitOfferV2RespBo.getOrderBountyInfo(), OrderBountyInfoDTO::new));
        return waitOfferV2Resp;
    }

    public static SiteOrderDetailMasterListResp getSiteOrderDetailMasterListResp(SiteOrderDetailMasterListBo siteOrderDetailMasterListBo) {
        SiteOrderDetailMasterListResp resp = new SiteOrderDetailMasterListResp();
        resp.setMasterId(siteOrderDetailMasterListBo.getMasterId());

        String mainLabel = getMasterMainLabelBySiteOrderDetailMasterListBo(siteOrderDetailMasterListBo);
        resp.setMasterMainLabel(mainLabel);

        if (SiteOrderDetailMasterListMasterMainLabelEnum.SAVE_MONEY.getLabelName().equals(mainLabel)) {
            String subLabel = getMasterSubLabelBySiteOrderDetailMasterListBo(siteOrderDetailMasterListBo);
            resp.setMasterSubLabel(subLabel);
        }

        resp.setPushDistance(siteOrderDetailMasterListBo.getPushDistance());

        resp.setPushScore(siteOrderDetailMasterListBo.getPushScore());
        resp.setUserIdIsCollectMaster(siteOrderDetailMasterListBo.getUserIdIsCollectMaster());
        resp.setUserIdIsCooperation(siteOrderDetailMasterListBo.getUserIdIsCooperation());
        resp.setServeLv2IsGoodService(siteOrderDetailMasterListBo.getServeLv2IsGoodService());
        resp.setMasterSecondDivisionIdIsSaveMoneyMaster(siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsSaveMoneyMaster());
        resp.setServeLv3FinishOrderCnt(siteOrderDetailMasterListBo.getServeLv3FinishOrderCnt());
        resp.setMasterSecondDivisionIdIsUsualGrabOrder(siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsUsualGrabOrder());
        resp.setMasterSecondDivisionIdIsUsualOfferLowPrice(siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsUsualOfferLowPrice());
        resp.setMasterSecondDivisionIdLatest90DayLowOfferCnt(siteOrderDetailMasterListBo.getMasterSecondDivisionIdLatest90DayLowOfferCnt());
        resp.setMasterSecondDivisionIdRankByLatest90DayLowOfferCnt(siteOrderDetailMasterListBo.getMasterSecondDivisionIdRankByLatest90DayLowOfferCnt());
        resp.setMasterThirdDivisionIdServeLv3IdRankByFinishOrderCnt(siteOrderDetailMasterListBo.getMasterThirdDivisionIdServeLv3IdRankByFinishOrderCnt());
        return resp;
    }

    /**
     *   若师傅同时命中多个标签，只展示一个，优先级：收藏师傅 > 合作过 > 服务好 > 省钱师傅 > 普通
     *
     *
     * @param siteOrderDetailMasterListBo
     * @return
     */
    public static String getMasterMainLabelBySiteOrderDetailMasterListBo(SiteOrderDetailMasterListBo siteOrderDetailMasterListBo) {
        String defaultLabel = SiteOrderDetailMasterListMasterMainLabelEnum.NORMAL.getLabelName();
        if(siteOrderDetailMasterListBo == null){
            return defaultLabel;
        }
        //师傅标签展示优先级
        if (Objects.nonNull(siteOrderDetailMasterListBo.getUserIdIsCollectMaster())
                && siteOrderDetailMasterListBo.getUserIdIsCollectMaster() == 1) {
            //收藏师傅
            return SiteOrderDetailMasterListMasterMainLabelEnum.COLLECT_MASTER.getLabelName();
        } else if (Objects.nonNull(siteOrderDetailMasterListBo.getUserIdIsCooperation())
                && siteOrderDetailMasterListBo.getUserIdIsCooperation() == 1) {
            //合作过
            return SiteOrderDetailMasterListMasterMainLabelEnum.COOPERATION_MASTER.getLabelName();
        } else if (Objects.nonNull(siteOrderDetailMasterListBo.getServeLv2IsGoodService())
                && siteOrderDetailMasterListBo.getServeLv2IsGoodService() == 1) {

            //服务好
            return SiteOrderDetailMasterListMasterMainLabelEnum.GOOD_SERVICE.getLabelName();
        } else if (Objects.nonNull(siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsSaveMoneyMaster())
                && siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsSaveMoneyMaster() == 1) {

            //省钱师傅
            return SiteOrderDetailMasterListMasterMainLabelEnum.SAVE_MONEY.getLabelName();
        } else {
            return defaultLabel;
        }
    }

    /**
     *
     * 返回省钱师傅主标签时，需返回  常报最低价/常抢一口价/距离近  的子标签，若师傅同时命中多个子标签，只返回一个，优先级：常报最低价 > 常抢一口价 > 距离近
     *
     * @param siteOrderDetailMasterListBo
     * @return
     */
    public static String getMasterSubLabelBySiteOrderDetailMasterListBo(SiteOrderDetailMasterListBo siteOrderDetailMasterListBo) {

        if (Objects.nonNull(siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsUsualOfferLowPrice())
                && siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsUsualOfferLowPrice() == 1) {
            //常报最低价
            return SiteOrderDetailMasterListMasterSubLabelEnum.USUAL_OFFER_LOW_PRICE.getLabelName();
        } else if (Objects.nonNull(siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsUsualGrabOrder())
                && siteOrderDetailMasterListBo.getMasterSecondDivisionIdIsUsualGrabOrder() == 1) {
            //常抢一口价
            return SiteOrderDetailMasterListMasterSubLabelEnum.USUAL_GRAB_ORDER.getLabelName();
        } else {
            //距离近
            return SiteOrderDetailMasterListMasterSubLabelEnum.NEARBY_MASTER.getLabelName();
        }
    }

    public static List<OrderAddItemServiceInfoDTO> buildOrderAddItemServiceInfoDTOList(List<OrderAddItemServiceInfo> orderAddItemServiceInfoList) {
        if(CollectionUtils.isEmpty(orderAddItemServiceInfoList)){
            return new ArrayList<>();
        }
        return orderAddItemServiceInfoList.stream().map(orderAddItemServiceInfo -> copyObject(orderAddItemServiceInfo, OrderAddItemServiceInfoDTO::new)).collect(Collectors.toList());
    }

    public static List<OrderAutoGrabDTO> buildOrderAutoGrabDTOList(List<OrderAutoGrab> orderAutoGrabList) {
        if(CollectionUtils.isEmpty(orderAutoGrabList)){
            return new ArrayList<>();
        }
        return orderAutoGrabList.stream().map(orderAutoGrab -> copyObject(orderAutoGrab, OrderAutoGrabDTO::new)).collect(Collectors.toList());
    }

    public static List<OrderPartsDTO> buildOrderPartsDTOList(List<OrderParts> orderPartsList) {
        if(CollectionUtils.isEmpty(orderPartsList)){
            return new ArrayList<>();
        }
        return orderPartsList.stream().map(orderParts -> copyObject(orderParts, OrderPartsDTO::new)).collect(Collectors.toList());
    }

    public static List<WaitOfferNoPageResp> getWaitOfferNoPageRespList(List<WaitOfferNoPageRespBo> waitOfferNoPageRespBos){
        if(CollectionUtils.isEmpty(waitOfferNoPageRespBos)){
            return new ArrayList<>();
        }
        return waitOfferNoPageRespBos.stream().map(waitOfferNoPageRespBo -> {
            WaitOfferNoPageResp waitOfferNoPageResp = new WaitOfferNoPageResp();
//            waitOfferNoPageResp.setOrderLogisticsInfoDTO();
            waitOfferNoPageResp.setOrderExtraDataDTO(copyObject(waitOfferNoPageRespBo.getOrderExtraData(), OrderExtraDataDTO::new));
            waitOfferNoPageResp.setOrderBaseDTO(copyObject(waitOfferNoPageRespBo.getOrderBase(), OrderBaseDTO::new));
            waitOfferNoPageResp.setOrderFirstImageAid(waitOfferNoPageRespBo.getOrderFirstImageAid());
            return waitOfferNoPageResp;
        }).collect(Collectors.toList());
    }

    public static  List<OrderExclusiveTagDTO> buildOrderExclusiveTagDTOS(List<OrderExclusiveTagResp> orderTagResp) {
        if(CollectionUtils.isEmpty(orderTagResp)){
            return new ArrayList<>();
        }
        return orderTagResp.stream().map(tag -> {
            OrderExclusiveTagDTO orderExclusiveTagDTO = new OrderExclusiveTagDTO();
            orderExclusiveTagDTO.setTagValue(tag.getTagValue());
            orderExclusiveTagDTO.setTagName(tag.getTagName());
            orderExclusiveTagDTO.setTagType(tag.getTagType());
            orderExclusiveTagDTO.setDisplayType(tag.getDisplayType());
            orderExclusiveTagDTO.setOrderId(tag.getOrderId());
            orderExclusiveTagDTO.setIsDynamicTag(tag.getIsDynamicTag());
            orderExclusiveTagDTO.setLv1TagDesc(tag.getLv1TagDesc());
            return orderExclusiveTagDTO;
        }).collect(Collectors.toList());
    }

    public static  List<OrderServiceAttributeInfoDTO> buildOrderServiceAttributeInfoDTOS(List<OrderServiceAttributeInfo> orderServiceAttributeInfos) {
        if(CollectionUtils.isEmpty(orderServiceAttributeInfos)){
            return new ArrayList<>();
        }
        return orderServiceAttributeInfos.stream().map(orderServiceAttributeInfo -> copyObject(orderServiceAttributeInfo, OrderServiceAttributeInfoDTO::new)).collect(Collectors.toList());
    }

    public static  List<OrderGoodsCompositeDTO> buildOrderGoodsCompositeDTOS(List<OrderGoodsComposite> orderGoodsComposites) {
        if(CollectionUtils.isEmpty(orderGoodsComposites)){
            return new ArrayList<>();
        }

        return orderGoodsComposites.stream().map(orderGoodsComposite -> {
            OrderGoodsCompositeDTO orderGoodsCompositeDTO = new OrderGoodsCompositeDTO();
            orderGoodsCompositeDTO.setOrderGoodsDTO(copyObject(orderGoodsComposite.getOrderGoods(), OrderGoodsDTO::new));
            orderGoodsCompositeDTO.setOrderGoodsImageRelaDTOS(buildOrderGoodsImageRelaDTOS(orderGoodsComposite.getOrderGoodsImageRelas()));
            orderGoodsCompositeDTO.setDefiniteGoodsPriceDTO(copyObject(orderGoodsComposite.getDefiniteGoodsPrice(), DefiniteGoodsPriceDTO::new));
            orderGoodsCompositeDTO.setOrderGoodsDoorHoleInfoDTOS(buildOrderGoodsDoorHoleInfoDTOS(orderGoodsComposite.getOrderGoodsDoorHoleInfos()));
            return orderGoodsCompositeDTO;
        }).collect(Collectors.toList());
    }

    public static  List<OrderGoodsDoorHoleInfoDTO> buildOrderGoodsDoorHoleInfoDTOS(List<OrderGoodsDoorHoleInfo> orderGoodsDoorHoleInfos) {
        if(CollectionUtils.isEmpty(orderGoodsDoorHoleInfos)){
            return new ArrayList<>();
        }
        return orderGoodsDoorHoleInfos.stream().map(orderGoodsDoorHoleInfo -> copyObject(orderGoodsDoorHoleInfo, OrderGoodsDoorHoleInfoDTO::new)).collect(Collectors.toList());
    }

    public static  List<OrderGoodsImageRelaDTO> buildOrderGoodsImageRelaDTOS(List<OrderGoodsImageRela> orderGoodsImageRelas) {
        if(CollectionUtils.isEmpty(orderGoodsImageRelas)){
            return new ArrayList<>();
        }
        return orderGoodsImageRelas.stream().map(orderGoodsImageRela -> copyObject(orderGoodsImageRela, OrderGoodsImageRelaDTO::new)).collect(Collectors.toList());
    }

    public static  List<IkeaOrderGoodsCompositeDTO> buildIkeaOrderGoodsCompositeDTOS(List<IkeaOrderGoodsComposite> ikeaOrderGoodsComposites) {
        if(CollectionUtils.isEmpty(ikeaOrderGoodsComposites)){
            return new ArrayList<>();
        }

        return ikeaOrderGoodsComposites.stream().map(ikeaOrderGoodsComposite -> {
            IkeaOrderGoodsCompositeDTO ikeaOrderGoodsCompositeDTO = new IkeaOrderGoodsCompositeDTO();
            ikeaOrderGoodsCompositeDTO.setOrderIkeaGoodsDTO(copyObject(ikeaOrderGoodsComposite.getOrderIkeaGoods(), OrderIkeaGoodsDTO::new));
            ikeaOrderGoodsCompositeDTO.setIkeaGoodsAdditionalDTOS(buildIkeaGoodsAdditionalDTOS(ikeaOrderGoodsComposite.getIkeaGoodsAdditionals()));
            ikeaOrderGoodsCompositeDTO.setIkeaGoodsImageRelaDTOS(buildIkeaGoodsImageRelaDTOS(ikeaOrderGoodsComposite.getIkeaGoodsImageRelas()));
            return ikeaOrderGoodsCompositeDTO;
        }).collect(Collectors.toList());
    }

    public static  List<OrderIkeaGoodsImageRelaDTO> buildIkeaGoodsImageRelaDTOS(List<OrderIkeaGoodsImageRela> ikeaGoodsImageRelas) {
        if(CollectionUtils.isEmpty(ikeaGoodsImageRelas)){
            return new ArrayList<>();
        }
        return ikeaGoodsImageRelas.stream().map(ikeaGoodsImageRela -> copyObject(ikeaGoodsImageRela, OrderIkeaGoodsImageRelaDTO::new)).collect(Collectors.toList());    }

    public static  List<OrderIkeaGoodsAdditionalDTO> buildIkeaGoodsAdditionalDTOS(List<OrderIkeaGoodsAdditional> ikeaGoodsAdditionals) {
        if(CollectionUtils.isEmpty(ikeaGoodsAdditionals)){
            return new ArrayList<>();
        }
        return ikeaGoodsAdditionals.stream().map(ikeaGoodsAdditional -> copyObject(ikeaGoodsAdditional, OrderIkeaGoodsAdditionalDTO::new)).collect(Collectors.toList());    }

    public static  List<InfoOrderGoodsCompositeDTO> buildInfoOrderGoodsCompositeDTOS(List<InfoOrderGoodsComposite> infoOrderGoodsComposite) {
        if(CollectionUtils.isEmpty(infoOrderGoodsComposite)){
            return new ArrayList<>();
        }

        return infoOrderGoodsComposite.stream().map(infoOrderGoodsComposite1 -> {
            InfoOrderGoodsCompositeDTO infoOrderGoodsCompositeDTO = new InfoOrderGoodsCompositeDTO();
            infoOrderGoodsCompositeDTO.setInfoOrderGoodsDTO(copyObject(infoOrderGoodsComposite1.getInfoOrderGoods(), InfoOrderGoodsDTO::new));
            infoOrderGoodsCompositeDTO.setInfoOrderGoodsAttachmentDTOS(buildInfoOrderGoodsAttachmentDTOS(infoOrderGoodsComposite1.getInfoOrderAttachment()));
            return infoOrderGoodsCompositeDTO;
        }).collect(Collectors.toList());
    }

    public static  List<InfoOrderGoodsAttachmentDTO> buildInfoOrderGoodsAttachmentDTOS(List<InfoOrderGoodsAttachment> infoOrderGoodsAttachments) {
        if(CollectionUtils.isEmpty(infoOrderGoodsAttachments)){
            return new ArrayList<>();
        }
        return infoOrderGoodsAttachments.stream().map(infoOrderGoodsAttachment -> copyObject(infoOrderGoodsAttachment, InfoOrderGoodsAttachmentDTO::new)).collect(Collectors.toList());
    }


    public static  List<InfoOrderAttachmentDTO> buildInfoOrderAttachmentDTOS(List<InfoOrderAttachment> infoOrderAttachment) {
        if(CollectionUtils.isEmpty(infoOrderAttachment)){
            return new ArrayList<>();
        }
        return infoOrderAttachment.stream().map(infoOrderAttachment1 -> copyObject(infoOrderAttachment1, InfoOrderAttachmentDTO::new)).collect(Collectors.toList());
    }


    public static  <B>B copyObject(Object o1 , Supplier<B> supplier) {
        if (Objects.isNull(o1)) {
            return null;
        }
        B b = supplier.get();
        BeanUtils.copyProperties(o1,b);
        return b;
    }

    public static OrderBaseCompositeDTO compositeOrderBaseCompositeDTO(OrderBaseComposite orderBaseComposite){
        if (Objects.isNull(orderBaseComposite)) {
            return new OrderBaseCompositeDTO();
        }
        OrderBaseCompositeDTO orderBaseCompositeDTO = new OrderBaseCompositeDTO();
        orderBaseCompositeDTO.setOrderBaseDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getOrderBase(),OrderBaseDTO::new));
        orderBaseCompositeDTO.setOrderExtraDataDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getOrderExtraData(),OrderExtraDataDTO::new));
        orderBaseCompositeDTO.setOrderLogisticsInfoDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getOrderLogisticsInfo(),OrderLogisticsInfoDTO::new));
        orderBaseCompositeDTO.setOrderGrabDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getOrderGrab(),OrderGrabDTO::new));
        orderBaseCompositeDTO.setOrderInitFeeDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getOrderInitFee(),OrderInitFeeDTO::new));
        orderBaseCompositeDTO.setOrderRateAwardDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getOrderRateAward(),OrderRateAwardDTO::new));
        orderBaseCompositeDTO.setOrderAwardDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getOrderAward(),OrderAwardDTO::new));
        orderBaseCompositeDTO.setOrderEpcGenreDataDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getOrderEpcGenreData(),OrderEpcGenreDataDTO::new));
        orderBaseCompositeDTO.setEmergencyOrderDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getEmergencyOrder(),EmergencyOrderDTO::new));
        orderBaseCompositeDTO.setOrderPartsDTOListList(compositeOrderPartsDTOList(orderBaseComposite.getOrderPartsList()));
        orderBaseCompositeDTO.setOrderFileRelaDTOList(compositeOrderFileRelaDTOList(orderBaseComposite.getOrderFileRelaList()));
        orderBaseCompositeDTO.setOrderCloseDTO(BeanEnhanceUtil.copyBean(orderBaseComposite.getOrderClose(),OrderCloseDTO::new));
        return orderBaseCompositeDTO;
    }


    public static  List<OrderPartsDTO> compositeOrderPartsDTOList(List<OrderParts> orderParts){
        if (CollectionUtils.isEmpty(orderParts)) {
            return null;
        }
        return orderParts.stream().map(it->BeanEnhanceUtil.copyBean(it,OrderPartsDTO::new)).collect(Collectors.toList());
    }

    public static  List<OrderFileRelaDTO> compositeOrderFileRelaDTOList(List<OrderFileRela> orderFileRelas){
        if (CollectionUtils.isEmpty(orderFileRelas)) {
            return null;
        }
        return orderFileRelas.stream().map(it->BeanEnhanceUtil.copyBean(it,OrderFileRelaDTO::new)).collect(Collectors.toList());
    }

}
