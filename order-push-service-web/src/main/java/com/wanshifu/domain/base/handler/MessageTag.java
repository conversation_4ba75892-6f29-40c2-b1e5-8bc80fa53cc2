package com.wanshifu.domain.base.handler;


import com.aliyun.openservices.ons.api.Message;

/**
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>@wshifu.com
 * @Date 2023-04-28 14:11
 * @Description 消息的tag
 * @Version v1
 **/

public interface MessageTag {

     /**
      * 业务逻辑处理
      * @param
      */
     void handler(Message message);

     String businessScene();

     void setBusinessScene(String businessScene);

     default boolean isReconsumeLater(){return Boolean.TRUE;}

     void setReconsumeLate(Boolean isReconsumeLater);

     void setSupportLog(Boolean isSupportLog);

     void setMaxReconsumeTime(Integer time);

     int maxReconsumeTime();
}
