package com.wanshifu.domain.base.consumer;

import com.wanshifu.domain.base.handler.AbstractNormalBusinessTopicMessageTag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023-11-07 11:15
 * @Description 内部-正常业务topic
 * @Version v1
 **/
@Component
public class PrivateNormalBusinessTopic extends AbstractMessageTopic<AbstractNormalBusinessTopicMessageTag> {

    @Value("${wanshifu.rocketMQ.normal-business-order-topic}")
    private String topic;


    @Override
    protected String getTopic() {
        return topic;
    }
}
