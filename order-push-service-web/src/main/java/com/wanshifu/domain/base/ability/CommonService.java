package com.wanshifu.domain.base.ability;

import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.po.OrderBase;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024-03-01 18:01
 * @Description
 * @Version v1
 **/
@Component
public class CommonService {

    /**
     * 判断是否为总包服务商订单
     *
     * @param orderBase 订单基础信息
     * @return boolean
     */
    public boolean isEnterpriseOrder(String accountType) {
        return AccountType.ENTERPRISE.code.equals(accountType);
    }

}
