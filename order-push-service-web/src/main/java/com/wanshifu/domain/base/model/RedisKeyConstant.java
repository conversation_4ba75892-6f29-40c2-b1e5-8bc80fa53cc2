package com.wanshifu.domain.base.model;

/**
 * <AUTHOR>
 * @description redis缓存key常量，方便缓存统一管理
 * @date 2024/5/29 18:17
 */
public class RedisKeyConstant {

    /**
     * 小程序服务redis前缀
     */
    public static final String KEY_PREFIX = "order-push-service:";

    /**
     * 省下级地址id key
     */
    public static String PROVINCE_NEXT_ID_KEY = KEY_PREFIX.concat("provinceNext:");

    /**
     * 师傅家庭样板城市推单标识 key
     */
    public static String MASTER_TMPL_CITY_FLAG_KEY = KEY_PREFIX.concat("masterTmplCityFlag:");

    public static String ORDER_DISTANCE_CONFIG_BTW_CITY_KEY = "order-push-service:btw:";


    public static String ORDER_DISTANCE_CONFIG_KEY = "order-push-service:sideOrderCombinationDistance:%d:%d";


    public static String SIDE_ORDER_COMBINATION_CATEGORY_KEY = "order-push-service:sideOrderCombinationCategory:%d:%d";

    /**
     * 智能排序干预位城市id key
     */
    public static String SMART_EXPOSURE_CITY_ID_KEY = KEY_PREFIX.concat("smartExposureCity:");

    /**
     * 定向推送推单首次查看后无人接单重推时间配置
     */
    public static String AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME_KEY = KEY_PREFIX.concat("agentFirstViewNoHiredRePushTime:");


    /**
     * 订单更新时间
     */
    public static String ORDER_UPDATE_TIME = KEY_PREFIX.concat("orderUpdateTime:");
}
