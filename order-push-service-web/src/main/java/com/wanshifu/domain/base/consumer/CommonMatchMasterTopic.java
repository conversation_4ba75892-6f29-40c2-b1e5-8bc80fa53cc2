package com.wanshifu.domain.base.consumer;

import com.wanshifu.domain.base.handler.AbstractMatchMasterTopicMessageTag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023-11-07 11:15
 * @Description 通用-匹配师傅topic
 * @Version v1
 **/
@Component
public class CommonMatchMasterTopic extends AbstractMessageTopic<AbstractMatchMasterTopicMessageTag> {

    @Value("${wanshifu.rocketMQ.order-match-master-topic}")
    private String topic;


    @Override
    protected String getTopic() {
        return topic;
    }
}
