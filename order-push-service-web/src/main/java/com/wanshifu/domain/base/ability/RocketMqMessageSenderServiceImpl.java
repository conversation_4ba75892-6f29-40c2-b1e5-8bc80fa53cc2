package com.wanshifu.domain.base.ability;

import com.aliyun.openservices.ons.api.SendResult;
import com.wanshifu.domain.base.tools.EnvUtil;
import com.wanshifu.domain.base.MessageSenderService;
import com.wanshifu.domain.log.ability.OrderMqDomainService;
import com.wanshifu.domain.log.model.OrderMqLogVo;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: ZhangQiWei
 * @Date: 2019/5/13 16:55
 * @Description:
 */
@Slf4j
@Service
public class RocketMqMessageSenderServiceImpl implements MessageSenderService {

    @Resource
    private RocketMqSendService rocketMqSendService;
    @Resource
    private OrderMqDomainService orderMqDomainService;

    @Override
    public SendResult sendSyncMessage(String topic, String tag, String body) {
        SendResult sendResult = rocketMqSendService.sendSyncMessage(topic, tag, body);
        insertSendMqLog(topic, tag, body, sendResult,false);
        return sendResult;
    }

    @Override
    public SendResult sendSyncMessage(String topic, String tag, String body, boolean isRecordLog) {
        SendResult sendResult = sendSyncMessage(topic, tag, body);
        insertSendMqLog(topic,tag,body,sendResult,isRecordLog);
        return sendResult;
    }

    @Override
    public SendResult sendSyncOrderMessage(String topic, String tag, String body, String shardKey) {
        SendResult sendResult = rocketMqSendService.sendSyncOrderMessage(topic, tag, body, shardKey);
        insertSendMqLog(topic, tag, body, sendResult,false);
        return sendResult;
    }

    @Override
    public SendResult sendDelayMessage(String topic, String tag, String body, Long delayTime) {
        SendResult sendResult = rocketMqSendService.sendDelayMessage(topic, tag, body, delayTime);
        insertSendMqLog(topic, tag, body, sendResult,false);
        return sendResult;
    }

    @Override
    public SendResult sendDelayMessage(String topic, String tag, String body, Long delayTime, boolean isRecordLog) {
        SendResult sendResult = rocketMqSendService.sendDelayMessage(topic, tag, body, delayTime);
        insertSendMqLog(topic, tag, body, sendResult,isRecordLog);
        return sendResult;
    }

    private void insertSendMqLog(String topic, String tag, String body, SendResult sendResult, boolean isRecordLog) {
        if (isRecordLog){
            OrderMqLogVo orderMqLogVo = new OrderMqLogVo(topic, tag, sendResult.getMessageId(), body);
            orderMqLogVo.setRemark(String.format("环境:【%s】", EnvUtil.getEnvName()));
            orderMqDomainService.insertSendMq(orderMqLogVo);
        }

    }

}
