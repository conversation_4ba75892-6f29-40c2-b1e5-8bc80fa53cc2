package com.wanshifu.domain.base.consumer;

import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.wanshifu.domain.base.handler.AbstractNearbyOrderTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * <AUTHOR>
 * @Date 2023-11-07 11:15
 * @Description 内部-附近单topic
 * @Version v1
 **/
@Component
public class PrivateNearbyOrderTopic extends AbstractMessageTopic<AbstractNearbyOrderTopicMessageTag> {

    @Value("${wanshifu.rocketMQ.nearby-order-topic}")
    private String topic;
    @Value("${wanshifu.mq-rateLimiter.side.order}")
    private int MQ_LIMIT_SIDE_MAX_QPS;

    @Override
    protected String getTopic() {
        return topic;
    }

    public void  initStrategy(){
        // 附近单-计算附近单距离信息(首次拉取待报价订单)
        initFlowControlRule( ConsumeTagEnum.NEARBY_ORDER_DISTANCE.value,MQ_LIMIT_SIDE_MAX_QPS);
    }


    /**
     * 初始化限流规则
     * @auth: xcc
     * @date: 2022/1/18 10:14
     */
    private void initFlowControlRule(String tag, int count) {
        FlowRule rule = new FlowRule();
        rule.setResource(tag);
        // 消费的 qps
        rule.setCount(count);
        rule.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule.setLimitApp("default");
        rule.setControlBehavior(RuleConstant.CONTROL_BEHAVIOR_RATE_LIMITER);
        // 等待超时时间
        rule.setMaxQueueingTimeMs(3 * 1000);
        FlowRuleManager.loadRules(Collections.singletonList(rule));
    }
}
