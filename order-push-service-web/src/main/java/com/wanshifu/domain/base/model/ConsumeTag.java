package com.wanshifu.domain.base.model;

import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Date 2023-04-28 14:28
 * @Description 公共消费者tag注解。
 * 作用标记 ConsumerMessageProcessor 接口实现类，接收相关消息
 *
 * @Version v1
 **/
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface ConsumeTag {

    //tag枚举
    ConsumeTagEnum value();

    //异常时 是否重试。
    boolean isReconsumeLate() default true;

    //是否支持记录mq日志
    boolean isSupportLog()  default true;

    //最大重试次数(isReconsumeLate = true 有效)
    int maxReconsumeTime() default 0;
}
