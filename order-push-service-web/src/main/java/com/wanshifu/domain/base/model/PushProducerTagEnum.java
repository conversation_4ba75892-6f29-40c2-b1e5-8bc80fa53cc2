package com.wanshifu.domain.base.model;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/4/29 20:34
 * @Description: 通用生产者tag
 */
public enum PushProducerTagEnum {
    /*
     * 订单推单结果通知
     */
    ORDER_PUSHED_RESULT_NOTICE("order_pushed_result_notice"),

    /**
     * 订单打标
     */
    ORDER_TAG("order_tag"),

    /**
     * 订单移除标签
     */
    ORDER_REMOVE_TAG("order_tag_remove");

    public final String tag;


    PushProducerTagEnum(String tag) {
        this.tag = tag;
    }

    public String getTag() {
        return tag;
    }


}
