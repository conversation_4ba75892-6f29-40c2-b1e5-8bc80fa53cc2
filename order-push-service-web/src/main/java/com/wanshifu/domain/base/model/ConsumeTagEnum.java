package com.wanshifu.domain.base.model;

/**
 * @Author: <PERSON><PERSON>i<PERSON><PERSON>
 * @Date: 2019/4/29 20:34
 * @Description: 消费者MQ的Tag方法（包含外部与内部）
 */
public enum ConsumeTagEnum {

    //TODO =======订阅外部平台tag===============================

    // 推单中台
    PUSH_LESS_CONTEND("less_contend","推送竞争偏少通知"),
//    MASTER_ORDER_SCORE("master_order_score","订单订单评分数据"),
    MATCH_RESULT("match_result","推单结果通知"),

    PUSH_ORDERS_TO_MASTER("push_orders_to_master","推单结果通知"),


    //家庭样板城市项目后台调度手动指派师傅功能新加
    RECOMMEND_MATCH_RESULT("recommend_match_result","推单的推荐师傅通知"),

    // 报价中台

    CLEAR_NEAR_BY_ORDER_DISTANCE("clear_near_by_order_distance","附近单-修改订单清理订单距离"),
    //师傅不感兴趣订单
    MASTER_DISINTEREST_ORDER("master_disinterest_order","师傅不感兴趣订单"),

    //
    OFFER_ORDER_PUSH_DELETE("offer_order_push_delete","删除推单记录"),

    OFFER_ORDER_PUSH_HANDLE_DELETE("order_push_handle_delete","公共处理删除推单记录"),

    //
    OFFER_ORDER_PUSH_CLEAR("offer_order_push_clear", "Offer订单已雇佣-清理订单推单记录"),

    EXCLUSIVE_ORDER_TRANSFER("exclusive_order_transfer", "专属订单转单"),

    MASTER_OFFER_PRICE("master_offerPrice", "师傅报价"),

    MASTER_SERVE_FINISH("master_serveFinish", "师傅服务完工"),

    ORDER_CLOSE("order_close", "订单关单"),

    //履约中台
    FULFILLMENT_SERVE_STATUS("fulfillment_serve_status","订单履约状态变更"),

    MASTER_BOUNTY("master_bounty","师傅悬赏"),

    NO_OFFER_TO_CALL("no_offer_to_call","无人报价可电话联系"),

    FILTER_MASTER_FOR_NO_OFFER("filter_master_for_no_offer","筛选落库合适的师傅在无人报价时可进行邀请报价"),


    TAG_JON_FULL_TIME_SHARE_TIME_MASTER("tag_join_full_time_share_time_master","师傅变更全时/分时状态"),



    //TODO =========订阅内部tag ======

    ORDER_PUSH_CLEAR("orderPushClear", "订单已雇佣-清理订单推单记录"),

    ORDER_PUSH_UPDATE_OFFER("order_push_update_offer", "首次报价更新报价状态"),

    NEARBY_ORDER_DISTANCE("nearbyOrderDistance","附近单-计算附近单距离信息(首次拉取待报价订单)"),

    GENERAL_MASTER_GRAB("general_master_grab","师傅抢单"),

    INTERFERE_ORDER_PUSH("interfere_order_push","平台干预推单列表"),


    USER_PAY_SERVE_FEE("user_payServeFee", "用户支付服务费用"),


    //家庭样板城市项目后台调度手动指派师傅功能新加
    ENTERPRIRSE_DIRECT_APPOINT("enterprise_direct_appoint","总包直接指派"),





    ;
    public final String value;

    public final String describe;

    ConsumeTagEnum(String value, String describe) {
        this.value = value;
        this.describe = describe ;
    }

    public String getValue() {
        return value;
    }

    public String getDescribe(){return describe;}

}
