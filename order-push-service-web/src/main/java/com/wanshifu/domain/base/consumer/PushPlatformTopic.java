package com.wanshifu.domain.base.consumer;

import com.wanshifu.domain.base.handler.AbstractPushPlatformTopicMessageTag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/1 10:31
 */
@Component
public class PushPlatformTopic  extends AbstractMessageTopic<AbstractPushPlatformTopicMessageTag>{
    @Value("${wanshifu.rocketMQ.push-platform-notice-topic}")
    private String pushTopic;


    @Override
    protected String getTopic() {
        return pushTopic;
    }
}
