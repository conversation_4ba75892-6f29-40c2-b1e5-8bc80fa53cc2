package com.wanshifu.domain.base.consumer;


import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.wanshifu.domain.base.handler.AbstractOfferPlatformTopicMessageTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/1 11:32
 */
@Component
public class OfferPlatformTopic extends AbstractMessageTopic<AbstractOfferPlatformTopicMessageTag> {

    @Value("${wanshifu.rocketMQ.order-offer-common-topic}")
    private String offerTopic;

    @Value("${wanshifu.mq-rateLimiter.side.order}")
    private int MQ_LIMIT_SIDE_MAX_QPS;

    @Override
    protected String getTopic() {
        return offerTopic;
    }

    public void  initStrategy(){

        // 附近单-修改订单清理订单距离
        initFlowControlRule( ConsumeTagEnum.CLEAR_NEAR_BY_ORDER_DISTANCE.value, MQ_LIMIT_SIDE_MAX_QPS);

        initFlowControlRule(ConsumeTagEnum.OFFER_ORDER_PUSH_DELETE.value, MQ_LIMIT_SIDE_MAX_QPS);

        initFlowControlRule(ConsumeTagEnum.OFFER_ORDER_PUSH_CLEAR.value, MQ_LIMIT_SIDE_MAX_QPS);

        /**
         * 报价、查看已报价订单详情
         */
        initFlowControlRule(ConsumeTagEnum.NEARBY_ORDER_DISTANCE.value, MQ_LIMIT_SIDE_MAX_QPS);





    }


    /**
     * 初始化限流规则
     * @auth: xcc
     * @date: 2022/1/18 10:14
     */
    private void initFlowControlRule(String tag, int count) {
        FlowRule rule = new FlowRule();
        rule.setResource(tag);
        // 消费的 qps
        rule.setCount(count);
        rule.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule.setLimitApp("default");
        rule.setControlBehavior(RuleConstant.CONTROL_BEHAVIOR_RATE_LIMITER);
        // 等待超时时间
        rule.setMaxQueueingTimeMs(3 * 1000);
        FlowRuleManager.loadRules(Collections.singletonList(rule));
    }
}
