package com.wanshifu.domain.base.tools;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wanshifu.domain.base.CallGateway;
import com.wanshifu.domain.sdk.address.CommonAddressService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description  通用Apollo开关控制
 * @date 2024/3/1 10:45
 */
@Component
public class ApolloSwitchUtils {

    @Value("${order.sink.iteration.switch:off}")
    private String iterationSwitch;

    @Value("${order.sink.iteration.version.switch:null}")
    private String iterationVersionSwitch;

    @Value("${order.push.city.switch:old}")
    private String pushResultCitySwitch;

    @Value("${provinceNextId.open.switch:on}")
    private String provinceNextIdOpenSwitch;

    /**
     * 订单评分更新开关，默认开启
     */
    @Value("${order.push.scoreUpdateSwitch:on}")
    private String scoreUpdateSwitch;

    /**
     * 顺路单组合开关，默认开启
     */
    @Value("${sideOrderCombination.switch:on}")
    private String sideOrderCombinationSwitch;


    /**
     * 角标查询分表城市
     */
    @Value("${analytic.waitOfferCount.sharding.city}")
    private String analyticWaitOfferCountShardingCity;

    /**
     * 香港等出海城市
     */
    @Value("${outer.push.cityList:}")
    private String outerCity;

    @Resource
    private CallGateway callGateway;

    @Resource
    private CommonAddressService addressService;

    /**
     * apollo配置
     */
    private static final Config CONFIG = ConfigService.getAppConfig();

    /**
     * 是否开启订单评分更新开关，默认开启
     * @return
     */
    public boolean isOpenScoreUpdateSwitch(){
        return "on".equals(scoreUpdateSwitch);
    }


    /**
     * 是否开启订单下沉开关
     * @return
     */
    public Boolean isOpenIterationSwitch(){
        return "on".equals(iterationSwitch);
    }

    /**
     * 迭代下沉版本开关
     * @param version
     * @return
     */
    public Boolean isOpenIterationVersionSwitch(String version){
        if(StringUtils.isBlank(iterationVersionSwitch)){
            return false;
        }
        return Arrays.asList(iterationVersionSwitch.split(",")).contains(version);

    }


    /**
     *  推单对接新旧开关
     * @return
     */
    public String getPushDockingHandoffTag (Long thirdDivisionId){
        if (EnvUtil.isProdProfile()){
            //配置 all 则意味全部城市开放接入新系统
            if (StringUtils.isEmpty(pushResultCitySwitch)) {
                return "old";
            }
            if (pushResultCitySwitch.equals("old")) {
                return "old";
            }
            if (pushResultCitySwitch.equals("all") || pushResultCitySwitch.contains("all")) {
                return "new";
            }
            if (Objects.nonNull(thirdDivisionId)) {
                Long cityIdByDivisionId = callGateway.catchNoLog(()-> addressService.getCityIdByDivisionId(thirdDivisionId));
                List<String> cityIdList = StringUtils.splitCommaToList(pushResultCitySwitch);
                if (Objects.nonNull(cityIdByDivisionId) && CollectionUtils.isNotEmpty(cityIdList)) {
                    if (cityIdList.contains(cityIdByDivisionId.toString())) {
                        return "new";
                    }
                }
            }
            return "old";
        }else {
            //开发、测试、灰度 全部默认开启
            return "new";
        }
    }

    /**
     * 分表城市id字段应用开关
     * @return
     */
    public String getProvinceNextIdOpenSwitch() {
        return provinceNextIdOpenSwitch;
    }

    /**
     * 该城市是否配置分表
     * @param provinceNextId
     * @return
     */
    public static boolean isOpenSharingCitySwitch(String provinceNextId){
        String[] sharingCityArray = CONFIG.getArrayProperty("order.push.sharding.switch.sharingCityList", ",", new String[]{});

        if ((sharingCityArray == null || sharingCityArray.length == 0)) {
            return false;
        }
        List<String> sharingCityList = Arrays.asList(sharingCityArray);
        if (sharingCityList.contains("all")) {
            return true;
        }
        if (sharingCityList.contains(provinceNextId)) {
            return true;
        } else {
            return false;
        }

    }



    /**
     * 该城市是否开放角标查询分表
     * @param provinceNextId
     * @return
     */
    public boolean isAnalyticWaitOfferCountSharing(Long provinceNextId){

        
        if(StringUtils.isBlank(analyticWaitOfferCountShardingCity)){
            return false;
        }

        final List<String> analyticWaitOfferCountShardCityList =
                new ArrayList<>(Arrays.asList(analyticWaitOfferCountShardingCity.split(",")));

        if (analyticWaitOfferCountShardCityList.contains("all")) {
            return true;
        }
        if (analyticWaitOfferCountShardCityList.contains(String.valueOf(provinceNextId))) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 是否是出海城市
     * @param divisionId
     * @return
     */
    public boolean isOuterCitySwitch(Long divisionId){
        if(StringUtils.isBlank(outerCity)){
            return false;
        }
        final List<String> outerCityList =
                new ArrayList<>(Arrays.asList(outerCity.split(",")));

        if (outerCityList.contains(String.valueOf(divisionId))) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 该城市是否配置写原表
     * @param provinceNextId
     * @return
     */
    public static boolean isOpenWriteOriginCitySwitch(String provinceNextId){
        String[] writeOriginArray = CONFIG.getArrayProperty("order.push.sharding.switch.doubleWriteCityList", ",", new String[]{});

        if ((writeOriginArray == null || writeOriginArray.length == 0)) {
            return false;
        }
        List<String> writeOriginCityList = Arrays.asList(writeOriginArray);

        if (writeOriginCityList.contains(provinceNextId)) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 该城市是否配置读分表
     * @param provinceNextId
     * @return
     */
    public static boolean isOpenReadShardingCitySwitch(String provinceNextId){
        String[] readShardingArray = CONFIG.getArrayProperty("order.push.sharding.switch.readSharingCityList", ",", new String[]{});

        if ((readShardingArray == null || readShardingArray.length == 0)) {
            return false;
        }
        List<String> readShardingCityList = Arrays.asList(readShardingArray);

        if (readShardingCityList.contains("all")) {
            return true;
        }

        if (readShardingCityList.contains(provinceNextId)) {
            return true;
        } else {
            return false;
        }

    }
    /**
     * 智能排序走push表城市开关,(下架orderPushSore开关)
     * @param provinceNextId
     * @return
     */
    public static boolean isDownOrderPushScoreCitySwitch(String provinceNextId){
        String[] downOrderPushScoreArray = CONFIG.getArrayProperty("order.push.score.down.switch.cityList", ",", new String[]{});

        if ((downOrderPushScoreArray == null || downOrderPushScoreArray.length == 0)) {
            return false;
        }
        List<String> downOrderPushScoreCityList = Arrays.asList(downOrderPushScoreArray);

        if (downOrderPushScoreCityList.contains("all")) {
            return true;
        }

        if (downOrderPushScoreCityList.contains(provinceNextId)) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 该城市是否配置智能排序干预位
     * @param provinceNextId
     * @return
     */
    public static boolean isOpenExposureSortCitySwitch(String provinceNextId){
        String[] exposureCityArray = CONFIG.getArrayProperty("order.push.exposure.switch.cityList", ",", new String[]{});

        if ((exposureCityArray == null || exposureCityArray.length == 0)) {
            return false;
        }
        List<String> exposureCityList = Arrays.asList(exposureCityArray);

        if (exposureCityList.contains("all")) {
            return true;
        }

        if (exposureCityList.contains(provinceNextId)) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 获取待接单默认列表展示长尾单的一级服务id
     * @return
     */
    public static List<String> getDisplayLongTailLv1ServeIds(){
        String[] displayLongTailLv1ServeIdsArray = CONFIG.getArrayProperty("order.push.display.longTail.level1ServeIdList", ",", new String[]{});

        if ((displayLongTailLv1ServeIdsArray == null || displayLongTailLv1ServeIdsArray.length == 0)) {
            return new ArrayList<>();
        }
        return Arrays.asList(displayLongTailLv1ServeIdsArray);
    }

    public boolean isOpenSideOrderCombination(){
        return "on".equals(sideOrderCombinationSwitch);
    }
}
