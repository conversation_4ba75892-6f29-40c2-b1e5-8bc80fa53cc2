package com.wanshifu.domain.base.ability;

import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.TransmittableContext;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024-02-29 18:35
 * @Description
 * @Version v1
 **/
@Service
public class CommonBooleanValueServiceImpl  implements CommonBooleanValueService{


    @Override
    public Boolean isUnknownException (Exception e) {
        if (e instanceof BusException) {
            return false;
        }
        if (e instanceof  IllegalArgumentException) {
            return false;
        }
        if (e instanceof ApiAccessException) {
            return false;
        }
        return true;
    }

    /***
     * 是否为压测流量
     * @return true: 是  false： 否
     */
    @Override
    public Boolean isPressureBeta() {
        return TransmittableContext.isPtScene();
    }

}
