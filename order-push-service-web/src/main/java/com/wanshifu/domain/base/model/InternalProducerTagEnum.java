package com.wanshifu.domain.base.model;

/**
 * <AUTHOR>
 * @Date 2023-08-24 14:02
 * @Description 内部生产者tag
 * @Version v1
 **/
public enum InternalProducerTagEnum {

    /**
     * 订单已雇佣-清理订单推单记录
     */
    ORDER_PUSH_CLEAR( ConsumeTagEnum.ORDER_PUSH_CLEAR.value),


    /**
     * 附近单-首次拉取待报价订单，记录附近单信息
     */
    NEARBY_ORDER_DISTANCE(ConsumeTagEnum.NEARBY_ORDER_DISTANCE.value)

    ;
    public final String tag;


    InternalProducerTagEnum(String tag) {
        this.tag = tag;
    }

    public String getTag() {
        return tag;
    }
}
