package com.wanshifu.domain.base;

import com.aliyun.openservices.ons.api.SendResult;

/**
 * @Author: <PERSON><PERSON>i<PERSON><PERSON>
 * @Date: 2019/5/13 15:50
 * @Description:
 */
public interface MessageSenderService {


    /**
     * 发送普通消息
     * @param topic
     * @param tag
     * @param body
     * @return
     */
    SendResult sendSyncMessage(String topic, String tag, String body);

    /**
     * 发送普通消息 (是否支持日志记录)
     * @param topic
     * @param tag
     * @param body
     *
     * @return
     */
    SendResult sendSyncMessage(String topic, String tag, String body,boolean isRecordLog);

    /**
     * 发送顺序消息
     * @param topic
     * @param tag
     * @param body
     * @return
     */
    SendResult sendSyncOrderMessage(String topic, String tag, String body, String shardKey);

    /**
     * 发送延时消息
     * @param tag
     * @param body
     * @param topic
     * @param delayTime 延时时间，time 单位(ms)
     * @return
     */
    SendResult sendDelayMessage(String topic, String tag, String body, Long delayTime);

    /**
     * 发送延时消息
     * @param tag
     * @param body
     * @param topic
     * @param delayTime 延时时间，time 单位(ms)
     * @return
     */
    SendResult sendDelayMessage(String topic, String tag, String body, Long delayTime,boolean isRecordLog );


}
