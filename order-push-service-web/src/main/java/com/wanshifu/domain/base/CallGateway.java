package com.wanshifu.domain.base;

import com.wanshifu.domain.base.tools.FeiShuUtils;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.TransmittableContext;
import com.wanshifu.order.push.enums.PushBusinessCode;
import com.wanshifu.order.offer.domains.enums.code.OfferBusinessCode;
import com.wanshifu.order.push.enums.PushBusinessCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @Date 2023-08-23 20:06
 * @Description
 * @Version v1
 **/
@Component
public class CallGateway {

    private static final Logger logger = LoggerFactory.getLogger(CallGateway.class);

    /***
     * 误报错不给提示
     * @param supplier
     * @param <T>
     * @return
     */
    public <T> T catchNoLog (Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog", e);
        }
        return null;
    }


    /**
     * 误报错不给提示, 返回默认值
     * @param supplier
     * @return T
     * @author: ZhiHao
     * @date: 2022/2/23
     */
    public <T> T catchNoLog(Supplier<T> supplier, T defaultValue) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog", e);
        }
        return defaultValue;
    }


    /**
     * 误报错不给提示, 返回默认值
     * @param supplier
     * @return T
     * @author: ZhiHao
     * @date: 2022/2/23
     */
    public <T> T catchNoLog(Supplier<T> supplier, T defaultValue,String methodName, Object requestParams ) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog", e);
            FeiShuUtils.sendTempMsg(methodName,requestParams,e);

        }
        return defaultValue;
    }


    /**
     * 捕获调用第三方接口异常，并记录日志, 不抛异常
     * @param supplier 处理程序
     * @param <T>      返回值类型
     * @return 返回结果
     */
    public <T> T catchLog(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog=", e);
        }
        return null;
    }

    /**
     * 第三方接口异常，并记录日志, 不抛异常,钉钉群通知
     *
     * @param supplier      处理程序
     * @param requestParams 请求参数
     * @param <T>           返回结果类型
     */
    public <T> T catchLog (Supplier<T> supplier, String methodName, Object requestParams) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog methodName={} requestParams={}", methodName, requestParams, e);
            FeiShuUtils.sendTempMsg(methodName,requestParams,e);
        }
        return null;
    }


    /**
     * 捕获调用第三方接口异常,记录日志并抛出异常
     *
     * @param supplier 处理程序
     * @param <T>      返回结果类型
     */
    public <T> T catchLogThrow(Supplier<T> supplier) {
        T t;
        try {
            t = supplier.get();
        } catch (Exception e) {
            logger.error("Tools..catchLogThrow..", e);
            throw new BusException(PushBusinessCode.REMOTE_CALL_SERVER_ERROR.code,e.getMessage());
        }
        return t;
    }


    /***
     * 是否为压测流量
     * @return true: 是  false： 否
     */
    public static boolean isPressureBeta() {
        return TransmittableContext.isPtScene();
    }


    /**
     * 捕获调用第三方接口异常,记录日志、钉钉通知, 并抛出异常
     *
     * @param supplier
     * @param methodName
     * @param requestParams
     * @return T
     * @author: ZhiHao
     * @date: 2021/11/12
     */
    public <T> T catchLogThrow(Supplier<T> supplier, String methodName, Object requestParams) {
        T t;
        try {
            t = supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog methodName={} requestParams={}", methodName, requestParams, e);
            FeiShuUtils.sendTempMsg(methodName,requestParams,e);
            throw new BusException(e.getMessage());
        }
        return t;
    }
}
