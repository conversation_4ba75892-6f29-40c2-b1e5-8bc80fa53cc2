package com.wanshifu.domain.base.consumer;


import com.wanshifu.domain.base.handler.AbstractCommonOrderGeneralTopicMessageTag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @description
 * @date 2024/3/1 11:32
 */
@Component
public class CommonOrderGeneralTopic extends AbstractMessageTopic<AbstractCommonOrderGeneralTopicMessageTag> {

    @Value("${wanshifu.rocketMQ.common-order-general-topic}")
    private String commonOrderGeneralTopic;


    @Override
    protected String getTopic() {
        return commonOrderGeneralTopic;
    }

}
