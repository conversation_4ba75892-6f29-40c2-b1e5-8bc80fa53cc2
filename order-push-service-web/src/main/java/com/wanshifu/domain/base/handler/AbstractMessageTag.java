package com.wanshifu.domain.base.handler;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.domain.base.model.Constant;
import com.wanshifu.domain.base.tools.ApolloSwitchUtils;
import com.wanshifu.domain.base.tools.EnvUtil;
import com.wanshifu.domain.base.tools.ValidatorHelper;
import com.wanshifu.domain.log.ability.OrderMqDomainService;
import com.wanshifu.domain.log.model.OrderMqLogVo;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.config.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.aop.support.AopUtils;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @Date 2023-04-28 14:11
 * @Description 消费者消息接口
 * @Version v1
 **/
@Slf4j
public abstract class AbstractMessageTag<T> implements MessageTag {

     //是否需要重试
     protected Boolean isReconsumeLater;
     //执行具体业务
     protected String businessScene;
     //是否添加mq日志
     private Boolean  isSupportLog;
     /**
      * 最大重试次数 （必须maxReconsumeTime = true）
      */
     private Integer maxReconsumeTime;

     @Resource
     private ApolloSwitchUtils apolloSwitchUtils;

     @Resource
     private OrderMqDomainService orderMqDomainService;


     @Override
     public boolean isReconsumeLater(){
          return isReconsumeLater;
     }


     public void setReconsumeLate(Boolean isReconsumeLater){
          this.isReconsumeLater = isReconsumeLater;
     }

     @Override
     public void setMaxReconsumeTime(Integer time) {
          this.maxReconsumeTime = time;
     }

     public int maxReconsumeTime() {
          return maxReconsumeTime;
     }

     @Override
     public void setBusinessScene(String businessScene){
          this.businessScene = businessScene;
     }


     @Override
     public String businessScene() {
          return businessScene;
     }

     public Boolean isSupportLog() {
          return isSupportLog;
     }

     public void setSupportLog(Boolean supportLog) {
          isSupportLog = supportLog;
     }

     @Override
     public void handler(Message message) {
          //记录mq消息日志
          addTracing(message.getMsgID());
          try {
               String text = new String(message.getBody());
               //消费的消息中，必须继承BaseMessageBean，不然启动报错
               Class<?> actualTypeArgument = getActualTypeArgument(this);
               Object o = JSON.parseObject(text, actualTypeArgument);
               T t = (T) o;
               preHandler(message, t);
               postHandler(t);
               afterHandler(message,t);
          }catch ( Exception e ) {
               exceptionHandler(message,e);
               throw e;
          }finally {
               // remove
               DynamicDataSource.clearDataSourceKey();
          }

     }


     public void preHandler(Message message,  T t ){

          businessScene = businessScene();
          if (StringUtils.isNotEmpty(businessScene)) {
               log.info(String.format("消费%s消息:text={%s}", businessScene, new String(message.getBody())));
          }


          String topic = message.getTopic();
          String tag = message.getTag();
          String msgID = message.getMsgID();

          if (isSupportLog){

               OrderMqLogVo orderMqLogVo = new OrderMqLogVo();
               orderMqLogVo.setTopic(topic);
               orderMqLogVo.setTag(tag);
               orderMqLogVo.setMessageId(msgID);
               orderMqLogVo.setMessageDesc(businessScene);
               orderMqLogVo.setRemark(String.format("环境:【%s】", EnvUtil.getEnvName()));
               orderMqLogVo.setDataDetails(JSON.toJSONString(t));
               orderMqLogVo.setGlobalOrderTraceId(getGlobalOrderTraceId(t));
               //记录mq消息日志
               orderMqDomainService.insertAcceptMq(orderMqLogVo);
          }

          //增加对接口参数校验
          ValidatorHelper.checkParams(t);

     }
     /**
      * 获取全局id，用于日志追踪 ，具体子类实现
      * @param t
      * @return
      */
     public Long getGlobalOrderTraceId (T t){
          return null;
     }

     public abstract void postHandler(T t);

     public void afterHandler(Message message, T t ) {


     }

     public void exceptionHandler( Message message, Exception e) {
          //
     }

     private void addTracing(String messageId) {
          MDC.put(Constant.REQUEST_ID,messageId);
     }


     public Class getActualTypeArgument(Object o){
          //如果代理类。获取其原始类
          Class<?> targetClass = null;
          if ( AopUtils.isAopProxy(o)) {
               targetClass = AopUtils.getTargetClass(o);
          }else {
               targetClass = o.getClass();
          }
          return getActualTypeArgument(targetClass);
     }

     public  Class<?> getActualTypeArgument(Class<?> clazz) {
          Class<?> entitiClass = null;
          Type genericSuperclass = clazz.getGenericSuperclass();
          if (genericSuperclass instanceof ParameterizedType) {
               Type[] actualTypeArguments = ((ParameterizedType) genericSuperclass)
                       .getActualTypeArguments();
               if (actualTypeArguments != null && actualTypeArguments.length > 0) {
                    entitiClass = (Class<?>) actualTypeArguments[0];
               }
          }

          return entitiClass;
     }


}
