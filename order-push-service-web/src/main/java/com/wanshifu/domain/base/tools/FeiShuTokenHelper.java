package com.wanshifu.domain.base.tools;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description：
 * <AUTHOR>
 * @Date 2021-07-29 9:27
 */
@Component
@Slf4j
public class FeiShuTokenHelper {


    // TODO 替换

    /**
     * 飞书预警机器人-订单中心业务异常报警群
     */
    public static String FEI_SHU_TALK_ORDER_CENTER_BUSINESS_URL;
    @Value("${feiShu-talk.client.order_center_business.url:}")
    public void setFeiShuTalkUserBusinessGroupUrl(String feiShuTalkOrderCenterBusinessUrl) {
        FEI_SHU_TALK_ORDER_CENTER_BUSINESS_URL = feiShuTalkOrderCenterBusinessUrl;
    }


    /**
     * 飞书预警机器人-订单中心业务技能匹配异常业务预警群
     */
    public static String SKILL_MATCHING_BUSINESS_URL;
    @Value("${feiShu-talk.client.skill_matching_business.url:}")
    public void setSkillMatchingBusinessUrl(String skillMatchingBusinessUrl) {
        SKILL_MATCHING_BUSINESS_URL = skillMatchingBusinessUrl;
    }

    /**
     * 飞书预警机器人-未传省下级id参数或传参有误飞书提醒群
     */
    public static String FEI_SHU_TALK_PUSH_DATA_UPGRADE_PROVINCE_NEXT_ID_VALID_URL;
    @Value("${feiShu-talk.client.push.data.upgrade.provinceNextId.valid.url:}")
    public void setFeiShuTalkPushDataUpgradeProvinceNextIdValidUrl(String feiShuTalkPushDataUpgradeProvinceNextIdValidUrl) {
        FEI_SHU_TALK_PUSH_DATA_UPGRADE_PROVINCE_NEXT_ID_VALID_URL = feiShuTalkPushDataUpgradeProvinceNextIdValidUrl;
    }

}
