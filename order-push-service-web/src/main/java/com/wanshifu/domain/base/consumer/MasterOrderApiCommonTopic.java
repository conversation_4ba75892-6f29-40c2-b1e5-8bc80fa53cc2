package com.wanshifu.domain.base.consumer;

import com.wanshifu.domain.base.handler.AbstractMasterOrderApiCommonTopicMessageTag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/30 15:52
 */
@Component
public class MasterOrderApiCommonTopic extends AbstractMessageTopic<AbstractMasterOrderApiCommonTopicMessageTag> {

    @Value("${wanshifu.rocketMQ.master-order-api-common-topic}")
    private String masterOrderApiCommonTopic;


    @Override
    protected String getTopic() {
        return masterOrderApiCommonTopic;
    }
}
