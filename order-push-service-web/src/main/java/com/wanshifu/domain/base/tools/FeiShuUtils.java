package com.wanshifu.domain.base.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.HttpBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.slf4j.MDC;
import org.springframework.cloud.sleuth.Span;

import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

@Slf4j
public class FeiShuUtils {

    /**
     * 发送消息
     * 成功报文: {"StatusCode": 0,"StatusMessage": "success"}
     *
     * @param webhookUrl 飞书群 url
     * @param text       消息内容
     * @author: Zhang <PERSON>
     * @date: 2022/9/6
     */
    public static boolean sendMsg(String webhookUrl, String text) {
        String res = "";
        String requestData = "";
        try {

            HashMap<String,Object> content = new HashMap();
            content.put("text", text);
            HashMap<String,Object> data = new HashMap();
            data.put("msg_type", "text");
            data.put("content", content);

            requestData = JSON.toJSONString(data);
            res = HttpBuilder.genericPost(webhookUrl)
                    .connectTimeOut(500)
                    .socketTimeout(500)
                    .entity(new StringEntity(requestData, ContentType.APPLICATION_JSON))
                    .build().executeToString();
            JSONObject jsonObject = JSON.parseObject(res);

            String statusCode = jsonObject.getString("StatusCode");
            if(StringUtils.isBlank(statusCode) || !statusCode.equals("0")){
                log.error("飞书预警请求参数: {} , 响应内容: {}, webhookUrl:{}", requestData, res, webhookUrl);
            }
            return true;
        } catch (Exception e) {
            log.error("飞书预警失败: req: {}, res: {} , webhookUrl:{} ", requestData, res, webhookUrl , e);
        }
        return false;
    }



    /**
     * 发送消息
     * 成功报文: {"StatusCode": 0,"StatusMessage": "success"}
     *
     * @date: 2022/9/6
     */
    public static boolean sendTempMsg ( String bussCode,
                                        Object param, String exceptionMessage) {

        String res = "";
        String requestData = "";
        String webhookUrl = FeiShuTokenHelper.FEI_SHU_TALK_ORDER_CENTER_BUSINESS_URL;
        try {
            String errorMessage = new StringBuilder()
                    .append("【告警时间】 ").append(DateUtils.formatDateTime(new Date())).append("\n")
                    .append("【业务场景】 ").append( StringUtils.isNotEmpty(bussCode) ? bussCode : "未知" ).append("\n")
                    .append("【来源服务】 ").append("order-push-service").append("\n")
                    .append("【参数信息】 ").append(buildParamStr(param)).append("\n")
                    .append("【异常原因】 ").append(StringUtils.isNotEmpty(exceptionMessage) ? exceptionMessage : "").append("\n")
                    .append("【环境信息】 ").append(EnvUtil.getEnvName()).append("\n")
                    .append("【traceId】").append(MDC.get(Span.TRACE_ID_NAME)).append("\n")
                    .append("<at user_id=\"all\">所有人</at> ").toString();

            HashMap<String,Object> content = new HashMap();
            content.put("text", errorMessage);
            HashMap<String,Object> data = new HashMap();
            data.put("msg_type", "text");
            data.put("content", content);

            requestData = JSON.toJSONString(data);
            res = HttpBuilder.genericPost(webhookUrl)
                    .connectTimeOut(500)
                    .socketTimeout(500)
                    .entity(new StringEntity(requestData, ContentType.APPLICATION_JSON))
                    .build().executeToString();
            JSONObject jsonObject = JSON.parseObject(res);

            String statusCode = jsonObject.getString("StatusCode");
            if(StringUtils.isBlank(statusCode) || !statusCode.equals("0")){
                log.error("飞书预警请求参数: {} , 响应内容: {}, webhookUrl:{}", requestData, res, webhookUrl);
            }
            return true;
        } catch (Exception e) {
            log.error("飞书预警失败: req: {}, res: {} , webhookUrl:{} ", requestData, res, webhookUrl , e);
        }
        return false;
    }

    /**
     * 发送消息
     * 成功报文: {"StatusCode": 0,"StatusMessage": "success"}
     *
     * @date: 2022/9/6
     */
    public static boolean sendTempMsg ( String bussCode,
                                        Object param,Exception exception) {

        String res = "";
        String requestData = "";
        String webhookUrl = FeiShuTokenHelper.FEI_SHU_TALK_ORDER_CENTER_BUSINESS_URL;
        try {
            String errorMessage = new StringBuilder()
                    .append("【告警时间】 ").append(DateUtils.formatDateTime(new Date())).append("\n")
                    .append("【业务场景】 ").append( StringUtils.isNotEmpty(bussCode) ? bussCode : "未知" ).append("\n")
                    .append("【来源服务】 ").append(EnvUtil.getAppName()).append("\n")
                    .append("【参数信息】 ").append( buildParamStr(param)).append("\n")
                    .append("【异常原因】 ").append(ExceptionHelper.substrException(exception)).append("\n")
                    .append("【环境信息】 ").append(EnvUtil.getEnvName()).append("\n")
                    .append("【traceId】").append(MDC.get(Span.TRACE_ID_NAME)).append("\n")
                    .append("<at user_id=\"all\">所有人</at> ").toString();

            HashMap<String,Object> content = new HashMap();
            content.put("text", errorMessage);
            HashMap<String,Object> data = new HashMap();
            data.put("msg_type", "text");
            data.put("content", content);

            requestData = JSON.toJSONString(data);
            res = HttpBuilder.genericPost(webhookUrl)
                    .connectTimeOut(500)
                    .socketTimeout(500)
                    .entity(new StringEntity(requestData, ContentType.APPLICATION_JSON))
                    .build().executeToString();
            JSONObject jsonObject = JSON.parseObject(res);

            String statusCode = jsonObject.getString("StatusCode");
            if(StringUtils.isBlank(statusCode) || !statusCode.equals("0")){
                log.error("飞书预警请求参数: {} , 响应内容: {}, webhookUrl:{}", requestData, res, webhookUrl);
            }
            return true;
        } catch (Exception e) {
            log.error("飞书预警失败: req: {}, res: {} , webhookUrl:{} ", requestData, res, webhookUrl , e);
        }
        return false;
    }

    private static String buildParamStr(Object param) {
        String paramStr = "";
        if (Objects.nonNull(param)) {
            if (param instanceof  String) {
                paramStr = param.toString();
            }else {
                paramStr =  JSON.toJSONString(param);
            }
        }

        return paramStr;
    }
}
