package com.wanshifu.domain.base.consumer;

import com.wanshifu.domain.base.handler.AbstractMatchMasterTopicMessageTag;
import com.wanshifu.domain.base.handler.AbstractOrderOfferTopicMessageTag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023-11-07 11:15
 * @Description 通用-匹配师傅topic
 * @Version v1
 **/
@Component
public class OrderOfferTopic extends AbstractMessageTopic<AbstractOrderOfferTopicMessageTag> {

    @Value("${wanshifu.rocketMQ.order-offer-topic}")
    private String topic;


    @Override
    protected String getTopic() {
        return topic;
    }
}
