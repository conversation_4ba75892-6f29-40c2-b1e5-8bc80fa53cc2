package com.wanshifu.domain.base.tools;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description topic管理
 * @date 2024/3/1 14:50
 */
@Component
public class TopicHelper {

    /**
     * 订单推单公共对外topic
     */
    public static String  ORDER_PUSH_COMMON_TOPIC;

    @Value("${wanshifu.rocketMQ.order-push-common-topic}")
    public void setOrderOfferCommonTopic(String orderPushCommonTopic) {
        ORDER_PUSH_COMMON_TOPIC = orderPushCommonTopic;
    }


    /**
     * 订单推单内部正常业务topic
     */
    public static String  ORDER_PUSH_INTERNAL_NORMAL_BUSINESS_TOPIC;

    @Value("${wanshifu.rocketMQ.normal-business-order-topic}")
    public void setOrderPushInternalNormalBusinessTopic(String orderPushInternalNormalBusinessTopic) {
        ORDER_PUSH_INTERNAL_NORMAL_BUSINESS_TOPIC = orderPushInternalNormalBusinessTopic;
    }

    /**
     * 订单推单内部顺路单topic
     */
    public static String  ORDER_PUSH_SIDE_ORDER_TOPIC;

    @Value("${wanshifu.rocketMQ.side-order-topic}")
    public void setOrderPushSideOrderTopic(String orderPushSideOrderTopic) {
        ORDER_PUSH_SIDE_ORDER_TOPIC = orderPushSideOrderTopic;
    }

    /**
     * 订单推单内部附近单topic
     */
    public static String  ORDER_PUSH_NEAR_BY_ORDER_TOPIC;

    @Value("${wanshifu.rocketMQ.nearby-order-topic}")
    public void setOrderPushNearByOrderTopic(String orderPushNearByOrderTopic) {
        ORDER_PUSH_NEAR_BY_ORDER_TOPIC = orderPushNearByOrderTopic;
    }


    /**
     * 推单触达topic
     */
    public static String ORDER_PUSH_NOTICE_TOPIC;
    @Value("${wanshifu.rocketMQ.push-platform-notice-topic}")
    public void setOrderPushNoticeTopic(String orderPushNoticeTopic) {
        ORDER_PUSH_NOTICE_TOPIC = orderPushNoticeTopic;
    }



}
