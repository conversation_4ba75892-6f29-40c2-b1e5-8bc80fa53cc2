package com.wanshifu.domain.base.consumer;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.Consumer;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.domain.base.handler.MessageTag;
import com.wanshifu.domain.base.model.Constant;
import com.wanshifu.domain.base.model.ConsumeTag;
import com.wanshifu.domain.base.model.ConsumeTagEnum;
import com.wanshifu.domain.base.model.ConsumeTags;
import com.wanshifu.domain.base.tools.EnvUtil;
import com.wanshifu.domain.base.tools.FeiShuTokenHelper;
import com.wanshifu.domain.base.tools.FeiShuUtils;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.framework.utils.TransmittableContext;
import com.wanshifu.order.push.enums.PushBusinessCode;
import com.wanshifu.order.offer.domains.enums.code.OfferBusinessCode;
import com.wanshifu.order.push.enums.PushBusinessCode;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.AnnotationUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2023-04-28 13:54
 * @Description mq消息监听器抽象方法，新定义topic继承此类
 * @Version v1
 **/
@Slf4j
public abstract class AbstractMessageTopic<T extends MessageTag>  implements MessageTopic, InitializingBean, ApplicationRunner {

    /**
     * 这里只能通过类型注入。根据指定泛型类型，来注入消息处理器接口
     */
    @Autowired(required = false)
    protected List<T> messageTag;

    @Resource
    private Consumer consumer;

    private Map<String, MessageTag>  messageTagMap = new ConcurrentHashMap<>();


    @Override
    public Action dispatch (Message message){

        //MDC.put(Constant.REQUEST_ID, message.getMsgID());
        String tag = message.getTag();
       // log.info("收到订阅消息： tag:{}， messageId={},  message={}", message.getTag(), message.getMsgID(), JSONObject.toJSONString(new String(message.getBody())));
        if (!messageTagMap.containsKey(message.getTag())){
            if (null == tag) {
                log.error("未知的Tag类型：" + message.getTag());
                return Action.ReconsumeLater;
            }
        }

        MessageTag consumerMessageService = null;
        try {
            consumerMessageService = messageTagMap.get(tag);
            consumerMessageService.handler(message);
            return Action.CommitMessage;

        }catch (Exception e) {
            Action commitMessage = exceptionBusHandler( message,  consumerMessageService.businessScene(), e);
            if (commitMessage != null) {
                return commitMessage;
            }

            int maxReconsumeTime = consumerMessageService.maxReconsumeTime();
            //判断重试次数
            if (consumerMessageService.isReconsumeLater()  && maxReconsumeTime > 0 &&
                    (message.getReconsumeTimes() >= maxReconsumeTime)){
                return Action.CommitMessage;
            }

            // 如果tag任务支持尝试机制则返回延迟消费
            if (consumerMessageService.isReconsumeLater()) {
                return Action.ReconsumeLater;
            }
            return Action.CommitMessage;

        } finally {
            TransmittableContext.context.remove();
            MDC.remove(Constant.REQUEST_ID);
        }

    }



    @Override
    public  void afterPropertiesSet(){
        if (CollectionUtils.isNotEmpty(messageTag)) {
            for (MessageTag consumerMessageService: messageTag) {
                List<ConsumeTag> consumeTagList = new ArrayList<>();
                ConsumeTags consumeTags = AnnotationUtils.findAnnotation(consumerMessageService.getClass(), ConsumeTags.class);
                if (Objects.nonNull(consumeTags) &&  consumeTags.value().length > 0) {
                    ConsumeTag[] value = consumeTags.value();
                    consumeTagList.addAll(Arrays.asList(value));
                }
                ConsumeTag consumeTag = AnnotationUtils.findAnnotation(consumerMessageService.getClass(), ConsumeTag.class);
                if (Objects.nonNull(consumeTag)) {
                    consumeTagList.add(consumeTag);
                }

                if (CollectionUtils.isNotEmpty(consumeTagList)) {
                    consumeTagList.forEach(tag -> {
                        ConsumeTagEnum value = tag.value();
                        String tagValue = value.getValue();
                        String describe = value.getDescribe();
                        boolean reconsumeLate = tag.isReconsumeLate();
                        boolean supportLog = tag.isSupportLog();
                        int maxReconsumeTime = tag.maxReconsumeTime();
                        String targetName = consumerMessageService.getClass().getName();
                        if (messageTagMap.containsKey(tagValue)) {
                            MessageTag sourceConsumerMessageService = messageTagMap.get(tagValue);
                            String source = sourceConsumerMessageService.getClass().getName();
                            throw new IllegalStateException(String.format("%s and %s custom tag is repetition ",source,targetName));
                        }
                        consumerMessageService.setReconsumeLate(reconsumeLate);
                        consumerMessageService.setBusinessScene(describe);
                        consumerMessageService.setSupportLog(supportLog);
                        consumerMessageService.setMaxReconsumeTime(maxReconsumeTime);
                        messageTagMap.put(tagValue,consumerMessageService);
                    });

                }
            }
        }

        if (StringUtils.isEmpty(getTopic())) {
            String name = this.getClass().getName();
            throw new IllegalStateException(String.format("%s  start consumer topic is empty ",name));
        }
    }


    protected abstract String getTopic();



    public void run(ApplicationArguments var1) {

        List<String> customerList = new ArrayList<String>();
        if (MapUtils.isEmpty(messageTagMap)) {
            return;
        }
        customerList.addAll(messageTagMap.keySet());
        // 订阅方法
        String focusTag = "";
        for (int i = 0; i < customerList.size(); i++) {
            if (i == customerList.size() - 1) {
                focusTag += customerList.get(i);
                break;
            }
            focusTag += customerList.get(i) + " || ";
        }
        //自定义策略方法，子类型重写
        initStrategy();
        log.info("启动订阅topic={},tag={}", getTopic(), focusTag);
        consumer.subscribe(getTopic(), focusTag, (message, consumeContext) -> {
            MDC.put(Constant.REQUEST_ID, message.getMsgID());
            log.info("收到外部的订阅消息： tag:{}， messageId={},  message={}", message.getTag(), message.getMsgID(), JSONObject.toJSONString(new String(message.getBody())));
            return dispatch(message);
        });
        log.info("订阅外部的消息成功...");

    }


    /**
     * 业务异常处理
     * @param message
     * @param businessScene
     * @param e
     * @return
     */
    private Action exceptionBusHandler (Message message, String businessScene, Exception e) {

        //是否需要预警
        Boolean isNeedWarn = true;
        //忽略掉特殊不需要预警的场景code
        if (e instanceof BusException) {
            BusException exception = (BusException) e;
            //忽略当前消息异常
            if (PushBusinessCode.isFilterMqMessage( exception.getCode()) ){
                return Action.CommitMessage;
            }
            //忽略预警消息
            if (PushBusinessCode.isFilterFeShuWarn(exception.getCode())){
                isNeedWarn = false;
            }
        }
        String envTag = EnvUtil.getEnvName();

        String errorMsg = new StringBuilder()
                .append("【mq消费失败】").append("\n")
                .append(" 来源服务: ").append("order-push-service").append("\n")
                .append(" 失败原因: ").append(e.getMessage()).append("\n")
                .append(" 环境信息: ").append(envTag).append("\n")
                .append(" globalOrderTraceId: ").append(JSONObject.parseObject(new String(message.getBody())).get("globalOrderTraceId")).append("\n")
                .append(" messageId: ").append(message.getMsgID()).append("\n")
                .append(" 业务场景: ").append(businessScene).append("\n")
                .append(" topic: ").append(message.getTopic()).append("\n")
                .append(" tag: ").append(message.getTag()).append("\n")
                .append(" <at user_id=\"all\">所有人</at>")
                .toString();

        if ( isNeedWarn ) {
            log.error(errorMsg, e);
            //TODO 是否需要记录数据库日志
            // commonService.addErrorlog(null, message.getTag(), new String(message.getBody()), e.getMessage(), message.getMsgID());

            if (0 == message.getReconsumeTimes()) {
                FeiShuUtils.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_ORDER_CENTER_BUSINESS_URL, errorMsg);
            }
        }

        //消息重试 10次仍然失败，则预警处理 （不管是否预警都报警）
        if ( message.getReconsumeTimes() > 10) {
            FeiShuUtils.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_ORDER_CENTER_BUSINESS_URL, errorMsg);
        }

        return null;
    }


    /**
     * 初始化限流策略
     */
    protected void initStrategy() {};

}
