package com.wanshifu.domain.base.tools;

import com.wanshifu.framework.core.BusException;
import com.wanshifu.order.push.enums.PushBusinessCode;
import com.wanshifu.order.offer.domains.enums.code.OfferBusinessCode;
import com.wanshifu.order.push.enums.PushBusinessCode;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: ZhangQiWei
 * @Date: 2019/5/6 14:45
 * @Description: 实体类注解校验工具
 */
public class ValidatorHelper {

    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    private static List<String> selfMessageTemplate = new ArrayList<>();
    static {
        selfMessageTemplate.add("值不在选项范围内");
        selfMessageTemplate.add("must be blank");
        selfMessageTemplate.add("金额格式不正确,正确格式为xxxxx.xx");
    }

    private ValidatorHelper() {

    }

    public static <T> boolean sampleValidate(T target, Class<?>... groups) {
        return validator.validate(target, groups).size() == 0;
    }

    public static void checkParams ( Object entity, Class<?>... groups) {
        if (entity == null) {
            throw new BusException("文本内容无法识别，请重新输入后再试");
        }
        Set<ConstraintViolation<Object>> set = validator.validate(entity, groups);
        if (set.size() == 0) {
            return;
        }
        ConstraintViolation<Object> next = set.iterator().next();
        if(StringUtils.isEmpty(next.getMessage()) || next.getMessageTemplate().contains("{") || selfMessageTemplate.contains(next.getMessageTemplate())){
            throw new BusException(PushBusinessCode.BUS_VALIDATION_ERROR.code,next.getPropertyPath().toString()+":"+next.getMessage());
        }else {
            throw new BusException(PushBusinessCode.BUS_VALIDATION_ERROR.code, next.getMessage());
        }
    }


    public static void check (Object entity, Class<?>... groups) {
        if (entity == null) {
            throw new BusException("文本内容无法识别，请重新输入后再试");
        }
        Set<ConstraintViolation<Object>> set = validator.validate(entity, groups);
        StringBuilder stringBuilder = new StringBuilder();
        if (set.size() == 0) {
            return;
        }
        set.forEach(c -> {
            stringBuilder.append(" ," + c.getPropertyPath().toString() + ":" + c.getMessage());
        });
        String message = stringBuilder.toString().trim();
        if (message.startsWith(",")) {
            message = message.substring(1);
            throw new BusException(message);
        }
    }
}
