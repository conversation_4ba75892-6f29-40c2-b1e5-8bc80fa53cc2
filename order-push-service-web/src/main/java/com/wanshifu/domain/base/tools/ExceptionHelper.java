package com.wanshifu.domain.base.tools;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * @Date 2023-09-15 10:52
 * @Description
 * @Version v1
 **/
public class ExceptionHelper {


    public static final int substrLength = 800;

    /**
     * 截取异常长度
     * @param e
     * @return
     */
    public static String substrException ( Exception e){
        StringWriter stringWriter= new StringWriter();
        PrintWriter writer= new PrintWriter(stringWriter);
        e.printStackTrace(writer);
        StringBuffer buffer= stringWriter.getBuffer();
        String errorInfo = buffer.toString();
        if (errorInfo.length() > substrLength) {
            return errorInfo.substring(0,substrLength);
        }
        return errorInfo;
    }
}
