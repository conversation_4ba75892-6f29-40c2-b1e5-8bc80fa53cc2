package com.wanshifu.domain.base.tools;

import org.springframework.beans.BeanUtils;

import java.util.Objects;
import java.util.function.Supplier;

/**
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @Date 2023-09-23 16:51
 * @Description
 * @Version v1
 **/
public class BeanEnhanceUtil extends  BeanUtils {



    public static <T>T copyBean(Object sourceObj , Supplier<T> supplier){
        if (Objects.isNull(sourceObj)) {
            return null;
        }
        T t = supplier.get();
        copyProperties(sourceObj,t);
        return t;
    }
}
