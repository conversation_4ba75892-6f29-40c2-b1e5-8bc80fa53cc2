<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.infrastructure.mapper.AgentPushDetailMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.AgentPushDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="nobody_offer_hour" jdbcType="INTEGER" property="nobodyOfferHour"/>
        <result column="push_time" jdbcType="TIMESTAMP" property="pushTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">id
    , orderId, nobodyOfferHour, pushTime, abandonTime, createTime, updateTime </sql>

    <update id="updateAbandonTimeByOrderId">
        update agent_push_detail set abandon_time = #{abandonTime},is_self_abandon = 0,is_offer_price_within_time = 0 where order_id = #{orderId}
    </update>

    <update id="updateSecondPushTimeByOrderId">
        update agent_push_detail set second_push_time = #{secondPushTime} where order_id = #{orderId}
    </update>
</mapper>