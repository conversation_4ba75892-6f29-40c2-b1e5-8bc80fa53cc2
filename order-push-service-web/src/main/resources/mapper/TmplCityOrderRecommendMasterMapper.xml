<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanshifu.infrastructure.mapper.TmplCityOrderRecommendMasterMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.TmplCityOrderRecommendMaster">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="global_order_trace_id" jdbcType="BIGINT" property="globalOrderTraceId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="master_id" jdbcType="BIGINT" property="masterId"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="is_push" jdbcType="TINYINT" property="isPush"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

    </resultMap>
    
    <sql id="Base_Column_List">
        id, global_order_trace_id, order_id, master_id, sort, score,create_time, update_time
    </sql>


    <select id="listTmplCityOrderRecommendMaster" resultMap="BaseResultMap"
            parameterType="com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBo">
        select
        <include refid="Base_Column_List"/>
        from tmpl_city_order_recommend_master
        <where>
            global_order_trace_id = #{globalOrderTraceId}
        </where>
        order by sort desc
    </select>

    <select id="listTmplCityOrderRecommendMasterV2" resultMap="BaseResultMap"
            parameterType="com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderRecommendMasterRqtBoV2">
        select
        order_id, master_id, score, create_time,is_push
        from tmpl_city_order_recommend_master
        <where>
            global_order_trace_id = #{globalOrderTraceId}

            <if test="isPush != null">
                AND is_push = #{isPush}
            </if>


            <if test="masterSourceType != null and masterSourceType != ''">
                AND master_source_type = #{masterSourceType}
            </if>

        </where>
        <choose>
            <when test="sortType != null and sortType == 1">
                order by score desc
            </when>
            <otherwise>
                order by score asc
            </otherwise>
        </choose>
        limit #{returnLimit}
    </select>

</mapper>