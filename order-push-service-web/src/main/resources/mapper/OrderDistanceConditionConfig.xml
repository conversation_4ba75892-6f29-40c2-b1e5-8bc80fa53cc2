<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.infrastructure.mapper.OrderDistanceConditionConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.OrderDistanceConditionConfig">
        <id column="condition_config_id" jdbcType="BIGINT" property="conditionConfigId"/>
        <result column="order_type" jdbcType="TINYINT" property="orderType"/>
        <result column="distance_type" jdbcType="TINYINT" property="distanceType"/>
        <result column="category_id" jdbcType="INTEGER" property="categoryId"/>
        <result column="second_division_id" jdbcType="BIGINT" property="secondDivisionId"/>
        <result column="address_distance" jdbcType="BIGINT" property="addressDistance"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">conditionConfigId, orderType, distanceType, categoryId, secondDivisionId, addressDistance, isDelete, createTime, updateTime </sql>
</mapper>