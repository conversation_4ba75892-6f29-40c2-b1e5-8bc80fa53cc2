<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.infrastructure.mapper.OrderDistanceMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.OrderDistance">
        <id column="distance_id" jdbcType="BIGINT" property="distanceId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="distance_order_id" jdbcType="BIGINT" property="distanceOrderId"/>
        <result column="category_id" jdbcType="INTEGER" property="categoryId"/>
        <result column="distance_category_id" jdbcType="INTEGER" property="distanceCategoryId"/>
        <result column="second_division_id" jdbcType="BIGINT" property="secondDivisionId"/>
        <result column="third_division_id" jdbcType="BIGINT" property="thirdDivisionId"/>
        <result column="order_address_distance" jdbcType="BIGINT" property="orderAddressDistance"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="distance_create_time" jdbcType="TIMESTAMP" property="distanceCreateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">distanceId, orderId, distanceOrderId, categoryId, distanceCategoryId, secondDivisionId, thirdDivisionId, orderAddressDistance, isDelete, note, distanceCreateTime, createTime, updateTime </sql>
    <!--批量查询附近单-->
    <select id="selectByDistanceCreateTimeList" resultType="long">
        select distance_id from order_distance where distance_create_time &lt; #{distanceCreateTime} limit #{limitCount}
    </select>

</mapper>