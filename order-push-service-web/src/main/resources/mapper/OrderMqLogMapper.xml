<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.infrastructure.mapper.OrderMqLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.OrderMqLog">
        <id column="log_offset_id" jdbcType="BIGINT" property="logOffsetId"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="topic" jdbcType="VARCHAR" property="topic"/>
        <result column="tag" jdbcType="VARCHAR" property="tag"/>
        <result column="message_desc" jdbcType="VARCHAR" property="messageDesc"/>
        <result column="message_id" jdbcType="VARCHAR" property="messageId"/>
        <result column="global_order_trace_id" jdbcType="BIGINT" property="globalOrderTraceId"/>
        <result column="data_details" jdbcType="LONGVARCHAR" property="dataDetails"/>
        <result column="deal_status" jdbcType="VARCHAR" property="dealStatus"/>
        <result column="fail_reason" jdbcType="VARCHAR" property="failReason"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">log_offset_id, sourceType, topic, tag, messageDesc, messageId, globalOrderTraceId, dataDetails, dealStatus, failReason, remark, createTime, updateTime </sql>


    <select id="selectMasterByMessageId" resultMap="BaseResultMap"  >

        /*FORCE_MASTER*/ SELECT * FROM order_mq_log WHERE message_id = #{messageId} AND source_type = #{sourceType} LIMIT 1;

    </select>

    <select id="pageQueryMessage"  resultMap="BaseResultMap" >
    SELECT * FROM order_mq_log
    -- 偏移量
    WHERE log_offset_id between #{param.startOffsetId} AND #{param.endOffsetId}
      -- 创建时间
      <if test="param.startTime != null  and param.startTime != '' ">
          AND create_time &gt;=#{param.startTime}
      </if>
      <if test="param.endTime != null and param.endTime != ''">
          AND create_time &lt;=#{param.endTime}
      </if>
      <if test="param.sourceType != null and param.sourceType != ''">
          -- 来源类型
          AND  source_type = #{param.sourceType}
      </if>

      <if test="param.topic != null and param.topic != ''">
          AND topic = #{param.topic}
      </if>

      <if test="param.tag != null and param.tag != ''">
            AND tag = #{param.tag}
      </if>

     <if test="param.messageIds != null and param.messageIds.size() >0">
      AND message_id  IN
         <foreach collection="param.messageIds" item="messageId" open="(" separator="," close=")">
               #{messageId}
          </foreach>
     </if>

    <if test="param.dealStatus != null and param.dealStatus != ''">
     AND deal_status = #{param.dealStatus}
    </if>
    order by  log_offset_id asc
    </select>


    <select id="selectMaxAndMinPrimaryId" resultType="com.wanshifu.domain.corn.model.MaxAndMinPrimaryIdBo" >
        SELECT min(`log_offset_id`) as minId  ,max(`log_offset_id`) as maxId ,COUNT(1) as sumCount FROM  `order_mq_log` ;
    </select>

    <delete id="deleteByStartAndEndOfferId" >
        DELETE FROM order_mq_log WHERE log_offset_id between #{startOffsetId} AND #{endOffsetId}
    </delete>

</mapper>