<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanshifu.infrastructure.mapper.OrderPushMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.OrderPush">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="push_id" jdbcType="BIGINT" property="pushId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="master_id" jdbcType="BIGINT" property="masterId"/>
        <result column="appoint_type" jdbcType="TINYINT" property="appointType"/>
        <result column="order_from" jdbcType="VARCHAR" property="orderFrom"/>
        <result column="account_type" jdbcType="VARCHAR" property="accountType"/>
        <result column="order_label" jdbcType="VARCHAR" property="orderLabel"/>
        <!--<result column="offer_number" jdbcType="INTEGER" property="offerNumber" />-->
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="push_time" jdbcType="TIMESTAMP" property="pushTime"/>
        <result column="offer_time" jdbcType="TIMESTAMP" property="offerTime"/>
        <result column="hire_time" jdbcType="TIMESTAMP" property="hireTime"/>
        <result column="limit_offer" jdbcType="TINYINT" property="limitOffer"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="first_pull_time" jdbcType="TIMESTAMP" property="firstPullTime"/>
        <result column="first_view_time" jdbcType="TIMESTAMP" property="firstViewTime"/>
        <result column="stop_offer_time" jdbcType="TIMESTAMP" property="stopOfferTime"/>
        <result column="is_arrived" jdbcType="TINYINT" property="isArrived"/>
        <result column="is_intention" jdbcType="TINYINT" property="isIntention"/>
        <result column="order_division_id" jdbcType="BIGINT" property="orderDivisionId"/>
        <result column="master_latitude" jdbcType="DECIMAL" property="masterLatitude"/>
        <result column="master_longitude" jdbcType="DECIMAL" property="masterLongitude"/>
        <result column="push_distance" jdbcType="BIGINT" property="pushDistance"/>
        <result column="push_distance_type" jdbcType="TINYINT" property="pushDistanceType"/>
        <result column="push_from" jdbcType="TINYINT" property="pushFrom"/>
        <result column="exclusive_flag" jdbcType="TINYINT" property="exclusiveFlag"/>
        <result column="emergency_order_flag" jdbcType="TINYINT" property="emergencyOrderFlag"/>
        <result column="agent_order_flag" jdbcType="TINYINT" property="agentOrderFlag"/>
        <result column="technique_type_ids" jdbcType="VARCHAR" property="techniqueTypeIds"/>
        <result column="category_id" jdbcType="TINYINT" property="categoryId"/>
        <result column="is_pull_order_distance" jdbcType="TINYINT" property="isPullOrderDistance"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="exposure_score" jdbcType="DECIMAL" property="exposureScore"/>
        <result column="push_score" jdbcType="DECIMAL" property="pushScore"/>
        <result column="less_contend_flag" jdbcType="TINYINT" property="lessContendFlag"/>
        <result column="menu_category" jdbcType="TINYINT" property="menuCategory"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="according_distance_push_flag" jdbcType="TINYINT" property="accordingDistancePushFlag"/>
        <result column="according_technology_push_flag" jdbcType="TINYINT" property="accordingTechnologyPushFlag"/>
        <result column="less_contend_flag" jdbcType="TINYINT" property="lessContendFlag"/>
        <result column="province_next_id" jdbcType="INTEGER" property="provinceNextId"/>
        <result column="tmpl_city_flag" jdbcType="INTEGER" property="tmplCityFlag"/>
        <result column="tmpl_city_tip_time" jdbcType="TIMESTAMP" property="tmplCityTipTime"/>
        <result column="must_order_flag" jdbcType="TINYINT" property="mustOrderFlag"/>
        <result column="is_pull_view" jdbcType="TINYINT" property="isPullView"/>
        <result column="master_source_type" jdbcType="VARCHAR" property="masterSourceType"/>

    </resultMap>

    <!--逻辑删除订单推送记录-->
    <update id="softDeleteOrderPush">
        update `ltb_order_push`
        set `is_delete` = 1,
            `note`      = CONCAT(`note`, #{appendNote})
        WHERE `order_id` = #{orderId}
          and `is_delete` = 0
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
          limit #{limitCount}
    </update>


    <select id="selectMasterOrderCategoryNum"
            resultType="com.wanshifu.domain.push.model.GetMasterOrderCategoryRateBo">
        SELECT
        master_id masterId,category_id categoryId,count(*) orderNum
        FROM
        ltb_order_push
        WHERE
        master_id IN
        <foreach collection="masterIdList" item="masterId" open="(" separator="," close=")">
            #{masterId}
        </foreach>
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <include refid="orderPushWhere"></include>
        GROUP BY master_id,category_id
    </select>

    <sql id="orderPushWhere">
        AND is_delete = 0
          AND offer_time IS NULL
          AND (  stop_offer_time IS NULL OR stop_offer_time > NOW())
          AND exclusive_flag != 101
    </sql>



    <select id="selectByMasterIdAndOrderIdsFiler" resultMap="BaseResultMap">
        select
        `order_id`,`master_id`,`appoint_type`,`stop_offer_time`,`is_intention`,`push_distance`,`push_distance_type`,is_pull_order_distance, menu_category
        FROM ltb_order_push
        WHERE master_id = #{masterId}
        and `order_id` IN
        <foreach collection="orderIdIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        and is_delete = 0
        and offer_time is NULL
        and stop_offer_time > #{currentDate}
        and is_intention = 0
        and exclusive_flag != 101
        <if test="tmplCityFlag != null and tmplCityFlag.size() > 0">
            AND tmpl_city_flag in
            <foreach collection="tmplCityFlag" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
    </select>



    <!--查询师傅待报价订单 条件筛选支持到多选-->
    <!-- TODO 请注意,ltb_order_push中加字段时，如果是在该sql中有使用到（查询/筛选/排序），需要在ltb_order_push_score 也加上该字段，同时修改transformers脚本同步该字段 -->
    <!-- TODO 还有下方 analyticWaitOfferCount 的数据库也需要加字段-->
    <select id="selectWaitOfferListV4" resultMap="BaseResultMap"
            parameterType="com.wanshifu.domain.push.model.WaitOfferToV2">
        select
        `order_id`,`master_id`,`appoint_type`,`stop_offer_time`,`is_intention`,`push_distance`,`push_distance_type`,is_pull_order_distance,`first_pull_time`, `push_time`,`menu_category`,
        CASE
        <if test="sortQueryType == 'arrival'">
            WHEN `is_arrived`=1 THEN
            10
        </if>
        WHEN order_from="ikea" THEN
        9
        WHEN exclusive_flag IN (1,101,102,103,104,105,106) THEN
        8
        <if test="sortQueryType == 'tobCustomCategory'">
            WHEN `category_id` IN (6,7,16) AND `account_type` = 'user' AND `order_from` = 'site' THEN
            7
        </if>
        WHEN emergency_order_flag=1 THEN
        6
        WHEN agent_order_flag=1 THEN
        5
        WHEN `order_label`="rigorous_selection" THEN
        4
        WHEN `appoint_type`=3 THEN
        3
        WHEN `appoint_type`=4 THEN
        2
        WHEN is_intention =1 THEN
        0
        ELSE
        1
        END AS first_sort,
           CASE WHEN push_distance = 0 then ********* else push_distance end as push_distance_tmp
        FROM
        <if test="tableName != null">
            <choose>
                <when test="tableName == 'ltb_order_push'">
                    ltb_order_push
                </when>
                <otherwise>
                    ltb_order_push_score
                </otherwise>
            </choose>
        </if>
        WHERE `master_id` = #{masterId} and `is_delete` = 0
        <if test="fromAccount != null and fromAccount != 0">
            AND from_account = #{fromAccount}
        </if>
        <if test="divisionId != null and divisionId.size() > 0">
            AND order_division_id in
            <foreach collection="divisionId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>

        <if test="appointType != null and appointType.size() > 0">
            and
            <foreach collection="appointType" item="itemValue" open="(" separator="or" close=")">
                <if test="null != itemValue and 6 != itemValue">
                    <if test="2 == itemValue">
                        (appoint_type in (2, 3) AND is_intention != 1)
                    </if>
                    <if test="2 != itemValue">
                        (appoint_type = #{itemValue} AND is_intention != 1)
                    </if>
                </if>
                <if test="null != itemValue and 6 == itemValue">
                    is_intention = 1
                </if>
            </foreach>
        </if>

        <if test="orderAccountLabel != null and orderAccountLabel.size() > 0">
            and
            <foreach collection="orderAccountLabel" item="itemValue" open="(" separator="or" close=")">
                <if test="3 == itemValue">
                    order_from = 'ikea'
                </if>
                <if test="4 == itemValue">
                    order_from = 'applet'
                </if>
                <if test="1 == itemValue">
                    ((order_from = 'site' AND account_type != 'enterprise' ) OR order_from = 'thirdpart' OR is_intention
                    = 1)
                </if>
                <if test="2 == itemValue">
                    account_type = 'enterprise'
                </if>
            </foreach>
        </if>

        AND offer_time is null

        AND (stop_offer_time is null OR stop_offer_time > #{currentDateTime})

        <if test="2 == pushFrom">
            AND ( push_time >= #{restStartTime} and (push_from = #{pushFrom} OR exclusive_flag IN (102,103,104)))
        </if>

        <if test="techniqueTypeId != null and techniqueTypeId.size() > 0">
            and
            <foreach collection="techniqueTypeId" item="itemValue" open="(" separator="or" close=")">
                FIND_IN_SET(#{itemValue},technique_type_ids)
            </foreach>
        </if>

        <if test="categoryId != null and categoryId.size() > 0">
            AND category_id in
            <foreach collection="categoryId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>

        <if test="isArrived != null and isArrived.size() > 0">
            AND is_arrived in
            <foreach collection="isArrived" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="null != pushFlag">
            <choose>
                <when test="pushFlag == 0">
                    AND (
                    push_flag = 0 OR according_technology_push_flag = 1
                    <if test="displayLongTailOrderLevel1ServeIds != null and displayLongTailOrderLevel1ServeIds.size() > 0">
                        OR (push_flag = 5  AND level_1_serve_id IN
                        <foreach collection="displayLongTailOrderLevel1ServeIds" item="itemValue" open="(" separator="," close=")">
                            #{itemValue}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="pushFlag == 5">
                    AND (push_flag in (5,6))
                </when>
                <otherwise>
                    AND push_flag = #{pushFlag}
                </otherwise>
            </choose>
        </if>

        <if test="null != menuCategory and (menuCategory == 1 or menuCategory == 2 or menuCategory == 5 or menuCategory == 6 or menuCategory == 8)">
            AND menu_category = #{menuCategory}
        </if>


        <if test="null != menuCategory and menuCategory == 3">
            AND must_order_flag = 1
        </if>


        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(1)">
            AND is_pull_view = 0
        </if>

        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(2)">
            AND must_order_flag = 1
        </if>

        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(3)">
            AND offer = 0
        </if>

        <if test="null == menuCategory or menuCategory != 6">
            AND menu_category != 6
        </if>


        <if test="null == menuCategory or menuCategory != 8">
            AND menu_category != 8
        </if>

        <if test="null != menuCategory and menuCategory == 4">
            AND is_pull_view = 0
        </if>

        <if test="null != menuCategory and menuCategory == 7">
            AND exclusive_good_order_flag = 1
        </if>

        <if test="null != lessContendFlag">
            AND less_contend_flag = #{lessContendFlag}
        </if>
        <if test="null != provinceNextIds and provinceNextIds.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextIds" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="tmplCityFlag != null and tmplCityFlag.size() > 0">
            AND tmpl_city_flag in
            <foreach collection="tmplCityFlag" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>

        AND exclusive_flag != 101
        AND tmpl_city_flag != 3

        <choose>
            <when test="sortQueryType == 'newOrder'">
                ORDER BY push_time DESC,first_sort DESC
            </when>
            <when test="sortQueryType == 'pushDistance'">
                ORDER BY push_distance_tmp asc,push_distance ASC,push_time DESC
            </when>
            <when test="sortQueryType == 'residentDistance'">
                ORDER BY push_distance_tmp,push_distance ,push_time DESC
            </when>
            <when test="sortQueryType == 'smart'">
                ORDER BY score DESC,first_sort DESC,push_time DESC
            </when>
            <otherwise>
                ORDER BY first_sort DESC,push_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectSmartList" resultMap="BaseResultMap"
            parameterType="com.wanshifu.domain.push.model.WaitOfferToV2">
        select
        `order_id`,`master_id`,`appoint_type`,`stop_offer_time`,`is_intention`,`push_distance`,`push_distance_type`,is_pull_order_distance,`first_pull_time`, `push_time`,`menu_category`,`exposure_score`
        FROM
        ltb_order_push
        WHERE `master_id` = #{masterId} and `is_delete` = 0
        <if test="fromAccount != null and fromAccount != 0">
            AND from_account = #{fromAccount}
        </if>
        <if test="divisionId != null and divisionId.size() > 0">
            AND order_division_id in
            <foreach collection="divisionId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>

        <if test="appointType != null and appointType.size() > 0">
            and
            <foreach collection="appointType" item="itemValue" open="(" separator="or" close=")">
                <if test="null != itemValue and 6 != itemValue">
                    <if test="2 == itemValue">
                        (appoint_type in (2, 3) AND is_intention != 1)
                    </if>
                    <if test="2 != itemValue">
                        (appoint_type = #{itemValue} AND is_intention != 1)
                    </if>
                </if>
                <if test="null != itemValue and 6 == itemValue">
                    is_intention = 1
                </if>
            </foreach>
        </if>

        <if test="orderAccountLabel != null and orderAccountLabel.size() > 0">
            and
            <foreach collection="orderAccountLabel" item="itemValue" open="(" separator="or" close=")">
                <if test="3 == itemValue">
                    order_from = 'ikea'
                </if>
                <if test="4 == itemValue">
                    order_from = 'applet'
                </if>
                <if test="1 == itemValue">
                    ((order_from = 'site' AND account_type != 'enterprise' ) OR order_from = 'thirdpart' OR is_intention
                    = 1)
                </if>
                <if test="2 == itemValue">
                    account_type = 'enterprise'
                </if>
            </foreach>
        </if>

        AND offer_time is null

        AND (stop_offer_time is null OR stop_offer_time > #{currentDateTime})

        <if test="2 == pushFrom">
            AND ( push_time >= #{restStartTime} and (push_from = #{pushFrom} OR exclusive_flag IN (102,103,104)))
        </if>

        <if test="techniqueTypeId != null and techniqueTypeId.size() > 0">
            and
            <foreach collection="techniqueTypeId" item="itemValue" open="(" separator="or" close=")">
                FIND_IN_SET(#{itemValue},technique_type_ids)
            </foreach>
        </if>

        <if test="categoryId != null and categoryId.size() > 0">
            AND category_id in
            <foreach collection="categoryId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>

        <if test="isArrived != null and isArrived.size() > 0">
            AND is_arrived in
            <foreach collection="isArrived" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="null != pushFlag">
            <choose>
                <when test="pushFlag == 0">
                    AND (
                        push_flag = 0 OR according_technology_push_flag = 1
                        <if test="displayLongTailOrderLevel1ServeIds != null and displayLongTailOrderLevel1ServeIds.size() > 0">
                            OR (push_flag = 5 AND level_1_serve_id IN
                                <foreach collection="displayLongTailOrderLevel1ServeIds" item="itemValue" open="(" separator="," close=")">
                                    #{itemValue}
                                </foreach>
                            )
                        </if>
                    )
                </when>
                <when test="pushFlag == 5">
                    AND push_flag IN (5, 6)
                </when>
                <otherwise>
                    AND push_flag = #{pushFlag}
                </otherwise>
            </choose>
        </if>

        <if test="null != menuCategory and (menuCategory == 1 or menuCategory == 2 or menuCategory == 5 or menuCategory == 6 or menuCategory == 8)">
            AND menu_category = #{menuCategory}
        </if>


        <if test="null != menuCategory and menuCategory == 3">
            AND must_order_flag = 1
        </if>


        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(1)">
            AND is_pull_view = 0
        </if>

        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(2)">
            AND must_order_flag = 1
        </if>

        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(3)">
            AND offer = 0
        </if>


        <if test="null != menuCategory and menuCategory == 4">
            AND is_pull_view = 0
        </if>

        <if test="null == menuCategory or menuCategory != 6">
            AND menu_category != 6
        </if>


        <if test="null != menuCategory and menuCategory == 7">
            AND exclusive_good_order_flag = 1
        </if>

        <if test="null == menuCategory or menuCategory != 8">
            AND menu_category != 8
        </if>

        <if test="null != lessContendFlag">
            AND less_contend_flag = #{lessContendFlag}
        </if>
        <if test="null != provinceNextIds and provinceNextIds.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextIds" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="tmplCityFlag != null and tmplCityFlag.size() > 0">
            AND tmpl_city_flag in
            <foreach collection="tmplCityFlag" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>

        AND exclusive_flag != 101
        AND tmpl_city_flag != 3
        ORDER BY score DESC,push_time DESC
    </select>

    <select id="listTmplCityOrderPush" resultMap="BaseResultMap"
            parameterType="com.wanshifu.order.push.request.push.TmplCityOrderPushRqt">
        select `order_id`,`master_id`,`appoint_type`,`stop_offer_time`,`is_intention`,`push_distance`,
               `push_distance_type`,is_pull_order_distance,`first_pull_time`, `push_time`
        from ltb_order_push
        <where>
            master_id = #{masterId}
            and tmpl_city_flag = 1
            and is_delete = 0
            and tmpl_city_tip_time is null
            <if test="null != provinceNextIdList and provinceNextIdList.size() > 0">
                and `province_next_id` in
                <foreach collection="provinceNextIdList" item="itemValue" open="(" separator="," close=")">
                    #{itemValue}
                </foreach>
            </if>
        </where>
        order by push_time desc
    </select>

    <select id="selectOrderPushNoOfferByOrderId" resultMap="BaseResultMap"
            parameterType="com.wanshifu.order.push.request.push.NoOfferByOrderIdRqt">
        select *
        from ltb_order_push
        <where>
            order_id = #{orderId}
            AND offer_time is null
            AND  stop_offer_time > #{currentDateTime}
            and is_delete = 0
            <if test="null != provinceNextIds and provinceNextIds.size() > 0">
                and `province_next_id` in
                <foreach collection="provinceNextIds" item="itemValue" open="(" separator="," close=")">
                    #{itemValue}
                </foreach>
            </if>
        </where>
        order by push_time desc
    </select>

    <!--统计师傅待报价列表指标，通过analyticDB查询-->
    <select id="analyticWaitOfferCount"
            parameterType="com.wanshifu.domain.push.model.WaitOfferCountGateway"
            resultType="java.lang.Integer">
        select
        COUNT(1)
        FROM ${tableName}
        <include refid="waitListFilterSortWhere"></include>
    </select>

    <select id="selectExpiredOrderPush" resultType="java.lang.Long">
        select push_id
        from ${tableName}
        where `stop_offer_time` <![CDATA[ <= ]]> #{stopOfferTime}
        order by `stop_offer_time` asc limit #{limit}
    </select>




    <sql id= "waitListFilterSortWhere" >
        WHERE `master_id` = #{masterId} and `is_delete` = 0
        <if test="divisionId != null and divisionId.size() > 0">
            AND order_division_id in
            <foreach collection="divisionId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="appointType != null and appointType.size() > 0">
            and
            <foreach collection="appointType" item="itemValue" open="(" separator="or" close=")">
                <if test="null != itemValue and 6 != itemValue">
                    <if test="2 == itemValue">
                        (appoint_type in (2, 3) AND is_intention != 1)
                    </if>
                    <if test="2 != itemValue">
                        (appoint_type = #{itemValue} AND is_intention != 1)
                    </if>
                </if>
                <if test="null != itemValue and 6 == itemValue">
                    is_intention = 1
                </if>
            </foreach>
        </if>
        <if test="orderAccountLabel != null and orderAccountLabel.size() > 0">
            and
            <foreach collection="orderAccountLabel" item="itemValue" open="(" separator="or" close=")">
                <if test="3 == itemValue">
                    order_from = 'ikea'
                </if>
                <if test="4 == itemValue">
                    order_from = 'applet'
                </if>
                <if test="1 == itemValue">
                    ((order_from = 'site' AND account_type != 'enterprise' ) OR order_from = 'thirdpart' OR is_intention
                    = 1)
                </if>
                <if test="2 == itemValue">
                    account_type = 'enterprise'
                </if>
            </foreach>
        </if>
        AND offer_time is null
        AND (stop_offer_time is null OR stop_offer_time > #{currentDateTime})
        <if test="2 == pushFrom">
            AND ( push_time >= #{restStartTime} and (push_from = #{pushFrom} OR exclusive_flag IN (102,103,104)))
        </if>
        <if test="techniqueTypeId != null and techniqueTypeId.size() > 0">
            and
            <foreach collection="techniqueTypeId" item="itemValue" open="(" separator="or" close=")">
                FIND_IN_SET(#{itemValue},technique_type_ids)
            </foreach>
        </if>
        <if test="categoryId != null and categoryId.size() > 0">
            AND category_id in
            <foreach collection="categoryId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="isArrived != null and isArrived.size() > 0">
            AND is_arrived in
            <foreach collection="isArrived" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <if test="null != pushFlag">
            <choose>
                <when test="pushFlag == 0">
                    AND (
                    push_flag = 0 OR according_technology_push_flag = 1
                    <if test="displayLongTailOrderLevel1ServeIds != null and displayLongTailOrderLevel1ServeIds.size() > 0">
                        OR (push_flag = 5 AND level_1_serve_id IN
                        <foreach collection="displayLongTailOrderLevel1ServeIds" item="itemValue" open="(" separator="," close=")">
                            #{itemValue}
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <when test="pushFlag == 5">
                    AND (push_flag in (5,6) )
                </when>
                <otherwise>
                    AND push_flag = #{pushFlag}
                </otherwise>
            </choose>
        </if>
        <if test="null != menuCategory">
            AND menu_category = #{menuCategory}
        </if>
        <if test="null != lessContendFlag">
            AND less_contend_flag = #{lessContendFlag}
        </if>
        <if test="null != mustOrderFlag">
            AND must_order_flag = #{mustOrderFlag}
        </if>

        <if test="null != exclusiveGoodOrderFlag">
            AND exclusive_good_order_flag = #{exclusiveGoodOrderFlag}
        </if>


        AND menu_category != 8
        AND exclusive_flag != 101
        AND tmpl_city_flag != 3

        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(1)">
            AND is_pull_view = 0
        </if>

        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(2)">
            AND must_order_flag = 1
        </if>
        <if test="orderFlags != null and orderFlags.size() > 0 and orderFlags.contains(3)">
            AND offer = 0
        </if>


        <if test="null != provinceNextIdList and provinceNextIdList.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextIdList" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>

    </sql>

    <!--推荐订单查询-->
    <select id="selectWaitOfferNoPageList" resultMap="BaseResultMap"
            parameterType="com.wanshifu.domain.push.model.WaitOfferNoPageBo">
        select `order_id` FROM `ltb_order_push`
        WHERE `master_id` = #{masterId} and `is_delete` = 0
        AND is_intention = 0

        <if test="exclusiveAppointType != null">
            AND appoint_type != #{exclusiveAppointType}
        </if>


        <if test="thirdDivisionIds != null">
            AND `order_division_id` in
            <foreach collection="thirdDivisionIds" item="divisionId" open="(" separator="," close=")">
                #{divisionId}
            </foreach>
        </if>
        <if test="tmplCityFlag != null and tmplCityFlag.size() > 0">
            AND tmpl_city_flag in
            <foreach collection="tmplCityFlag" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        AND offer_time is null

        AND (stop_offer_time is null OR stop_offer_time > #{currentDateTime})
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
    </select>

    <select id="selectIocWaitOfferFilter" resultMap="BaseResultMap">
        SELECT order_id, appoint_type
        FROM ltb_order_push
        WHERE master_id = #{masterId}
          and is_delete = 0
          and offer_time is NULL
          and stop_offer_time > #{currentDate}
          and is_intention = 0
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        order by stop_offer_time DESC LIMIT #{queryNumber};
    </select>

    <select id="selectWaitOfferMasterIdsByOrderId" resultType="java.lang.Long">
        select master_id from ltb_order_push
        where order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        <include refid="orderPushWhere"></include>
    </select>

    <select id="selectInviteMasterByOrderId" resultType="java.lang.Long">
        select master_id
        FROM ltb_order_push
        WHERE order_id = #{orderId}
          and is_delete = 0
          and offer_time is NULL
          and stop_offer_time > #{currentDateTime}
          and is_intention = 0
          and exclusive_flag != 101
        and first_view_time is NULL
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        order by push_time limit 10
    </select>

    <select id="selectMasterCategorySelector" resultType="java.lang.Long">
        SELECT DISTINCT category_id
        FROM ltb_order_push
        WHERE master_id = #{masterId}
          AND `is_delete` = 0
          AND offer_time IS NULL
          AND (stop_offer_time IS NULL OR stop_offer_time > #{currentDateTime})
          AND exclusive_flag != 101
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
    </select>


    <select id="selectOrderPushForNotice" resultMap="BaseResultMap">
        select *
        FROM ltb_order_push
        WHERE order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
          and is_intention = 0
          and exclusive_flag != 101
          and (push_flag = 0
        <if test="displayLongTailOrderLevel1ServeIds != null and displayLongTailOrderLevel1ServeIds.size() > 0">
            OR (push_flag = 5 AND level_1_serve_id IN
            <foreach collection="displayLongTailOrderLevel1ServeIds" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
            )
        </if>
        )

    </select>

    <select id="selectOrderPushByOrderId" resultMap="BaseResultMap">
        select *
        FROM ltb_order_push
        WHERE order_id = #{orderId}
          and is_delete = 0
          and offer_time is NULL
          and stop_offer_time > #{currentDateTime}
          and is_intention = 0
          and exclusive_flag != 101
          and push_flag = 0
        and first_view_time is NULL
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        order by push_time limit 1000
    </select>


    <select id="listByMasterDivisionIdForAppletUnLogin" resultMap="BaseResultMap">
        select distinct order_id, appoint_type, stop_offer_time
        FROM ltb_order_push
        <where>
            <if test="masterDivisionId != null">
                AND order_division_id = #{masterDivisionId}
            </if>
            <if test="stopOfferTime != null">
                AND stop_offer_time > #{stopOfferTime}
            </if>
            <if test="pushTime != null">
                AND push_time > #{pushTime}
            </if>

            <if test="orderFrom != null and orderFrom != ''">
                AND order_from = #{orderFrom}
            </if>

            <if test="null != provinceNextId and provinceNextId.size() > 0">
                and `province_next_id` in
                <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                    #{itemValue}
                </foreach>
            </if>
            and is_delete = 0
            and is_intention = 0
        </where>
        order by push_id desc limit 20
    </select>

    <delete id="deleteByOrderIdAndLimit">
        delete from ltb_order_push
        where order_id = #{orderId}
          and is_delete = #{isDelete}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </delete>

    <update id="updateMenuCategoryByOrderIdAndLimit">
        update `ltb_order_push` set `menu_category` = #{menuCategory}
        WHERE order_id = #{orderId}
        and is_delete = 0
        and `menu_category` = 0
        and push_flag = 0
        and offer_time is NULL
        AND (stop_offer_time is null OR stop_offer_time > #{currentDateTime})
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </update>

    <update id="updateMenuCategoryByOrderIdAndLimitV2">
        update `ltb_order_push` set `menu_category` = #{menuCategory}
        WHERE order_id = #{orderId}
        and is_delete = 0
        and `menu_category` = 2
        and offer_time is NULL
        AND (stop_offer_time is null OR stop_offer_time > #{currentDateTime})
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </update>

    <update id="updateIsArrivedByOrderId">
        update `ltb_order_push` set `is_arrived` =1
        WHERE order_id = #{orderId}
        and `is_arrived` = 2
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </update>

    <update id="updateNotArrivedByOrderId">
        update `ltb_order_push` set `is_arrived` =2
        WHERE order_id = #{orderId}
        and `is_arrived` = 1
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </update>

    <update id="updateOfferByOrderIdAndLimit">
        update `ltb_order_push` set `offer` = 1
        WHERE order_id = #{orderId}
        and is_delete = 0
        and offer = 0
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </update>

    <delete id="deleteByOrderId">
        delete from ltb_order_push
        where order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
            limit #{limit}
    </delete>

    <delete id="deleteExpiredOrderPushByPushIds">
        delete from ${tableName}
        where push_id in
        <foreach collection="pushIds" item="itemValue" open="(" separator="," close=")">
            #{itemValue}
        </foreach>
    </delete>


    <select id="selectMasterOrderPushByAccount" resultMap="BaseResultMap">
        select *
        FROM ltb_order_push
        WHERE is_delete = 0
        and offer_time is NULL
        and stop_offer_time > #{currentDateTime}
        and is_intention = 0
        and exclusive_flag != 101
        and tmpl_city_flag = 0
        and from_account = #{accountId}
        and account_type = #{accountType}
        and master_id = #{masterId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit 300
    </select>

    <select id="getOrderPushByOrderIdAndOffer" resultMap="BaseResultMap">
        select *
        FROM ltb_order_push
        WHERE is_delete = 0
        and order_id = #{orderId}
        and offer = #{offer}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit 1
    </select>



    <select id="selectOrderPushList" resultMap="BaseResultMap">
        select *
        FROM ltb_order_push
        WHERE order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        and is_intention = 0
        and exclusive_flag != 101
        and push_flag = 0
        and according_technology_push_flag = 1
        order by push_score desc
        limit #{limitCount}

    </select>

    <select id="selectOrderPushListByPushScore" resultMap="BaseResultMap">
        select push_id,master_id,order_id
        FROM ltb_order_push
        WHERE order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        and is_intention = 0
        and according_technology_push_flag = 1
        and is_delete = 0
        and offer_time is null
        and (  stop_offer_time IS NULL OR stop_offer_time > NOW())
        order by push_score desc

    </select>

    <select id="selectSiteDetailNearbyMasterListAndLimit" resultMap="BaseResultMap">
        select master_id,push_score,push_distance
        FROM ltb_order_push
        WHERE order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        and is_intention = 0
        and is_delete = 0
        and offer_time is null
        and (  stop_offer_time IS NULL OR stop_offer_time > NOW())
        order by push_score desc
        limit #{limitCount}

    </select>

    <delete id="deleteByMasterIdAndCategoryId">
        delete from ltb_order_push
        where master_id = #{masterId}
        AND category_id = #{categoryId}
        and menu_category = 6
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </delete>


    <select id="selectFamilyOrderPushList" resultMap="BaseResultMap">
        select *
        FROM ltb_order_push
        WHERE master_id = #{masterId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        and order_from = 'applet'
        and account_type = 'user'
        and offer_time is null
        and exclusive_flag != 101
        and push_flag = 0
        and according_technology_push_flag = 1
        and (  stop_offer_time IS NULL OR stop_offer_time > NOW())
        and is_delete = 0
        order by push_time desc
        limit #{limitCount}

    </select>


    <select id="selectPushMasterList" resultMap="BaseResultMap">
        select master_id,push_distance,first_view_time,master_latitude,master_longitude
        FROM ltb_order_push
        WHERE order_id = #{orderId}
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>

        <if test="null != distance and distance > 0">
            and push_distance &lt; #{distance}
        </if>

    </select>


    <!--统计查看订单的师傅数量-->
    <select id="orderPushCount"
            resultType="java.lang.Integer">
    select
    COUNT(1)
    FROM ltb_order_push WHERE order_id = #{orderId}
    <if test="null != provinceNextId and provinceNextId.size() > 0">
        and `province_next_id` in
        <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
            #{itemValue}
        </foreach>
    </if>

    <if test="mode != null and mode == 1">
        and first_view_time is not null
    </if>
    </select>


    <delete id="updateFullTimeExclusiveOrderFlag">
        update ltb_order_push set menu_category = 0
        where master_id = #{masterId}
        and menu_category = 8
        <if test="null != provinceNextId and provinceNextId.size() > 0">
            and `province_next_id` in
            <foreach collection="provinceNextId" item="itemValue" open="(" separator="," close=")">
                #{itemValue}
            </foreach>
        </if>
        limit #{limit}
    </delete>

</mapper>