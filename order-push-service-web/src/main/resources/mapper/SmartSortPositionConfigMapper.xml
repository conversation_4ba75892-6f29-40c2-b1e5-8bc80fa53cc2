<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanshifu.infrastructure.mapper.SmartSortPositionConfigMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.SmartSortPositionConfig">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="city_id" jdbcType="INTEGER" property="cityId"/>
        <result column="position_type" jdbcType="INTEGER" property="positionType"/>
        <result column="position_config_rule" jdbcType="VARCHAR" property="positionConfigRule"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectAllConfigs" resultMap = "BaseResultMap">
        select * from smart_sort_position_config
    </select>

</mapper>