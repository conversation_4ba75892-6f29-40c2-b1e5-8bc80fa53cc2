<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.infrastructure.mapper.NoOfferFilterMasterMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.NoOfferFilterMaster">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="global_order_id" jdbcType="BIGINT" property="globalOrderId"/>
        <result column="master_id" jdbcType="BIGINT" property="masterId"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="put_tag_flag" jdbcType="INTEGER" property="putTagFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>

    <sql id="Base_Column_List">id, order_id,global_order_id, master_id, score, put_tag_flag,create_time, update_time</sql>

</mapper>