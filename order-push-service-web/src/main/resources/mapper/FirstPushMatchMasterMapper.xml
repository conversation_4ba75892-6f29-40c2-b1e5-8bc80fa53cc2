<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanshifu.infrastructure.mapper.FirstPushMatchMasterMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.FirstPushMatchMaster">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="global_order_id" jdbcType="BIGINT" property="globalOrderId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="match_master_num" jdbcType="INTEGER" property="matchMasterNum"/>


    </resultMap>

</mapper>