<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanshifu.infrastructure.mapper.TmplCityOrderPushLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.TmplCityOrderPushLog">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="push_event" jdbcType="VARCHAR" property="pushEvent"/>
        <result column="operator_type" jdbcType="VARCHAR" property="operatorType"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="main_master_ids" jdbcType="VARCHAR" property="mainMasterIds"/>
        <result column="reserve_master_ids" jdbcType="VARCHAR" property="reserveMasterIds"/>
        <result column="normal_master_ids" jdbcType="VARCHAR" property="normalMasterIds"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>

    </resultMap>

    <sql id="Base_Column_List">
        id, order_id, push_event, operator_type, operator, main_master_ids, reserve_master_ids,normal_master_ids,create_time
    </sql>


    <select id="listTmplCityOrderPushLog" resultMap="BaseResultMap"
            parameterType="com.wanshifu.domain.tmplcity.bo.ListTmplCityOrderPushLogRqtBo">
        select
        <include refid="Base_Column_List"/>
        from tmpl_city_order_push_log
        <where>
            order_id = #{orderId}
        </where>
    </select>

</mapper>