<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanshifu.infrastructure.mapper.OrderPushDeleteLogMapper">
  <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.OrderPushDeleteLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="log_id" jdbcType="BIGINT" property="logId" />
    <result column="master_id" jdbcType="BIGINT" property="masterId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="handle_type" jdbcType="VARCHAR" property="handleType" />
    <result column="request_data" jdbcType="VARCHAR" property="requestData" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>