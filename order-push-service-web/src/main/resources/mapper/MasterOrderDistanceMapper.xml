<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.infrastructure.mapper.MasterOrderDistanceMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.infrastructure.MasterOrderDistance">
        <id column="order_distance_id" jdbcType="BIGINT" property="orderDistanceId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="distance_order_id" jdbcType="BIGINT" property="distanceOrderId"/>
        <result column="master_id" jdbcType="BIGINT" property="masterId"/>
        <result column="order_serve_id" jdbcType="BIGINT" property="orderServeId"/>
        <result column="category_id" jdbcType="INTEGER" property="categoryId"/>
        <result column="distance_order_serve_status" jdbcType="TINYINT" property="distanceOrderServeStatus"/>
        <result column="order_address_distance" jdbcType="BIGINT" property="orderAddressDistance"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="distance_create_time" jdbcType="TIMESTAMP" property="distanceCreateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">orderDistanceId, orderId, distanceOrderId, masterId, orderServeId, categoryId, distanceOrderServeStatus, orderAddressDistance, isDelete, note, distanceCreateTime, createTime, updateTime </sql>

    <!--批量查询订单距离-->
    <select id="selectByDistanceCreateTimeList" resultType="long">
        select order_distance_id from master_order_distance where distance_create_time &lt; #{distanceCreateTime} limit #{limitCount}
    </select>
</mapper>