package com.wanshifu.infrastructure.repository;

import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * OrderPushScoreRepository Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>08/06/2024</pre>
 */
@SpringBootTest()
@RunWith(SpringRunner.class)
public class OrderPushScoreRepositoryTest {


    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: deleteByMasterIdAndOrderId(Long provinceNextId, Long masterId, Long orderId)
     */
    @Test
    public void testDeleteByMasterIdAndOrderId() throws Exception {
//        orderPushScoreRepository.deleteByMasterIdAndOrderId(440300L, 61170665832L, 61494566994L);
        /*MasterOrderScoreVo m = new MasterOrderScoreVo();
        m.setMasterId(61170665832L);
        m.setOrderId(61494564716L);
        m.setScore(new BigDecimal(87));
        m.setProvinceNextId(440300L);
        orderPushScoreRepository.updateMasterOrderScore(m);
        Thread.sleep(100000000000L);
        System.out.println("deleteByMasterIdAndOrderId");*/
//TODO: Test goes here... 
    }

    /**
     * Method: deleteByOrderIdAndLimit(Long provinceNextId, Long orderId, Integer isDelete, Integer limit)
     */
    @Test
    public void testDeleteByOrderIdAndLimit() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: updateMasterOrderScore(MasterOrderScoreVo masterOrderScoreVo)
     */
    @Test
    public void testUpdateMasterOrderScore() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: deleteByMasterIdAndOrderIds(Long provinceNextId, Long masterId, List<Long> orderIds)
     */
    @Test
    public void testDeleteByMasterIdAndOrderIds() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: deleteByOrderIdAndMasterIds(Long provinceNextId, Long orderId, List<Long> masterIds)
     */
    @Test
    public void testDeleteByOrderIdAndMasterIds() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: deleteByOrderId(Long provinceNextId, Long orderId, Integer limit)
     */
    @Test
    public void testDeleteByOrderId() throws Exception {
//TODO: Test goes here... 
    }


} 
