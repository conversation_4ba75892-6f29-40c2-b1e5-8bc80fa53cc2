package com.wanshifu.infrastructure.gatewayimpl;

import com.wanshifu.master.order.domains.enums.OfferPriceEndReasonType;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

/**
 * OrderPushManagerGatewayImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>07/23/2024</pre>
 */
@SpringBootTest()
@RunWith(SpringRunner.class)
public class OrderPushManagerGatewayImplTest {

    @Resource
    private OrderPushManagerGatewayImpl orderPushManagerGatewayImpl;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: selectByMasterIdAndOrderIds(Long provinceNextId, Long masterId, List<Long> orderIdIdList)
     */
    @Test
    public void testSelectByMasterIdAndOrderIds() throws Exception {
//        orderPushManagerGatewayImpl.softDeleteMasterOrderPush(440300L, 5248490258L, Lists.newArrayList(61486259399L, 61486259356L), "删除");
//        orderPushManagerGatewayImpl.clearOrderPushNew(440300L, Lists.newArrayList(61486259399L, 61486259356L), 5248490258L, OfferPriceEndReasonType.MODIFY_ORDER);
//        orderPushManagerGatewayImpl.clearOrderPushByOrderId(440300L, 61486308745L, "删除", true);
//        orderPushManagerGatewayImpl.deleteInfoOrderPush(440300L, 61486191860L,  new Date());
//TODO: Test goes here... 
    }

    /**
     * Method: selectInviteMasterByOrderId(Long provinceNextId, Long orderId)
     */
    @Test
    public void testSelectInviteMasterByOrderId() throws Exception {

//TODO: Test goes here... 
    }

    /**
     * Method: selectPushOrderByMasterId(Long provinceNextId, Long masterId)
     */
    @Test
    public void testSelectPushOrderByMasterId() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: queryOrderToMasterIdsPushInfo(Long provinceNextId, Long orderId, List<Long> masterIds)
     */
    @Test
    public void testQueryOrderToMasterIdsPushInfo() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: masterPullOrderList(Long provinceNextId, Long masterId, List<Long> orderIdList)
     */
    @Test
    public void testMasterPullOrderList() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: updateTmplCityTime(Long provinceNextId, List<OrderPush> orderPushList)
     */
    @Test
    public void testUpdateTmplCityTime() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: modifyMasterServeRegions(Long provinceNextId, Long masterId, List<Long> removeServeThirdDivisionIdList, List<Long> removeServeFourthDivisionIdList, List<OrderPush> orderPushList, Map<Long, OrderBase> orderBaseMap)
     */
    @Test
    public void testModifyMasterServeRegions() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: removeTechnologys(Long provinceNextId, Long masterId, List<Long> removeTechnologyIds, Map<Long, OrderBase> orderBaseMap, List<OrderPush> orderPushList, String selectedTechnologyIds)
     */
    @Test
    public void testRemoveTechnologys() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: clearOrderPushNew(Long provinceNextId, List<Long> orderIds, Long masterId, OfferPriceEndReasonType offerPriceEndReasonType)
     */
    @Test
    public void testClearOrderPushNew() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: clearOrderPush(Long provinceNextId, Long orderId, Long masterId)
     */
    @Test
    public void testClearOrderPush() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: softDeleteOrderPush(Long provinceNextId, Long orderId, String appendNote)
     */
    @Test
    public void testSoftDeleteOrderPush() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: softDeleteMasterOrderPush(Long provinceNextId, Long masterId, List<Long> orderIdList, String appendNote)
     */
    @Test
    public void testSoftDeleteMasterOrderPush() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: softDeleteMastersOrderPush(Long provinceNextId, Long orderId, List<Long> masterIds, String appendNote)
     */
    @Test
    public void testSoftDeleteMastersOrderPush() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: deleteOrderPushByOrderIdAndMasterIds(Long provinceNextId, Long orderId, Set<Long> masterIds)
     */
    @Test
    public void testDeleteOrderPushByOrderIdAndMasterIds() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: updatePushDistanceAndLngLat(List<BatchUpdatePushDistanceRqt.MasterAddressInfoList> masterAddressInfoList, BigDecimal masterLongitude, BigDecimal masterLatitude)
     */
    @Test
    public void testUpdatePushDistanceAndLngLat() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: modifyOrderResetMasterDistance(ModifyOrderResetMasterDistance modifyOrderResetMasterDistance)
     */
    @Test
    public void testModifyOrderResetMasterDistance() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: pushAsync(Long provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, Map<Long, MasterAddressInfo> masterAddressInfoMap, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag, Integer pushFlag, Integer accordingDistancePushFlag, Integer exclusiveFlag)
     */
    @Test
    public void testPushAsync() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: pushSync(Long provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, Map<Long, MasterAddressInfo> masterAddressInfoMap, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag, Integer pushFlag, Integer accordingDistancePushFlag, Integer exclusiveFlag)
     */
    @Test
    public void testPushSync() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: setDistanceAndArriveStatus(OrderPush updateOrderPush, OrderLogisticsInfo orderLogisticsInfo)
     */
    @Test
    public void testSetDistanceAndArriveStatus() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: clearOrderPushByOrderId(Long provinceNextId, Long orderId, String appendNote, Boolean isNeedAsyncDelete)
     */
    @Test
    public void testClearOrderPushByOrderId() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: offerPrice(Long provinceNextId, Long orderId, Long masterId, Date offerTime, String appendNote)
     */
    @Test
    public void testOfferPrice() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: grabDefinite(Long provinceNextId, Long orderId, Long masterId, Date hireTime)
     */
    @Test
    public void testGrabDefinite() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: lastOfferPrice(Long provinceNextId, Long orderId, String appendNote)
     */
    @Test
    public void testLastOfferPrice() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: hireMaster(Long provinceNextId, Long orderId, Long masterId, Date hireTime, String note)
     */
    @Test
    public void testHireMaster() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: deleteInfoOrderBaseOrderPush(Long provinceNextId, Long orderId, Long masterId)
     */
    @Test
    public void testDeleteInfoOrderBaseOrderPush() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: softDeleteInfoOrderPush(Long provinceNextId, Long orderId)
     */
    @Test
    public void testSoftDeleteInfoOrderPush() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: deleteInfoOrderOrderPush(Long provinceNextId, Long orderId, Long masterId, Date closeTime)
     */
    @Test
    public void testDeleteInfoOrderOrderPush() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: masterDisinterestOrder(Long provinceNextId, Long orderId, Long masterId)
     */
    @Test
    public void testMasterDisinterestOrder() throws Exception {
//TODO: Test goes here... 
    }


    /**
     * Method: clearPush(Long provinceNextId, Long masterId, List<Long> orderIdListTarget, String note, OfferPriceEndReasonType offerPriceEndReasonType)
     */
    @Test
    public void testClearPush() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = OrderPushManagerGatewayImpl.getClass().getMethod("clearPush", Long.class, Long.class, List<Long>.class, String.class, OfferPriceEndReasonType.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: updateMasterAddress(List<BatchUpdatePushDistanceRqt.MasterAddressInfoList> masterAddressInfoList, BigDecimal masterLongitude, BigDecimal masterLatitude)
     */
    @Test
    public void testUpdateMasterAddress() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = OrderPushManagerGatewayImpl.getClass().getMethod("updateMasterAddress", List<BatchUpdatePushDistanceRqt.MasterAddressInfoList>.class, BigDecimal.class, BigDecimal.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: handlerInsertOrderPush(Long provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, Map<Long, MasterAddressInfo> masterAddressInfoMap, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag, Integer pushFlag, Integer accordingDistancePushFlag, Integer exclusiveFlag)
     */
    @Test
    public void testHandlerInsertOrderPush() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = OrderPushManagerGatewayImpl.getClass().getMethod("handlerInsertOrderPush", Long.class, String.class, PushingOrderCompositeResp.class, Map<Long,.class, Date.class, Integer.class, Integer.class, String.class, Integer.class, Integer.class, Integer.class, Integer.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: insertOrderPushBatch(Long provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, Map<Long, MasterAddressInfo> masterAddressInfoMap, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag, Integer pushFlag, Integer accordingDistancePushFlag, Integer exclusiveFlag)
     */
    @Test
    public void testInsertOrderPushBatch() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = OrderPushManagerGatewayImpl.getClass().getMethod("insertOrderPushBatch", Long.class, String.class, PushingOrderCompositeResp.class, Map<Long,.class, Date.class, Integer.class, Integer.class, String.class, Integer.class, Integer.class, Integer.class, Integer.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: buildPushNode(String note, Date pushTime)
     */
    @Test
    public void testBuildPushNode() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = OrderPushManagerGatewayImpl.getClass().getMethod("buildPushNode", String.class, Date.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: retryInsertOrderPushBatch(Long provinceNextId, String pushMold, PushingOrderCompositeResp pushOrderComposite, Map<Long, MasterAddressInfo> masterAddressInfoMap, Date pushTime, Integer pushDivisionLevel, Integer pushFrom, String techniqueTypeIds, Integer agentOrderFlag, Integer pushFlag, Integer accordingDistancePushFlag, Integer exclusiveFlag)
     */
    @Test
    public void testRetryInsertOrderPushBatch() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = OrderPushManagerGatewayImpl.getClass().getMethod("retryInsertOrderPushBatch", Long.class, String.class, PushingOrderCompositeResp.class, Map<Long,.class, Date.class, Integer.class, Integer.class, String.class, Integer.class, Integer.class, Integer.class, Integer.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

} 
