<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.wanshifu</groupId>
        <artifactId>wshifu-microservice-parent</artifactId>
        <version>2.18.Jacoco</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wanshifu</groupId>
    <artifactId>order-push-service</artifactId>
    <packaging>pom</packaging>
    <version>1.0.48-SNAPSHOT</version>
    <modules>
        <module>order-push-service-api</module>
        <module>order-push-service-web</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.plugin.version>3.6.1</maven.compiler.plugin.version>
        <maven.source.plugin.version>3.0.1</maven.source.plugin.version>
        <wanshfiu-framework-version>1.0.53</wanshfiu-framework-version>
        <sharding.version>4.1.1</sharding.version>
        <fastjson.version>1.2.83</fastjson.version>
        <apolloclient.version>1.4.0</apolloclient.version>
        <mybatis.version>1.3.5</mybatis.version>
    </properties>

    <dependencyManagement>
        <!-- json版本 不能随便调整。可能出现转换异常-->
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--公共依赖-->
            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-all-spring-boot-starter</artifactId>
                <version>${wanshfiu-framework-version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>netty-all</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--
                        这个wshifu-framework-lang包不能删除，必须依赖 wanshfiu-framework-version 版本，否则会有包冲突问题
            -->
            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-lang</artifactId>
                <version>${wanshfiu-framework-version}</version>
            </dependency>

            <!-- apollo -->
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apolloclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <!--framework test-->
            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-test</artifactId>
                <version>${wanshfiu-framework-version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>javamelody-spring-boot-starter</artifactId>
                        <groupId>net.bull.javamelody</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javamelody-core</artifactId>
                        <groupId>net.bull.javamelody</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-persistence</artifactId>
                <version>${wanshfiu-framework-version}</version>
            </dependency>

            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-core</artifactId>
                <version>${wanshfiu-framework-version}</version>
            </dependency>
        </dependencies>



    </dependencyManagement>

    <!--release版本发布 远程git仓库地址-->
    <scm>
        <connection>scm:git:********************:platform-order/wshifu-order-push-service.git</connection>
        <developerConnection>scm:git:********************:platform-order/wshifu-order-push-service.git</developerConnection>
        <url>scm:git:********************:platform-order/wshifu-order-push-service.git</url>
        <tag>HEAD</tag>
    </scm>




    <distributionManagement>
        <repository>
            <id>wanshifu-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.wanshifu.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>wanshifu-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.wanshifu.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <!-- 要将源码放上去，需要加入这个插件 -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven.source.plugin.version}</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.5</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--release版本发布插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules><!--自动更改所有子模块版本-->
                    <tagNameFormat>order-push-service-@{project.version}</tagNameFormat><!--生成的tag名字-->
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.scm</groupId>
                        <artifactId>maven-scm-provider-jgit</artifactId>
                        <version>1.9.5</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <!--取消doc生成-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9.1</version>
                <configuration>
                    <additionalparam>-Xdoclint:none</additionalparam>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>